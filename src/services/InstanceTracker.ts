/**
 * InstanceTracker.ts
 * 实例追踪系统 - 管理模板实例的生命周期和关联关系
 */

import { reactive, computed } from 'vue';
import { Template, TemplateInstance } from './TemplateManager';

/**
 * 实例状态
 */
export type InstanceStatus = 'active' | 'inactive' | 'error' | 'outdated';

/**
 * 实例事件类型
 */
export type InstanceEventType = 
  | 'created' 
  | 'updated' 
  | 'deleted' 
  | 'synced' 
  | 'conflict' 
  | 'activated' 
  | 'deactivated';

/**
 * 实例事件
 */
export interface InstanceEvent {
  id: string;
  instanceId: string;
  type: InstanceEventType;
  timestamp: Date;
  data?: any;
  description: string;
}

/**
 * 实例统计信息
 */
export interface InstanceStats {
  total: number;
  byTemplate: Record<string, number>;
  byScene: Record<string, number>;
  byStatus: Record<InstanceStatus, number>;
  recentEvents: InstanceEvent[];
}

/**
 * 实例查询选项
 */
export interface InstanceQueryOptions {
  templateId?: string;
  sceneId?: string;
  nodeId?: string;
  status?: InstanceStatus;
  syncStatus?: 'synced' | 'outdated' | 'conflict';
  limit?: number;
  offset?: number;
}

/**
 * 实例追踪系统
 */
export class InstanceTracker {
  private static instance: InstanceTracker;
  
  // 实例存储
  private instances = reactive<Map<string, TemplateInstance>>(new Map());
  
  // 实例状态映射
  private instanceStatus = reactive<Map<string, InstanceStatus>>(new Map());
  
  // 事件历史
  private events = reactive<InstanceEvent[]>([]);
  
  // 模板到实例的映射
  private templateInstances = reactive<Map<string, Set<string>>>(new Map());
  
  // 场景到实例的映射
  private sceneInstances = reactive<Map<string, Set<string>>>(new Map());
  
  // 事件监听器
  private eventListeners: Map<InstanceEventType, ((event: InstanceEvent) => void)[]> = new Map();

  private constructor() {
    this.initializeEventTypes();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): InstanceTracker {
    if (!InstanceTracker.instance) {
      InstanceTracker.instance = new InstanceTracker();
    }
    return InstanceTracker.instance;
  }

  /**
   * 注册实例
   */
  registerInstance(instance: TemplateInstance): void {
    console.log(`📝 注册实例: ${instance.id}`);
    
    // 存储实例
    this.instances.set(instance.id, instance);
    
    // 设置初始状态
    this.instanceStatus.set(instance.id, 'active');
    
    // 更新映射关系
    this.updateTemplateMapping(instance.templateId, instance.id, 'add');
    this.updateSceneMapping(instance.sceneId, instance.id, 'add');
    
    // 记录事件
    this.recordEvent({
      instanceId: instance.id,
      type: 'created',
      description: `实例 ${instance.id} 已创建`
    });
    
    console.log(`✅ 实例注册完成: ${instance.id}`);
  }

  /**
   * 注销实例
   */
  unregisterInstance(instanceId: string): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    console.log(`🗑️ 注销实例: ${instanceId}`);
    
    // 移除实例
    this.instances.delete(instanceId);
    this.instanceStatus.delete(instanceId);
    
    // 更新映射关系
    this.updateTemplateMapping(instance.templateId, instanceId, 'remove');
    this.updateSceneMapping(instance.sceneId, instanceId, 'remove');
    
    // 记录事件
    this.recordEvent({
      instanceId,
      type: 'deleted',
      description: `实例 ${instanceId} 已删除`
    });
    
    console.log(`✅ 实例注销完成: ${instanceId}`);
    return true;
  }

  /**
   * 更新实例
   */
  updateInstance(instanceId: string, updates: Partial<TemplateInstance>): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    console.log(`📝 更新实例: ${instanceId}`);
    
    // 应用更新
    Object.assign(instance, updates);
    instance.lastSyncAt = new Date();
    
    // 记录事件
    this.recordEvent({
      instanceId,
      type: 'updated',
      description: `实例 ${instanceId} 已更新`,
      data: updates
    });
    
    console.log(`✅ 实例更新完成: ${instanceId}`);
    return true;
  }

  /**
   * 获取实例
   */
  getInstance(instanceId: string): TemplateInstance | undefined {
    return this.instances.get(instanceId);
  }

  /**
   * 查询实例
   */
  queryInstances(options: InstanceQueryOptions = {}): TemplateInstance[] {
    let results = Array.from(this.instances.values());
    
    // 按模板ID过滤
    if (options.templateId) {
      results = results.filter(instance => instance.templateId === options.templateId);
    }
    
    // 按场景ID过滤
    if (options.sceneId) {
      results = results.filter(instance => instance.sceneId === options.sceneId);
    }
    
    // 按节点ID过滤
    if (options.nodeId) {
      results = results.filter(instance => instance.nodeId === options.nodeId);
    }
    
    // 按同步状态过滤
    if (options.syncStatus) {
      results = results.filter(instance => instance.syncStatus === options.syncStatus);
    }
    
    // 按实例状态过滤
    if (options.status) {
      results = results.filter(instance => 
        this.instanceStatus.get(instance.id) === options.status
      );
    }
    
    // 分页
    if (options.offset) {
      results = results.slice(options.offset);
    }
    
    if (options.limit) {
      results = results.slice(0, options.limit);
    }
    
    return results;
  }

  /**
   * 获取模板的所有实例
   */
  getTemplateInstances(templateId: string): TemplateInstance[] {
    const instanceIds = this.templateInstances.get(templateId);
    if (!instanceIds) return [];
    
    return Array.from(instanceIds)
      .map(id => this.instances.get(id))
      .filter(Boolean) as TemplateInstance[];
  }

  /**
   * 获取场景的所有实例
   */
  getSceneInstances(sceneId: string): TemplateInstance[] {
    const instanceIds = this.sceneInstances.get(sceneId);
    if (!instanceIds) return [];
    
    return Array.from(instanceIds)
      .map(id => this.instances.get(id))
      .filter(Boolean) as TemplateInstance[];
  }

  /**
   * 设置实例状态
   */
  setInstanceStatus(instanceId: string, status: InstanceStatus): void {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return;
    }

    const oldStatus = this.instanceStatus.get(instanceId);
    this.instanceStatus.set(instanceId, status);
    
    console.log(`📊 实例状态变更: ${instanceId} ${oldStatus} -> ${status}`);
    
    // 记录事件
    const eventType = status === 'active' ? 'activated' : 'deactivated';
    this.recordEvent({
      instanceId,
      type: eventType,
      description: `实例 ${instanceId} 状态变更为 ${status}`,
      data: { oldStatus, newStatus: status }
    });
  }

  /**
   * 获取实例状态
   */
  getInstanceStatus(instanceId: string): InstanceStatus | undefined {
    return this.instanceStatus.get(instanceId);
  }

  /**
   * 同步实例
   */
  syncInstance(instanceId: string, templateVersion?: string): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    console.log(`🔄 同步实例: ${instanceId}`);
    
    // 更新同步信息
    instance.syncStatus = 'synced';
    instance.lastSyncAt = new Date();
    
    if (templateVersion) {
      instance.templateVersion = templateVersion;
    }
    
    // 记录事件
    this.recordEvent({
      instanceId,
      type: 'synced',
      description: `实例 ${instanceId} 已同步`,
      data: { templateVersion }
    });
    
    console.log(`✅ 实例同步完成: ${instanceId}`);
    return true;
  }

  /**
   * 标记实例为过期
   */
  markInstanceOutdated(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (!instance) return;
    
    instance.syncStatus = 'outdated';
    this.setInstanceStatus(instanceId, 'outdated');
    
    console.log(`⚠️ 实例已标记为过期: ${instanceId}`);
  }

  /**
   * 批量同步模板的所有实例
   */
  syncTemplateInstances(templateId: string, templateVersion: string): number {
    const instances = this.getTemplateInstances(templateId);
    let syncedCount = 0;
    
    console.log(`🔄 批量同步模板实例: ${templateId} (${instances.length} 个实例)`);
    
    instances.forEach(instance => {
      if (this.syncInstance(instance.id, templateVersion)) {
        syncedCount++;
      }
    });
    
    console.log(`✅ 批量同步完成: ${syncedCount}/${instances.length} 个实例已同步`);
    return syncedCount;
  }

  /**
   * 获取统计信息
   */
  getStats(): InstanceStats {
    const stats: InstanceStats = {
      total: this.instances.size,
      byTemplate: {},
      byScene: {},
      byStatus: {
        active: 0,
        inactive: 0,
        error: 0,
        outdated: 0
      },
      recentEvents: this.events.slice(-10)
    };
    
    // 统计按模板分布
    this.templateInstances.forEach((instanceIds, templateId) => {
      stats.byTemplate[templateId] = instanceIds.size;
    });
    
    // 统计按场景分布
    this.sceneInstances.forEach((instanceIds, sceneId) => {
      stats.byScene[sceneId] = instanceIds.size;
    });
    
    // 统计按状态分布
    this.instanceStatus.forEach(status => {
      stats.byStatus[status]++;
    });
    
    return stats;
  }

  /**
   * 监听事件
   */
  addEventListener(type: InstanceEventType, listener: (event: InstanceEvent) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: InstanceEventType, listener: (event: InstanceEvent) => void): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 清理过期事件
   */
  cleanupEvents(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = new Date(Date.now() - maxAge);
    const originalLength = this.events.length;
    
    this.events.splice(0, this.events.findIndex(event => event.timestamp > cutoff));
    
    const cleaned = originalLength - this.events.length;
    if (cleaned > 0) {
      console.log(`🧹 清理了 ${cleaned} 个过期事件`);
    }
  }

  /**
   * 初始化事件类型
   */
  private initializeEventTypes(): void {
    const eventTypes: InstanceEventType[] = [
      'created', 'updated', 'deleted', 'synced', 'conflict', 'activated', 'deactivated'
    ];
    
    eventTypes.forEach(type => {
      this.eventListeners.set(type, []);
    });
  }

  /**
   * 记录事件
   */
  private recordEvent(eventData: Omit<InstanceEvent, 'id' | 'timestamp'>): void {
    const event: InstanceEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...eventData
    };
    
    this.events.push(event);
    
    // 触发事件监听器
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('事件监听器执行失败:', error);
        }
      });
    }
    
    // 限制事件历史长度
    if (this.events.length > 1000) {
      this.events.splice(0, 100);
    }
  }

  /**
   * 更新模板映射
   */
  private updateTemplateMapping(templateId: string, instanceId: string, action: 'add' | 'remove'): void {
    if (!this.templateInstances.has(templateId)) {
      this.templateInstances.set(templateId, new Set());
    }
    
    const instanceSet = this.templateInstances.get(templateId)!;
    
    if (action === 'add') {
      instanceSet.add(instanceId);
    } else {
      instanceSet.delete(instanceId);
      
      // 如果集合为空，删除映射
      if (instanceSet.size === 0) {
        this.templateInstances.delete(templateId);
      }
    }
  }

  /**
   * 更新场景映射
   */
  private updateSceneMapping(sceneId: string, instanceId: string, action: 'add' | 'remove'): void {
    if (!this.sceneInstances.has(sceneId)) {
      this.sceneInstances.set(sceneId, new Set());
    }
    
    const instanceSet = this.sceneInstances.get(sceneId)!;
    
    if (action === 'add') {
      instanceSet.add(instanceId);
    } else {
      instanceSet.delete(instanceId);
      
      // 如果集合为空，删除映射
      if (instanceSet.size === 0) {
        this.sceneInstances.delete(sceneId);
      }
    }
  }

  /**
   * 计算属性：活跃实例数量
   */
  get activeInstanceCount(): number {
    return Array.from(this.instanceStatus.values()).filter(status => status === 'active').length;
  }

  /**
   * 计算属性：过期实例数量
   */
  get outdatedInstanceCount(): number {
    return Array.from(this.instanceStatus.values()).filter(status => status === 'outdated').length;
  }
}

// 导出单例实例
export const instanceTracker = InstanceTracker.getInstance();
