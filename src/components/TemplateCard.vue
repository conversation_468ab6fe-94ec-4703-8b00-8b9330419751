<template>
  <div
    :class="[
      'template-card',
      `view-${viewMode}`,
      { 'is-selected': isSelected, 'is-dragging': isDragging }
    ]"
    :draggable="true"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @click="$emit('select', template)"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="template-icon">
        {{ getTypeIcon(template.type) }}
      </div>
      
      <div class="template-info">
        <h3 class="template-name" :title="template.name">
          {{ template.name }}
        </h3>
        <div class="template-meta">
          <span class="template-type">{{ getTypeDisplayName(template.type) }}</span>
          <span class="template-category">{{ template.metadata.category }}</span>
        </div>
      </div>
      
      <div class="card-actions">
        <button
          @click.stop="$emit('edit', template)"
          class="action-btn edit-btn"
          title="编辑模板"
        >
          ✏️
        </button>
        <button
          @click.stop="showMenu = !showMenu"
          class="action-btn menu-btn"
          title="更多操作"
        >
          ⋮
        </button>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <div v-if="template.description" class="template-description">
        {{ template.description }}
      </div>
      
      <!-- 模板预览 -->
      <div class="template-preview">
        <div v-if="template.metadata.preview" class="preview-image">
          <img :src="template.metadata.preview" :alt="template.name" />
        </div>
        <div v-else class="preview-placeholder">
          <span class="preview-icon">{{ getTypeIcon(template.type) }}</span>
          <span class="preview-text">{{ getTypeDisplayName(template.type) }}</span>
        </div>
      </div>
      
      <!-- 模板标签 -->
      <div v-if="template.metadata.tags.length > 0" class="template-tags">
        <span
          v-for="tag in template.metadata.tags.slice(0, 3)"
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </span>
        <span v-if="template.metadata.tags.length > 3" class="tag-more">
          +{{ template.metadata.tags.length - 3 }}
        </span>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="template-stats">
        <div class="stat-item">
          <span class="stat-icon">📊</span>
          <span class="stat-value">{{ template.metadata.usageCount || 0 }}</span>
          <span class="stat-label">使用</span>
        </div>
        
        <div class="stat-item">
          <span class="stat-icon">📅</span>
          <span class="stat-value">{{ formatDate(template.metadata.updatedAt) }}</span>
          <span class="stat-label">更新</span>
        </div>
        
        <div v-if="instanceCount > 0" class="stat-item">
          <span class="stat-icon">🔗</span>
          <span class="stat-value">{{ instanceCount }}</span>
          <span class="stat-label">实例</span>
        </div>
      </div>
      
      <div class="template-status">
        <div
          v-if="template.metadata.isBuiltIn"
          class="status-badge builtin"
          title="内置模板"
        >
          内置
        </div>
        <div
          v-if="hasConflicts"
          class="status-badge conflict"
          title="存在参数冲突"
        >
          冲突
        </div>
        <div
          v-if="isOutdated"
          class="status-badge outdated"
          title="有实例需要同步"
        >
          待同步
        </div>
      </div>
    </div>

    <!-- 操作菜单 -->
    <div v-if="showMenu" class="action-menu" @click.stop>
      <div class="menu-overlay" @click="showMenu = false"></div>
      <div class="menu-content">
        <button @click="handleEdit" class="menu-item">
          <span class="menu-icon">✏️</span>
          编辑模板
        </button>
        <button @click="handleDuplicate" class="menu-item">
          <span class="menu-icon">📋</span>
          复制模板
        </button>
        <button @click="handleExport" class="menu-item">
          <span class="menu-icon">📤</span>
          导出模板
        </button>
        <div class="menu-divider"></div>
        <button @click="handleViewInstances" class="menu-item">
          <span class="menu-icon">🔗</span>
          查看实例 ({{ instanceCount }})
        </button>
        <button @click="handleSyncInstances" class="menu-item">
          <span class="menu-icon">🔄</span>
          同步实例
        </button>
        <div class="menu-divider"></div>
        <button @click="handleDelete" class="menu-item danger">
          <span class="menu-icon">🗑️</span>
          删除模板
        </button>
      </div>
    </div>

    <!-- 拖拽预览 -->
    <div v-if="isDragging" class="drag-preview">
      <div class="preview-content">
        <span class="preview-icon">{{ getTypeIcon(template.type) }}</span>
        <span class="preview-name">{{ template.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Template, TemplateType } from '../types/TemplateTypes';
import { TemplateManager } from '../services/TemplateManager';

// Props
interface Props {
  template: Template;
  viewMode: 'grid' | 'list';
  isSelected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false
});

// Emits
const emit = defineEmits<{
  select: [template: Template];
  edit: [template: Template];
  delete: [template: Template];
  duplicate: [template: Template];
  dragStart: [template: Template, event: DragEvent];
}>();

// 状态
const showMenu = ref(false);
const isDragging = ref(false);

// 服务
const templateManager = TemplateManager.getInstance();

// 计算属性
const instanceCount = computed(() => {
  const instances = templateManager.instanceTracker.getTemplateInstances(props.template.id);
  return instances.length;
});

const hasConflicts = computed(() => {
  const conflicts = templateManager.getParameterConflicts(props.template.id);
  return conflicts.length > 0;
});

const isOutdated = computed(() => {
  const instances = templateManager.instanceTracker.getTemplateInstances(props.template.id);
  return instances.some(instance => instance.syncStatus === 'outdated' || instance.syncStatus === 'pending');
});

// 方法
const getTypeIcon = (type: TemplateType): string => {
  const icons: Record<TemplateType, string> = {
    [TemplateType.MESH]: '🧊',
    [TemplateType.STYLE]: '🎨',
    [TemplateType.CAMERA]: '📷',
    [TemplateType.ACTION]: '⚡',
    [TemplateType.LABEL]: '🏷️',
    [TemplateType.POSITION]: '📍',
    [TemplateType.INTERACTION]: '👆',
    [TemplateType.ENVIRONMENT]: '🌍',
    [TemplateType.CALLBACK]: '🔗'
  };
  return icons[type] || '📦';
};

const getTypeDisplayName = (type: TemplateType): string => {
  const names: Record<TemplateType, string> = {
    [TemplateType.MESH]: '网格',
    [TemplateType.STYLE]: '样式',
    [TemplateType.CAMERA]: '相机',
    [TemplateType.ACTION]: '动作',
    [TemplateType.LABEL]: '标签',
    [TemplateType.POSITION]: '位置',
    [TemplateType.INTERACTION]: '交互',
    [TemplateType.ENVIRONMENT]: '环境',
    [TemplateType.CALLBACK]: '回调'
  };
  return names[type] || type;
};

const formatDate = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - new Date(date).getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) return '今天';
  if (days === 1) return '昨天';
  if (days < 7) return `${days}天前`;
  if (days < 30) return `${Math.floor(days / 7)}周前`;
  if (days < 365) return `${Math.floor(days / 30)}月前`;
  return `${Math.floor(days / 365)}年前`;
};

const handleDragStart = (event: DragEvent) => {
  isDragging.value = true;
  emit('dragStart', props.template, event);
};

const handleDragEnd = () => {
  isDragging.value = false;
};

const handleEdit = () => {
  showMenu.value = false;
  emit('edit', props.template);
};

const handleDuplicate = () => {
  showMenu.value = false;
  emit('duplicate', props.template);
};

const handleDelete = () => {
  showMenu.value = false;
  emit('delete', props.template);
};

const handleExport = () => {
  showMenu.value = false;
  // 导出模板逻辑
  const config = templateManager.configExporter.exportTemplate(props.template);
  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${props.template.name}.json`;
  a.click();
  URL.revokeObjectURL(url);
  
  console.log(`📤 模板已导出: ${props.template.name}`);
};

const handleViewInstances = () => {
  showMenu.value = false;
  // 查看实例逻辑
  const instances = templateManager.instanceTracker.getTemplateInstances(props.template.id);
  console.log(`🔗 模板实例 (${instances.length} 个):`, instances);
};

const handleSyncInstances = async () => {
  showMenu.value = false;
  // 同步实例逻辑
  try {
    const result = await templateManager.syncTemplateParameters(props.template.id);
    if (result.success) {
      console.log(`✅ 模板实例同步完成: ${props.template.name}`);
    } else {
      console.error(`❌ 模板实例同步失败: ${result.errors?.join(', ')}`);
    }
  } catch (error) {
    console.error('模板实例同步失败:', error);
  }
};
</script>

<style scoped>
.template-card {
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.template-card:hover {
  border-color: #63b3ed;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.template-card.is-selected {
  border-color: #38a169;
  box-shadow: 0 0 0 2px rgba(56, 161, 105, 0.3);
}

.template-card.is-dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* 网格视图样式 */
.template-card.view-grid {
  display: flex;
  flex-direction: column;
  height: 320px;
}

.template-card.view-grid .card-header {
  padding: 1rem;
  border-bottom: 1px solid #4a5568;
}

.template-card.view-grid .card-content {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.template-card.view-grid .card-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid #4a5568;
}

/* 列表视图样式 */
.template-card.view-list {
  display: flex;
  align-items: center;
  height: 80px;
}

.template-card.view-list .card-header {
  flex: 1;
  padding: 1rem;
  display: flex;
  align-items: center;
}

.template-card.view-list .card-content {
  flex: 2;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.template-card.view-list .card-footer {
  flex: 1;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.template-card.view-list .template-preview {
  display: none;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.template-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4a5568;
  border-radius: 8px;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  margin: 0 0 0.25rem 0;
  color: #f7fafc;
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-meta {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.template-type {
  color: #63b3ed;
  background: rgba(99, 179, 237, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.template-category {
  color: #9ae6b4;
  background: rgba(154, 230, 180, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.card-actions {
  display: flex;
  gap: 0.25rem;
}

.action-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid #4a5568;
  border-radius: 4px;
  color: #a0aec0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

/* 卡片内容 */
.template-description {
  color: #a0aec0;
  font-size: 0.875rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-preview {
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background: #1a202c;
  border: 1px solid #4a5568;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #718096;
}

.preview-icon {
  font-size: 2rem;
}

.preview-text {
  font-size: 0.75rem;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
}

.tag {
  background: #4a5568;
  color: #e2e8f0;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.tag-more {
  background: #718096;
  color: #f7fafc;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #1a202c;
}

.template-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #a0aec0;
}

.stat-icon {
  font-size: 0.875rem;
}

.stat-value {
  color: #e2e8f0;
  font-weight: 500;
}

.template-status {
  display: flex;
  gap: 0.375rem;
}

.status-badge {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.625rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.builtin {
  background: #2d5016;
  color: #9ae6b4;
}

.status-badge.conflict {
  background: #742a2a;
  color: #fc8181;
}

.status-badge.outdated {
  background: #744210;
  color: #f6ad55;
}

/* 操作菜单 */
.action-menu {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

.menu-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.menu-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  min-width: 180px;
  overflow: hidden;
}

.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  color: #e2e8f0;
  cursor: pointer;
  font-size: 0.875rem;
  text-align: left;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background: #4a5568;
}

.menu-item.danger {
  color: #fc8181;
}

.menu-item.danger:hover {
  background: #742a2a;
}

.menu-icon {
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

.menu-divider {
  height: 1px;
  background: #4a5568;
  margin: 0.25rem 0;
}

/* 拖拽预览 */
.drag-preview {
  position: fixed;
  top: -1000px;
  left: -1000px;
  background: #2d3748;
  color: #f7fafc;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #4a5568;
  font-size: 0.875rem;
  pointer-events: none;
  z-index: 9999;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.preview-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-card.view-grid {
    height: auto;
    min-height: 280px;
  }
  
  .template-card.view-list {
    flex-direction: column;
    height: auto;
    align-items: stretch;
  }
  
  .template-card.view-list .card-header,
  .template-card.view-list .card-content,
  .template-card.view-list .card-footer {
    flex: none;
  }
  
  .template-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
