/**
 * TemplateTypes.ts
 * 模板系统的核心类型定义
 */

// 模板管理器结果类型
export type TemplateManagerResult<T = any> = {
  success: boolean;
  data?: T;
  errors?: string[];
};

// 模板类型枚举
export enum TemplateType {
  MESH = 'mesh',
  STYLE = 'style',
  CAMERA = 'camera',
  ACTION = 'action',
  LABEL = 'label',
  POSITION = 'position',
  INTERACTION = 'interaction',
  ENVIRONMENT = 'environment',
  CALLBACK = 'callback'
}

// 模板参数接口
export interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
  defaultValue?: any;
  required?: boolean;
  options?: any[]; // 向后兼容
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: any[];
  };
}

// 模板定义接口
export interface TemplateDefinition {
  type: TemplateType;
  parameters: TemplateParameter[];
  dependencies?: string[];
  content: any;
  // 向后兼容的属性
  nodeType?: string;
  properties?: Record<string, any>;
  configPath?: string;
  metadata?: {
    version?: string;
    author?: string;
    description?: string;
    tags?: string[];
  };
}

// 模板接口
export interface Template {
  id: string;
  name: string;
  description?: string;
  type: TemplateType;
  version: string;
  definition?: TemplateDefinition;
  // 向后兼容的属性
  data?: any;
  path?: string;
  versionHistory?: any[];
  metadata: {
    category: string;
    tags: string[];
    author?: string;
    createdAt: Date;
    updatedAt: Date;
    dependencies: string[];
    usageCount: number;
    isBuiltIn: boolean;
    // 向后兼容的属性
    usage?: number;
    preview?: string;
    sourceType?: 'user-created' | 'config-imported' | 'node-converted';
  };
}

// 模板引用接口
export interface TemplateReference {
  $ref: string;
  version?: string;
  parameters?: Record<string, any>;
  // 向后兼容的属性
  $extend?: Record<string, any>;
  $override?: Record<string, any>;
}

// 配置引用接口
export interface ConfigReference {
  path: string;
  version?: string;
  sceneId?: string;
  // 向后兼容的属性
  resolvedValue?: any;
  dependencies?: string[];
}

// 模板实例接口
export interface TemplateInstance {
  id: string;
  templateId: string;
  sceneId: string;
  nodeId?: string;
  position: { x: number; y: number; z?: number };
  overrides: Record<string, any>;
  status: 'active' | 'inactive' | 'error' | 'outdated';
  syncStatus: 'synced' | 'pending' | 'conflict' | 'failed' | 'outdated';
  createdAt: Date;
  lastSyncAt: Date;
  // 向后兼容的属性
  templateVersion?: string;
  configRef?: {
    path: string;
    isReference: boolean;
  };
  metadata?: {
    version?: string;
    source?: 'drag_drop' | 'api' | 'import';
    tags?: string[];
  };
}

// 实例化选项接口
export interface InstantiationOptions {
  position: { x: number; y: number; z?: number };
  size?: { width: number; height: number; depth?: number };
  sceneId?: string;
  parameterOverrides?: Record<string, any>;
  resolveDependencies?: boolean;
  validateParameters?: boolean;
  metadata?: {
    source?: 'drag_drop' | 'api' | 'import';
    tags?: string[];
  };
}

// 实例化结果接口
export interface InstantiationResult {
  node: any; // X6 Cell 或其他节点类型
  instance: TemplateInstance;
  resolvedDependencies?: string[];
  warnings?: string[];
}

// 模板使用统计接口
export interface TemplateUsageStats {
  totalTemplates: number;
  byType: Record<TemplateType, number>;
  byCategory: Record<string, number>;
  totalUsage: number;
  mostUsed: Array<{ templateId: string; count: number }>;
  recentlyCreated: Array<{ templateId: string; createdAt: Date }>;
}

// 实例统计接口
export interface InstanceStats {
  total: number;
  byTemplate: Record<string, number>;
  byScene: Record<string, number>;
  byStatus: Record<string, number>;
  bySyncStatus: Record<string, number>;
  averageAge: number;
  oldestInstance?: Date;
  newestInstance?: Date;
}

// 配置格式接口
export interface ConfigFormat {
  version: string;
  metadata?: {
    name?: string;
    description?: string;
    author?: string;
    createdAt?: string;
    tags?: string[];
  };
  templates?: {
    [sceneId: string]: {
      [templateType: string]: {
        [templateName: string]: any;
      };
    };
  };
  scenes?: {
    [sceneId: string]: {
      [instanceId: string]: {
        templateId: string;
        position: { x: number; y: number; z?: number };
        overrides?: Record<string, any>;
        metadata?: any;
      };
    };
  };
}

// 导入选项接口
export interface ImportOptions {
  mergeStrategy?: 'replace' | 'merge' | 'skip';
  validateReferences?: boolean;
  resolveConflicts?: boolean;
  preserveIds?: boolean;
  targetScene?: string;
}

// 导出选项接口
export interface ExportOptions {
  includeInstances?: boolean;
  includeMetadata?: boolean;
  minifyOutput?: boolean;
  format?: 'json' | 'js';
  sceneFilter?: string[];
  templateFilter?: string[];
}

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: Array<{
    path: string;
    message: string;
    severity: 'error' | 'warning' | 'info';
  }>;
  warnings: Array<{
    path: string;
    message: string;
    suggestion?: string;
  }>;
}

// 解析结果接口
export interface ResolveResult<T = any> {
  resolved: T;
  dependencies: string[];
  unresolvedRefs: string[];
  circularRefs: string[];
}

// 拖拽数据接口
export interface DragData {
  template: Template;
  source: 'template_panel' | 'library' | 'external';
  metadata?: any;
}

// 拖拽事件接口
export interface DragEvent {
  type: 'start' | 'move' | 'drop' | 'cancel';
  data: DragData;
  position: { x: number; y: number };
  target?: HTMLElement;
  timestamp: Date;
}

// 放置目标接口
export interface DropTarget {
  element: HTMLElement;
  accepts: (data: DragData) => boolean;
  onDrop: (data: DragData, position: { x: number; y: number }) => void;
  onDragOver?: (data: DragData, position: { x: number; y: number }) => void;
  onDragLeave?: () => void;
}

// 节点信息接口
export interface NodeInfo {
  id: string;
  type: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  properties: Record<string, any>;
  style?: Record<string, any>;
  data?: any;
}

// 节点选择结果接口
export interface NodeSelectionResult {
  nodes: NodeInfo[];
  primaryNode?: NodeInfo;
  selectionType: 'single' | 'multiple' | 'area';
  timestamp: Date;
}

// 模板创建选项接口
export interface TemplateCreationOptions {
  name: string;
  description?: string;
  category?: string;
  tags?: string[];
  version?: string;
  author?: string;
  extractParameters?: boolean;
  includeDependencies?: boolean;
  metadata?: Record<string, any>;
}

// 批量操作选项接口
export interface BatchOperationOptions {
  batchSize?: number;
  maxConcurrency?: number;
  retryAttempts?: number;
  retryDelay?: number;
  continueOnError?: boolean;
  progressCallback?: (progress: number, current: number, total: number) => void;
}

// 搜索选项接口
export interface SearchOptions {
  query?: string;
  type?: TemplateType | TemplateType[];
  category?: string | string[];
  tags?: string | string[];
  author?: string;
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'usageCount';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// 搜索结果接口
export interface SearchResult<T = Template> {
  items: T[];
  total: number;
  hasMore: boolean;
  facets?: {
    types: Record<TemplateType, number>;
    categories: Record<string, number>;
    tags: Record<string, number>;
    authors: Record<string, number>;
  };
}
