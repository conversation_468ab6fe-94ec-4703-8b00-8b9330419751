/**
 * ConfigImporter.ts
 * 负责从config.js导入模板和解析$ref引用
 */

import { TemplateType, Template, TemplateReference, ConfigReference } from '../types/TemplateTypes';

/**
 * 配置类型映射
 */
const CONFIG_TYPE_MAPPING: Record<string, TemplateType> = {
  'styles': TemplateType.STYLE,
  'meshes': TemplateType.MESH,
  'cameras': TemplateType.CAMERA,
  'actions': TemplateType.ACTION,
  'labels': TemplateType.LABEL,
  'positions': TemplateType.POSITION,
  'interactions': TemplateType.INTERACTION,
  'environments': TemplateType.ENVIRONMENT,
  'resetCallbacks': TemplateType.CALLBACK
};

/**
 * 配置导入器类
 */
export class ConfigImporter {
  // 存储解析后的引用
  private resolvedReferences: Map<string, ConfigReference> = new Map();
  
  // 存储循环引用检测的路径栈
  private referenceStack: string[] = [];
  
  /**
   * 从config.js导入模板
   * @param config 配置对象
   * @returns 导入的模板数组
   */
  public importTemplates(config: any): Partial<Template>[] {
    const templates: Partial<Template>[] = [];
    
    // 检查配置是否有templates节点
    if (!config || !config.templates) {
      console.warn('配置中没有templates节点');
      return templates;
    }
    
    // 遍历场景
    for (const sceneId in config.templates) {
      const sceneTemplates = config.templates[sceneId];
      
      // 遍历模板类型
      for (const typeKey in sceneTemplates) {
        // 检查是否是支持的模板类型
        if (!(typeKey in CONFIG_TYPE_MAPPING)) {
          console.warn(`未知的模板类型: ${typeKey}`);
          continue;
        }
        
        const templateType = CONFIG_TYPE_MAPPING[typeKey];
        const templateGroup = sceneTemplates[typeKey];
        
        // 遍历该类型下的所有模板
        for (const templateName in templateGroup) {
          const templateData = templateGroup[templateName];
          const configPath = `templates.${sceneId}.${typeKey}.${templateName}`;
          
          // 创建模板对象
          const template: Partial<Template> = {
            name: templateName,
            type: templateType,
            data: templateData,
            path: configPath,
            metadata: {
              tags: this.generateTags(templateType, templateName),
              author: 'system',
              usage: 0,
              dependencies: this.extractDependencies(templateData),
              category: this.formatCategory(typeKey),
              sourceType: 'config-imported'
            },
            definition: {
              nodeType: this.getNodeTypeFromTemplateType(templateType),
              properties: templateData,
              parameters: this.extractParameters(templateData),
              dependencies: this.extractDependencies(templateData),
              configPath
            }
          };
          
          templates.push(template);
        }
      }
    }
    
    return templates;
  }
  
  /**
   * 解析$ref引用
   * @param obj 包含引用的对象
   * @param config 完整配置对象
   * @returns 解析后的对象
   */
  public resolveReferences(obj: any, config: any): any {
    // 重置引用栈
    this.referenceStack = [];
    
    // 递归解析引用
    return this.resolveReferencesRecursive(obj, config);
  }
  
  /**
   * 递归解析引用
   */
  private resolveReferencesRecursive(obj: any, config: any): any {
    // 基本类型直接返回
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    // 数组类型递归处理每个元素
    if (Array.isArray(obj)) {
      return obj.map(item => this.resolveReferencesRecursive(item, config));
    }
    
    // 处理$ref引用
    if (obj.$ref) {
      const refPath = obj.$ref;
      
      // 检测循环引用
      if (this.referenceStack.includes(refPath)) {
        console.error(`检测到循环引用: ${this.referenceStack.join(' -> ')} -> ${refPath}`);
        return { error: `循环引用: ${refPath}` };
      }
      
      // 添加到引用栈
      this.referenceStack.push(refPath);
      
      // 解析引用
      let resolved = this.resolveReference(refPath, config);
      
      // 处理$extend扩展
      if (obj.$extend) {
        resolved = this.applyExtension(resolved, obj.$extend);
      }
      
      // 从引用栈中移除
      this.referenceStack.pop();
      
      // 缓存解析结果
      this.resolvedReferences.set(refPath, {
        path: refPath,
        resolvedValue: resolved,
        dependencies: this.extractDependencies(resolved)
      });
      
      return resolved;
    }
    
    // 普通对象递归处理每个属性
    const result: any = {};
    for (const key in obj) {
      result[key] = this.resolveReferencesRecursive(obj[key], config);
    }
    
    return result;
  }
  
  /**
   * 解析单个引用
   */
  private resolveReference(path: string, config: any): any {
    // 检查缓存
    if (this.resolvedReferences.has(path)) {
      return this.resolvedReferences.get(path)!.resolvedValue;
    }
    
    // 按点分割路径
    const parts = path.split('.');
    let current = config;
    
    // 逐级查找
    for (const part of parts) {
      if (current === undefined || current === null) {
        console.error(`引用路径无效: ${path}`);
        return { error: `引用路径无效: ${path}` };
      }
      current = current[part];
    }
    
    // 递归解析引用
    return this.resolveReferencesRecursive(current, config);
  }
  
  /**
   * 应用$extend扩展
   */
  private applyExtension(base: any, extension: any): any {
    // 如果base不是对象，无法扩展
    if (base === null || typeof base !== 'object' || Array.isArray(base)) {
      return base;
    }
    
    // 创建结果对象
    const result = { ...base };
    
    // 应用扩展
    for (const key in extension) {
      // 如果两者都是对象，递归合并
      if (
        result[key] !== null && 
        typeof result[key] === 'object' && 
        !Array.isArray(result[key]) &&
        extension[key] !== null && 
        typeof extension[key] === 'object' && 
        !Array.isArray(extension[key])
      ) {
        result[key] = this.applyExtension(result[key], extension[key]);
      } else {
        // 否则直接覆盖
        result[key] = extension[key];
      }
    }
    
    return result;
  }
  
  /**
   * 提取依赖关系
   */
  private extractDependencies(obj: any): string[] {
    const dependencies: string[] = [];
    
    // 基本类型没有依赖
    if (obj === null || typeof obj !== 'object') {
      return dependencies;
    }
    
    // 数组类型递归处理每个元素
    if (Array.isArray(obj)) {
      for (const item of obj) {
        dependencies.push(...this.extractDependencies(item));
      }
      return dependencies;
    }
    
    // 处理$ref引用
    if (obj.$ref) {
      dependencies.push(obj.$ref);
    }
    
    // 递归处理对象的每个属性
    for (const key in obj) {
      if (key !== '$ref' && key !== '$extend') {
        dependencies.push(...this.extractDependencies(obj[key]));
      }
    }
    
    // 去重
    return [...new Set(dependencies)];
  }
  
  /**
   * 提取参数
   */
  private extractParameters(obj: any): any[] {
    // 简单实现，后续可以根据模板类型和属性智能提取参数
    const parameters: any[] = [];
    
    // 基本类型没有参数
    if (obj === null || typeof obj !== 'object') {
      return parameters;
    }
    
    // 遍历对象属性
    for (const key in obj) {
      // 跳过特殊属性
      if (key === '$ref' || key === '$extend') continue;
      
      // 添加参数
      parameters.push({
        name: key,
        type: this.getParameterType(obj[key]),
        description: `${key}参数`,
        defaultValue: obj[key],
        required: false
      });
    }
    
    return parameters;
  }
  
  /**
   * 获取参数类型
   */
  private getParameterType(value: any): string {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
  }
  
  /**
   * 根据模板类型生成节点类型
   */
  private getNodeTypeFromTemplateType(type: TemplateType): string {
    switch (type) {
      case TemplateType.MESH: return 'MeshNode';
      case TemplateType.STYLE: return 'StyleNode';
      case TemplateType.CAMERA: return 'CameraNode';
      case TemplateType.ACTION: return 'ActionNode';
      case TemplateType.LABEL: return 'LabelNode';
      case TemplateType.POSITION: return 'PositionNode';
      case TemplateType.INTERACTION: return 'InteractionNode';
      case TemplateType.ENVIRONMENT: return 'EnvironmentNode';
      case TemplateType.CALLBACK: return 'CallbackNode';
      default: return 'GenericNode';
    }
  }
  
  /**
   * 生成模板标签
   */
  private generateTags(type: TemplateType, name: string): string[] {
    const baseTags = [type.toString()];
    
    // 根据名称添加额外标签
    if (name.includes('building')) baseTags.push('建筑');
    if (name.includes('floor')) baseTags.push('楼层');
    if (name.includes('door')) baseTags.push('门');
    if (name.includes('device')) baseTags.push('设备');
    if (name.includes('gis')) baseTags.push('GIS');
    
    return baseTags;
  }
  
  /**
   * 格式化分类名称
   */
  private formatCategory(typeKey: string): string {
    const categoryMap: Record<string, string> = {
      'styles': '样式',
      'meshes': '网格',
      'cameras': '相机',
      'actions': '动作',
      'labels': '标签',
      'positions': '位置',
      'interactions': '交互',
      'environments': '环境',
      'resetCallbacks': '回调'
    };
    
    return categoryMap[typeKey] || '其他';
  }
}
