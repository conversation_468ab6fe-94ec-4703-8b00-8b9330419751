/**
 * 完整集成测试 - 使用真实的public/config.js进行测试
 */

import { ConfigParser } from './services/ConfigParser.js';
import { GraphBuilder } from './services/GraphBuilder.js';

/**
 * 加载public/config.js配置文件
 */
async function loadPublicConfig() {
  try {
    // 在浏览器环境中，尝试从全局变量获取配置
    if (typeof window !== 'undefined' && window.$config) {
      return window.$config;
    }
    
    // 尝试通过fetch加载配置文件
    const response = await fetch('/config.js');
    const configText = await response.text();
    
    // 解析配置文件内容
    // 这里需要处理config.js的特殊格式
    const configMatch = configText.match(/window\.\$config\s*=\s*({[\s\S]*});/);
    if (configMatch) {
      const configStr = configMatch[1];
      // 使用Function构造器安全地解析配置
      const config = new Function('return ' + configStr)();
      return config;
    }
    
    throw new Error('无法解析config.js文件');
    
  } catch (error) {
    console.error('加载配置文件失败:', error);
    
    // 返回一个简化的测试配置
    return {
      scenes: {
        "integration-test": {
          name: "集成测试场景",
          models: ["test-model.glb"],
          scene: "DefaultScene",
          envTemplate: {
            $ref: "templates.1.environments.techStyle"
          },
          camera: {
            $ref: "templates.1.cameras.building"
          },
          actions: [
            {
              actionType: "doubleClick",
              meshNames: { $ref: "templates.1.meshes.buildingLevels" },
              config: {
                highlight: {
                  color: [1, 0.5, 0],
                  duration: 800,
                  intensity: 1.5
                },
                callbacks: { $ref: "templates.1.resetCallbacks.3" }
              }
            },
            {
              actionType: "rightDoubleClick",
              meshNames: { $ref: "templates.1.meshes.buildingLevels" },
              config: {
                callbacks: { $ref: "templates.1.resetCallbacks.3" }
              }
            }
          ],
          lifecycle: {
            onActivated: [
              {
                trigger: "immediate",
                callback: "DeviceService.setupDataDrivenPolling",
                parameters: {
                  dataSource: {
                    type: "polling",
                    interval: 5000,
                    url: "http://localhost:4523/m1/4523-0-default/api/nanao/device/list",
                    method: "get",
                    params: {},
                    headers: {
                      "Content-Type": "application/json"
                    },
                    transforms: []
                  },
                  mappings: "public/config/mappings/nanao-lszsc-device-mapping.csv",
                  transforms: [
                    {
                      type: "map",
                      match: { pointType: "digital" },
                      input: "pointValue",
                      output: "meshValue",
                      mapping: { "0": false, "1": true }
                    }
                  ],
                  callback: "AnimationService.toggleModelVisibility",
                  parameters: {
                    meshName: "{{meshName}}",
                    isVisible: "{{meshValue}}"
                  }
                }
              }
            ]
          },
          staticLabels: [
            {
              $ref: "templates.1.labels.floorLabel",
              $extend: {
                meshNames: ["NA_LSZSC_QITA03"],
                customNames: { "NA_LSZSC_QITA03": "其他区域" },
                click: {
                  enabled: true,
                  highlight: {
                    color: [0, 1, 0],
                    duration: 1000
                  },
                  callback: "CameraService.moveCamera",
                  parameters: {
                    position: [50, 30, 50],
                    target: [0, 0, 0],
                    duration: 2000
                  }
                }
              }
            }
          ]
        }
      },
      defaultScene: "integration-test"
    };
  }
}

/**
 * 执行完整的集成测试
 */
async function runFullIntegrationTest() {
  console.log("🚀 开始完整集成测试...");
  
  try {
    // 1. 加载配置文件
    console.log("📁 加载配置文件...");
    const config = await loadPublicConfig();
    
    if (!config || !config.scenes) {
      throw new Error("配置文件格式不正确或为空");
    }
    
    const sceneIds = Object.keys(config.scenes);
    console.log(`✅ 成功加载配置文件，包含 ${sceneIds.length} 个场景:`, sceneIds);
    
    // 2. 测试每个场景的解析
    const parser = new ConfigParser();
    const allParsedScenes = {};
    
    for (const sceneId of sceneIds) {
      console.log(`\n🔍 解析场景: ${sceneId}`);
      const sceneConfig = config.scenes[sceneId];
      
      try {
        const parsedData = parser.parseScene(sceneId, sceneConfig);
        allParsedScenes[sceneId] = parsedData;
        
        // 输出解析统计
        console.log(`  📊 解析统计:`);
        console.log(`    - 场景配置节点: 1`);
        console.log(`    - 事件节点: ${parsedData.eventNodes.length}`);
        console.log(`    - 动作节点: ${parsedData.actionNodes.length}`);
        console.log(`    - 生命周期节点: ${parsedData.lifecycleNodes.length}`);
        console.log(`    - 静态标签节点: ${parsedData.staticLabelNodes.length}`);
        console.log(`    - 引用节点: ${parsedData.referenceNodes.length}`);
        console.log(`    - 数据节点: ${parsedData.dataNodes.length}`);
        console.log(`    - 连接: ${parsedData.connections.length}`);
        
        // 验证节点数据完整性
        const allNodes = [
          parsedData.sceneConfig,
          ...parsedData.eventNodes,
          ...parsedData.actionNodes,
          ...parsedData.lifecycleNodes,
          ...parsedData.staticLabelNodes,
          ...parsedData.referenceNodes,
          ...parsedData.dataNodes
        ];
        
        const nodesWithoutId = allNodes.filter(node => !node.id);
        const nodesWithoutType = allNodes.filter(node => !node.nodeType);
        const nodesWithoutDisplay = allNodes.filter(node => !node.displayName);
        
        if (nodesWithoutId.length > 0) {
          console.warn(`    ⚠️  ${nodesWithoutId.length} 个节点缺少ID`);
        }
        if (nodesWithoutType.length > 0) {
          console.warn(`    ⚠️  ${nodesWithoutType.length} 个节点缺少类型`);
        }
        if (nodesWithoutDisplay.length > 0) {
          console.warn(`    ⚠️  ${nodesWithoutDisplay.length} 个节点缺少显示名称`);
        }
        
        // 验证连接有效性
        const allNodeIds = allNodes.map(node => node.id);
        const invalidConnections = parsedData.connections.filter(conn => 
          !allNodeIds.includes(conn.source) || !allNodeIds.includes(conn.target)
        );
        
        if (invalidConnections.length > 0) {
          console.warn(`    ⚠️  ${invalidConnections.length} 个无效连接`);
          invalidConnections.forEach(conn => {
            console.warn(`      ${conn.source} -> ${conn.target}`);
          });
        } else {
          console.log(`    ✅ 所有连接都有效`);
        }
        
      } catch (error) {
        console.error(`  ❌ 场景 ${sceneId} 解析失败:`, error);
      }
    }
    
    // 3. 测试图构建器（如果有图实例）
    console.log(`\n🎨 测试图构建器...`);
    const graphBuilder = new GraphBuilder();
    
    // 这里我们只能测试数据转换，因为没有真实的图实例
    const firstSceneId = sceneIds[0];
    if (allParsedScenes[firstSceneId]) {
      try {
        // 模拟图数据转换
        const parsedData = allParsedScenes[firstSceneId];
        console.log(`  📈 为场景 ${firstSceneId} 生成图数据结构`);
        console.log(`    - 总节点数: ${
          1 + // scene config
          parsedData.eventNodes.length +
          parsedData.actionNodes.length +
          parsedData.lifecycleNodes.length +
          parsedData.staticLabelNodes.length +
          parsedData.referenceNodes.length +
          parsedData.dataNodes.length
        }`);
        console.log(`    - 总连接数: ${parsedData.connections.length}`);
        
      } catch (error) {
        console.error(`  ❌ 图构建测试失败:`, error);
      }
    }
    
    // 4. 生成测试报告
    console.log(`\n📋 集成测试报告:`);
    console.log(`  🎯 测试场景数: ${sceneIds.length}`);
    console.log(`  ✅ 成功解析: ${Object.keys(allParsedScenes).length}`);
    console.log(`  ❌ 解析失败: ${sceneIds.length - Object.keys(allParsedScenes).length}`);
    
    const totalStats = Object.values(allParsedScenes).reduce((acc, data) => {
      acc.events += data.eventNodes.length;
      acc.actions += data.actionNodes.length;
      acc.lifecycles += data.lifecycleNodes.length;
      acc.labels += data.staticLabelNodes.length;
      acc.references += data.referenceNodes.length;
      acc.data += data.dataNodes.length;
      acc.connections += data.connections.length;
      return acc;
    }, { events: 0, actions: 0, lifecycles: 0, labels: 0, references: 0, data: 0, connections: 0 });
    
    console.log(`  📊 总计节点统计:`);
    console.log(`    - 事件节点: ${totalStats.events}`);
    console.log(`    - 动作节点: ${totalStats.actions}`);
    console.log(`    - 生命周期节点: ${totalStats.lifecycles}`);
    console.log(`    - 静态标签节点: ${totalStats.labels}`);
    console.log(`    - 引用节点: ${totalStats.references}`);
    console.log(`    - 数据节点: ${totalStats.data}`);
    console.log(`    - 连接: ${totalStats.connections}`);
    
    console.log(`\n🎉 完整集成测试完成！`);
    
    // 返回测试结果供进一步使用
    return {
      success: true,
      config,
      parsedScenes: allParsedScenes,
      stats: totalStats
    };
    
  } catch (error) {
    console.error("❌ 集成测试失败:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.runFullIntegrationTest = runFullIntegrationTest;
  window.loadPublicConfig = loadPublicConfig;
  console.log("集成测试函数已添加到window对象:");
  console.log("- runFullIntegrationTest() - 运行完整集成测试");
  console.log("- loadPublicConfig() - 加载配置文件");
}

export { runFullIntegrationTest, loadPublicConfig };
