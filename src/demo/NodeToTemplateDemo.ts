/**
 * NodeToTemplateDemo.ts
 * 节点转模板功能的演示脚本
 */

import { TemplateManager, TemplateType } from '../services/TemplateManager';
import { nodeSelector } from '../services/NodeSelector';

/**
 * 节点转模板功能演示
 */
export class NodeToTemplateDemo {
  private templateManager: TemplateManager;

  constructor() {
    this.templateManager = TemplateManager.getInstance();
  }

  /**
   * 运行完整演示
   */
  async runFullDemo(): Promise<void> {
    console.log('🚀 开始节点转模板功能演示');
    console.log('='.repeat(50));

    try {
      // 1. 演示节点创建
      await this.demoNodeCreation();
      
      // 2. 演示节点选择
      await this.demoNodeSelection();
      
      // 3. 演示模板提取
      await this.demoTemplateExtraction();
      
      // 4. 演示模板保存
      await this.demoTemplateSaving();
      
      // 5. 演示模板使用
      await this.demoTemplateUsage();
      
      // 6. 演示配置导出
      await this.demoConfigExport();

      console.log('='.repeat(50));
      console.log('✅ 节点转模板功能演示完成！');
      
    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 演示节点创建
   */
  private async demoNodeCreation(): Promise<void> {
    console.log('\n📦 1. 节点创建演示');
    console.log('-'.repeat(30));

    const nodeTypes: ('button' | 'label' | 'shape')[] = ['button', 'label', 'shape'];
    
    for (const nodeType of nodeTypes) {
      const node = nodeSelector.createMockNode(nodeType);
      const displayInfo = nodeSelector.getNodeDisplayInfo(node);
      
      console.log(`${displayInfo.icon} 创建${nodeType}节点:`);
      console.log(`  名称: ${displayInfo.name}`);
      console.log(`  类型: ${displayInfo.type}`);
      console.log(`  描述: ${displayInfo.description}`);
      
      // 验证节点
      const validation = nodeSelector.validateNodeForTemplate(node);
      console.log(`  验证: ${validation.valid ? '✅ 通过' : '❌ 失败'}`);
      if (validation.warnings.length > 0) {
        console.log(`  警告: ${validation.warnings.join(', ')}`);
      }
      console.log('');
    }
  }

  /**
   * 演示节点选择
   */
  private async demoNodeSelection(): Promise<void> {
    console.log('\n🎯 2. 节点选择演示');
    console.log('-'.repeat(30));

    // 创建一个按钮节点
    const buttonNode = nodeSelector.createMockNode('button');
    console.log('创建按钮节点...');

    // 设置选择回调
    let selectionCount = 0;
    const callback = (node: any) => {
      selectionCount++;
      if (node) {
        const info = nodeSelector.getNodeDisplayInfo(node);
        console.log(`📍 节点选择变化 #${selectionCount}: ${info.name}`);
      } else {
        console.log(`📍 节点选择变化 #${selectionCount}: 清除选择`);
      }
    };

    nodeSelector.onSelectionChange(callback);

    // 模拟选择过程
    console.log('模拟选择节点...');
    nodeSelector.setSelectedNode(buttonNode);
    
    await this.delay(500);
    
    console.log('清除选择...');
    nodeSelector.clearSelection();
    
    await this.delay(500);
    
    console.log('重新选择节点...');
    nodeSelector.setSelectedNode(buttonNode);

    // 清理回调
    nodeSelector.removeSelectionListener(callback);
  }

  /**
   * 演示模板提取
   */
  private async demoTemplateExtraction(): Promise<void> {
    console.log('\n🔧 3. 模板提取演示');
    console.log('-'.repeat(30));

    const selectedNode = nodeSelector.getSelectedNode();
    if (!selectedNode) {
      console.log('❌ 没有选中的节点');
      return;
    }

    console.log('从选中节点提取模板...');
    
    const result = this.templateManager.createTemplateFromNode(
      selectedNode,
      '演示按钮模板',
      '从演示节点创建的按钮模板',
      '演示'
    );

    if (result.success && result.data) {
      console.log('✅ 模板提取成功:');
      console.log(`  ID: ${result.data.id}`);
      console.log(`  名称: ${result.data.name}`);
      console.log(`  类型: ${result.data.type}`);
      console.log(`  分类: ${result.data.metadata.category}`);
      console.log(`  来源: ${result.data.metadata.sourceType}`);
      console.log(`  参数数量: ${result.data.definition?.parameters.length || 0}`);
      console.log(`  依赖数量: ${result.data.metadata.dependencies.length}`);
      
      // 显示参数详情
      if (result.data.definition?.parameters.length) {
        console.log('  参数列表:');
        result.data.definition.parameters.forEach(param => {
          console.log(`    - ${param.name} (${param.type}): ${param.description}`);
        });
      }
    } else {
      console.log('❌ 模板提取失败:', result.errors?.join(', '));
    }
  }

  /**
   * 演示模板保存
   */
  private async demoTemplateSaving(): Promise<void> {
    console.log('\n💾 4. 模板保存演示');
    console.log('-'.repeat(30));

    const stats = this.templateManager.getUsageStats();
    console.log(`当前模板统计:`);
    console.log(`  总数: ${stats.totalTemplates}`);
    console.log(`  按类型分布:`);
    Object.entries(stats.byType).forEach(([type, count]) => {
      console.log(`    ${type}: ${count}`);
    });
    console.log(`  按分类分布:`);
    Object.entries(stats.byCategory).forEach(([category, count]) => {
      console.log(`    ${category}: ${count}`);
    });

    // 创建更多演示模板
    console.log('\n创建更多演示模板...');
    
    const demoTemplates = [
      { nodeType: 'label' as const, name: '演示标签模板', category: '标签' },
      { nodeType: 'shape' as const, name: '演示形状模板', category: '图形' }
    ];

    for (const demo of demoTemplates) {
      const node = nodeSelector.createMockNode(demo.nodeType);
      const result = this.templateManager.createTemplateFromNode(
        node,
        demo.name,
        `${demo.name}的描述`,
        demo.category
      );

      if (result.success) {
        console.log(`✅ 创建模板: ${demo.name}`);
      } else {
        console.log(`❌ 创建失败: ${demo.name}`);
      }
    }

    // 显示更新后的统计
    const newStats = this.templateManager.getUsageStats();
    console.log(`\n更新后的模板统计: ${newStats.totalTemplates} 个模板`);
  }

  /**
   * 演示模板使用
   */
  private async demoTemplateUsage(): Promise<void> {
    console.log('\n🎨 5. 模板使用演示');
    console.log('-'.repeat(30));

    const allTemplates = this.templateManager.getAllTemplates();
    console.log(`可用模板列表 (${allTemplates.length} 个):`);

    allTemplates.forEach((template, index) => {
      console.log(`  ${index + 1}. ${template.name}`);
      console.log(`     类型: ${template.type} | 分类: ${template.metadata.category}`);
      console.log(`     来源: ${template.metadata.sourceType}`);
      console.log(`     标签: ${template.metadata.tags.join(', ')}`);
    });

    // 演示模板实例化
    if (allTemplates.length > 0) {
      const firstTemplate = allTemplates[0];
      console.log(`\n创建模板实例: ${firstTemplate.name}`);
      
      const instanceResult = this.templateManager.createTemplateInstance(
        firstTemplate.id,
        'demo-node-1',
        'demo-scene',
        { customProperty: 'demo-value' }
      );

      if (instanceResult.success && instanceResult.data) {
        console.log('✅ 实例创建成功:');
        console.log(`  实例ID: ${instanceResult.data.id}`);
        console.log(`  节点ID: ${instanceResult.data.nodeId}`);
        console.log(`  场景ID: ${instanceResult.data.sceneId}`);
        console.log(`  同步状态: ${instanceResult.data.syncStatus}`);
      }
    }
  }

  /**
   * 演示配置导出
   */
  private async demoConfigExport(): Promise<void> {
    console.log('\n📤 6. 配置导出演示');
    console.log('-'.repeat(30));

    // 导出所有模板
    console.log('导出模板到config.js格式...');
    const configData = this.templateManager.exportToConfig();
    
    console.log('导出的配置结构:');
    if (configData.templates) {
      Object.keys(configData.templates).forEach(sceneId => {
        console.log(`  场景 ${sceneId}:`);
        const sceneTemplates = configData.templates[sceneId];
        Object.keys(sceneTemplates).forEach(typeKey => {
          const typeTemplates = sceneTemplates[typeKey];
          const count = Object.keys(typeTemplates).length;
          console.log(`    ${typeKey}: ${count} 个模板`);
        });
      });
    }

    // 生成配置文件内容
    const configFileContent = this.templateManager.configExporter.generateConfigFile(configData);
    console.log(`\n生成的配置文件大小: ${configFileContent.length} 字符`);
    console.log('配置文件预览 (前200字符):');
    console.log(configFileContent.substring(0, 200) + '...');

    // 导出实例配置
    console.log('\n导出实例配置...');
    const instanceConfig = this.templateManager.exportInstancesToConfig();
    if (instanceConfig.scenes && Object.keys(instanceConfig.scenes).length > 0) {
      console.log('实例配置结构:');
      Object.keys(instanceConfig.scenes).forEach(sceneId => {
        const sceneInstances = instanceConfig.scenes[sceneId];
        const instanceCount = Object.keys(sceneInstances).length;
        console.log(`  场景 ${sceneId}: ${instanceCount} 个实例`);
      });
    } else {
      console.log('暂无实例配置');
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理演示数据
   */
  clearDemoData(): void {
    console.log('🧹 清理演示数据...');
    
    // 清除节点选择
    nodeSelector.clearSelection();
    
    // 这里可以添加清理模板的逻辑
    // 注意：实际应用中可能不需要清理功能
    
    console.log('✅ 演示数据清理完成');
  }
}

// 导出演示实例
export const nodeToTemplateDemo = new NodeToTemplateDemo();

// 如果在浏览器环境中，将演示添加到全局对象
if (typeof window !== 'undefined') {
  (window as any).nodeToTemplateDemo = nodeToTemplateDemo;
  console.log('💡 演示已添加到全局对象，可以通过 window.nodeToTemplateDemo 访问');
  console.log('💡 运行 window.nodeToTemplateDemo.runFullDemo() 开始演示');
}
