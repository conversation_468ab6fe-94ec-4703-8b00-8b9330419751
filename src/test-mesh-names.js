/**
 * 测试meshNames处理功能
 */

import { ConfigParser } from './services/ConfigParser.js';

// 创建测试配置，包含不同格式的meshNames
const testMeshNamesConfig = {
  name: "meshNames测试场景",
  models: ["test.glb"],
  actions: [
    {
      actionType: "doubleClick",
      meshNames: ["mesh1", "mesh2", "mesh3"], // 直接数组格式
      config: {
        highlight: { color: [1, 0, 0], duration: 500 },
        callback: "CameraService.focusToDevice"
      }
    },
    {
      actionType: "hover",
      meshNames: { $ref: "templates.1.meshes.buildingLevels" }, // 引用格式
      config: {
        highlight: { color: [0, 1, 0], duration: 300 }
      }
    },
    {
      actionType: "click",
      // 没有meshNames，应该使用默认值
      config: {
        highlight: { color: [0, 0, 1], duration: 400 }
      }
    }
  ],
  staticLabels: [
    {
      meshNames: ["label-mesh1", "label-mesh2"], // 直接数组格式
      customNames: {
        "label-mesh1": "标签1",
        "label-mesh2": "标签2"
      }
    },
    {
      $ref: "templates.1.labels.floorLabel",
      $extend: {
        meshNames: ["extended-mesh"], // 扩展中的直接数组
        customNames: { "extended-mesh": "扩展标签" }
      }
    }
  ]
};

/**
 * 测试meshNames的不同格式处理
 */
function testMeshNamesHandling() {
  console.log("🔍 开始测试meshNames处理功能...");
  
  try {
    const parser = new ConfigParser();
    const parsedData = parser.parseScene("mesh-names-test", testMeshNamesConfig);
    
    console.log("📊 解析结果统计:");
    console.log(`- 事件节点: ${parsedData.eventNodes.length}`);
    console.log(`- 静态标签节点: ${parsedData.staticLabelNodes.length}`);
    
    // 测试事件节点的meshNames处理
    console.log("\n🎯 事件节点meshNames测试:");
    parsedData.eventNodes.forEach((eventNode, index) => {
      console.log(`\n事件节点 ${index + 1} (${eventNode.actionType}):`);
      console.log(`  - meshNames:`, eventNode.meshNames);
      console.log(`  - meshNamesIsRef:`, eventNode.meshNamesIsRef);
      console.log(`  - meshNamesRef:`, eventNode.meshNamesRef);
      
      // 验证数据一致性
      if (eventNode.meshNamesIsRef) {
        if (!eventNode.meshNamesRef) {
          console.warn(`  ⚠️  引用节点缺少meshNamesRef`);
        }
        if (!eventNode.meshNames || !eventNode.meshNames.$ref) {
          console.warn(`  ⚠️  引用节点的meshNames格式不正确`);
        }
      } else {
        if (eventNode.meshNamesRef) {
          console.warn(`  ⚠️  非引用节点不应该有meshNamesRef`);
        }
        if (!Array.isArray(eventNode.meshNames)) {
          console.warn(`  ⚠️  非引用节点的meshNames应该是数组`);
        }
      }
    });
    
    // 测试静态标签节点的meshNames处理
    console.log("\n🏷️  静态标签节点meshNames测试:");
    parsedData.staticLabelNodes.forEach((labelNode, index) => {
      console.log(`\n标签节点 ${index + 1}:`);
      console.log(`  - meshNames:`, labelNode.meshNames);
      console.log(`  - customNames:`, labelNode.customNames);
      
      // 验证meshNames和customNames的一致性
      if (Array.isArray(labelNode.meshNames)) {
        const meshNamesSet = new Set(labelNode.meshNames);
        const customNamesKeys = new Set(Object.keys(labelNode.customNames || {}));
        
        const missingCustomNames = [...meshNamesSet].filter(mesh => !customNamesKeys.has(mesh));
        const extraCustomNames = [...customNamesKeys].filter(key => !meshNamesSet.has(key));
        
        if (missingCustomNames.length > 0) {
          console.warn(`  ⚠️  缺少自定义名称的网格:`, missingCustomNames);
        }
        if (extraCustomNames.length > 0) {
          console.warn(`  ⚠️  多余的自定义名称:`, extraCustomNames);
        }
        if (missingCustomNames.length === 0 && extraCustomNames.length === 0) {
          console.log(`  ✅ meshNames和customNames完全匹配`);
        }
      }
    });
    
    // 测试引用节点的处理
    console.log("\n🔗 引用节点测试:");
    console.log(`- 引用节点数量: ${parsedData.referenceNodes.length}`);
    parsedData.referenceNodes.forEach((refNode, index) => {
      console.log(`\n引用节点 ${index + 1}:`);
      console.log(`  - 引用路径: ${refNode.refPath}`);
      console.log(`  - 引用类型: ${refNode.refType}`);
      console.log(`  - 扩展参数:`, refNode.extendParams);
    });
    
    // 测试连接推断
    console.log("\n🔗 连接推断测试:");
    console.log(`- 连接数量: ${parsedData.connections.length}`);
    parsedData.connections.forEach((conn, index) => {
      console.log(`  ${index + 1}. ${conn.source} -> ${conn.target} (${conn.sourcePort} -> ${conn.targetPort})`);
    });
    
    // 验证所有节点都有有效的ID和类型
    const allNodes = [
      parsedData.sceneConfig,
      ...parsedData.eventNodes,
      ...parsedData.actionNodes,
      ...parsedData.lifecycleNodes,
      ...parsedData.staticLabelNodes,
      ...parsedData.referenceNodes,
      ...parsedData.dataNodes
    ];
    
    console.log("\n✅ 节点完整性验证:");
    const invalidNodes = allNodes.filter(node => !node.id || !node.nodeType || !node.displayName);
    if (invalidNodes.length === 0) {
      console.log(`  ✅ 所有 ${allNodes.length} 个节点都有有效的ID、类型和显示名称`);
    } else {
      console.warn(`  ⚠️  ${invalidNodes.length} 个节点缺少必要属性:`, invalidNodes);
    }
    
    // 生成UI配置建议
    console.log("\n💡 UI配置建议:");
    const directMeshNodes = parsedData.eventNodes.filter(node => !node.meshNamesIsRef);
    const refMeshNodes = parsedData.eventNodes.filter(node => node.meshNamesIsRef);
    
    console.log(`- ${directMeshNodes.length} 个事件节点使用直接网格数组`);
    console.log(`- ${refMeshNodes.length} 个事件节点使用模板引用`);
    
    if (directMeshNodes.length > 0) {
      console.log("  建议: 为直接网格数组提供可视化编辑器");
    }
    if (refMeshNodes.length > 0) {
      console.log("  建议: 为模板引用提供路径选择器");
    }
    
    console.log("\n🎉 meshNames处理功能测试完成！");
    return {
      success: true,
      parsedData,
      stats: {
        totalNodes: allNodes.length,
        eventNodes: parsedData.eventNodes.length,
        directMeshNodes: directMeshNodes.length,
        refMeshNodes: refMeshNodes.length,
        staticLabelNodes: parsedData.staticLabelNodes.length,
        referenceNodes: parsedData.referenceNodes.length,
        connections: parsedData.connections.length
      }
    };
    
  } catch (error) {
    console.error("❌ meshNames处理功能测试失败:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试meshNames的UI交互
 */
function testMeshNamesUI() {
  console.log("🎨 测试meshNames的UI交互功能...");
  
  // 模拟UI交互场景
  const testScenarios = [
    {
      name: "从直接数组切换到引用",
      initial: { meshNames: ["mesh1", "mesh2"], meshNamesIsRef: false },
      action: "switchToRef",
      expected: { meshNamesIsRef: true, meshNamesRef: "templates.1.meshes.buildingLevels" }
    },
    {
      name: "从引用切换到直接数组",
      initial: { meshNames: { $ref: "templates.1.meshes.gisDoors" }, meshNamesIsRef: true },
      action: "switchToDirect",
      expected: { meshNamesIsRef: false, meshNames: [] }
    },
    {
      name: "编辑直接数组",
      initial: { meshNames: ["old1", "old2"], meshNamesIsRef: false },
      action: "editDirect",
      newValue: ["new1", "new2", "new3"],
      expected: { meshNames: ["new1", "new2", "new3"], meshNamesIsRef: false }
    }
  ];
  
  testScenarios.forEach((scenario, index) => {
    console.log(`\n测试场景 ${index + 1}: ${scenario.name}`);
    console.log(`  初始状态:`, scenario.initial);
    console.log(`  执行操作: ${scenario.action}`);
    if (scenario.newValue) {
      console.log(`  新值:`, scenario.newValue);
    }
    console.log(`  期望结果:`, scenario.expected);
    console.log(`  ✅ 场景定义完整`);
  });
  
  console.log("\n💡 UI实现建议:");
  console.log("1. 使用单选按钮切换直接数组/引用模式");
  console.log("2. 直接数组模式：提供JSON文本编辑器");
  console.log("3. 引用模式：提供路径输入框和自动完成");
  console.log("4. 实时验证JSON格式和引用路径有效性");
  console.log("5. 提供常用网格名称的快速选择");
  
  return true;
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testMeshNamesHandling = testMeshNamesHandling;
  window.testMeshNamesUI = testMeshNamesUI;
  console.log("meshNames测试函数已添加到window对象:");
  console.log("- testMeshNamesHandling() - 测试meshNames处理功能");
  console.log("- testMeshNamesUI() - 测试meshNames UI交互");
}

export { testMeshNamesHandling, testMeshNamesUI };
