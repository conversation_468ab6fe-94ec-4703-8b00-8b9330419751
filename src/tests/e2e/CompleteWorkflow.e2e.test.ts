/**
 * CompleteWorkflow.e2e.test.ts
 * 完整工作流程的端到端测试
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { TestRunner, createTestSuite, TestUtils, TestAssertions } from '../runTests';
import { TemplateManager } from '../../services/TemplateManager';
import { NodeSelector } from '../../services/NodeSelector';
import { notificationService } from '../../services/NotificationService';

describe('完整工作流程端到端测试', () => {
  let templateManager: TemplateManager;
  let nodeSelector: NodeSelector;

  beforeEach(() => {
    // 重置所有单例实例
    (TemplateManager as any).instance = null;
    (NodeSelector as any).instance = null;
    
    templateManager = TemplateManager.getInstance();
    nodeSelector = NodeSelector.getInstance();
    
    TestUtils.cleanup();
    notificationService.clear();
  });

  afterEach(() => {
    TestUtils.cleanup();
    notificationService.clear();
  });

  describe('用户故事：从节点创建到模板应用', () => {
    it('用户故事1：设计师创建高亮动作模板', async () => {
      // 场景：设计师在场景中选择了一个需要高亮的建筑构件
      const buildingNode = {
        id: 'building-component-001',
        data: {
          nodeType: 'action-highlight',
          displayName: '电气楼五层高亮',
          color: [1.0, 0.8, 0.2], // 橙色高亮
          duration: 2000,
          intensity: 1.5,
          fadeIn: true,
          fadeOut: true
        },
        attrs: {
          body: { fill: '#ffffff', stroke: '#ff6600', strokeWidth: 3 },
          text: { text: '电气楼五层', fontSize: 16, fontWeight: 'bold' }
        },
        position: { x: 150, y: 300 },
        size: { width: 200, height: 120 },
        getData: function() { return this.data; },
        getAttrs: function() { return this.attrs; },
        getPosition: function() { return this.position; },
        getSize: function() { return this.size; }
      };

      // 步骤1：选择节点
      const selectionResult = nodeSelector.selectNode(buildingNode);
      expect(selectionResult.success).toBe(true);
      expect(nodeSelector.getSelectedNode()).toBe(buildingNode);

      // 步骤2：分析节点属性
      const analysis = nodeSelector.analyzeNode(buildingNode);
      expect(analysis.success).toBe(true);
      expect(analysis.nodeInfo!.type).toBe('action-highlight');
      expect(analysis.parameters!.color).toEqual([1.0, 0.8, 0.2]);
      expect(analysis.parameters!.duration).toBe(2000);

      // 步骤3：生成模板建议
      const suggestions = nodeSelector.generateTemplateSuggestions(buildingNode);
      expect(suggestions.length).toBeGreaterThan(0);
      
      const bestSuggestion = suggestions[0];
      expect(bestSuggestion.type).toBe('ACTION');
      expect(bestSuggestion.confidence).toBeGreaterThan(0.8);

      // 步骤4：创建模板
      const template = TestUtils.createTestTemplate({
        name: '建筑构件高亮动作',
        type: 'ACTION' as any,
        description: '用于高亮显示建筑构件的动作模板，支持自定义颜色、持续时间和强度',
        data: analysis.parameters,
        definition: {
          parameters: [
            { name: 'color', type: 'array', defaultValue: [1.0, 0.8, 0.2], required: true, description: '高亮颜色RGB值' },
            { name: 'duration', type: 'number', defaultValue: 2000, required: true, description: '高亮持续时间(毫秒)' },
            { name: 'intensity', type: 'number', defaultValue: 1.5, required: false, description: '高亮强度倍数' },
            { name: 'fadeIn', type: 'boolean', defaultValue: true, required: false, description: '是否渐入效果' },
            { name: 'fadeOut', type: 'boolean', defaultValue: true, required: false, description: '是否渐出效果' }
          ],
          dependencies: [],
          validation: {
            required: ['color', 'duration'],
            constraints: {
              duration: { min: 100, max: 10000 },
              intensity: { min: 0.1, max: 5.0 }
            }
          }
        },
        metadata: {
          category: '动作',
          tags: ['highlight', 'building', 'animation'],
          author: 'designer-001',
          createdAt: new Date(),
          updatedAt: new Date(),
          usageCount: 0,
          isBuiltIn: false,
          dependencies: [],
          sourceType: 'user-created' as const
        }
      });

      const createResult = templateManager.createTemplate(template);
      expect(createResult.success).toBe(true);
      expect(createResult.data!.name).toBe('建筑构件高亮动作');

      // 步骤5：验证模板创建成功
      const createdTemplate = templateManager.getTemplate(template.id);
      expect(createdTemplate).toBeDefined();
      expect(createdTemplate!.definition.parameters).toHaveLength(5);

      // 步骤6：应用模板到其他构件
      const targetNodes = [
        'building-component-002',
        'building-component-003',
        'building-component-004'
      ];

      const instances = [];
      for (const nodeId of targetNodes) {
        const instanceResult = await templateManager.instantiateTemplate(
          template.id,
          'main-scene',
          nodeId,
          {
            color: [0.2, 0.8, 1.0], // 蓝色高亮
            duration: 1500,
            intensity: 1.2,
            fadeIn: true,
            fadeOut: false
          }
        );
        
        expect(instanceResult.success).toBe(true);
        instances.push(instanceResult.data!);
      }

      // 步骤7：验证实例创建和追踪
      const trackedInstances = templateManager.instanceTracker.getTemplateInstances(template.id);
      expect(trackedInstances).toHaveLength(3);
      
      trackedInstances.forEach(instance => {
        expect(instance.templateId).toBe(template.id);
        expect(instance.parameters.color).toEqual([0.2, 0.8, 1.0]);
        expect(instance.status).toBe('active');
        expect(instance.syncStatus).toBe('synced');
      });

      // 步骤8：验证统计信息
      const stats = templateManager.getUsageStats();
      expect(stats.totalTemplates).toBe(1);
      expect(stats.totalInstances).toBe(3);
      expect(stats.templatesByType['ACTION']).toBe(1);
    });

    it('用户故事2：项目经理批量更新模板参数', async () => {
      // 场景：项目经理需要统一调整所有高亮动作的持续时间
      
      // 准备：创建多个高亮模板和实例
      const templates = [];
      const allInstances = [];

      for (let i = 0; i < 3; i++) {
        const template = TestUtils.createTestTemplate({
          name: `高亮动作模板 ${i + 1}`,
          type: 'ACTION' as any,
          definition: {
            parameters: [
              { name: 'color', type: 'array', defaultValue: [1, 0, 0], required: true },
              { name: 'duration', type: 'number', defaultValue: 1000, required: true },
              { name: 'intensity', type: 'number', defaultValue: 1.0, required: false }
            ],
            dependencies: [],
            validation: { required: ['color', 'duration'], constraints: {} }
          }
        });

        const createResult = templateManager.createTemplate(template);
        expect(createResult.success).toBe(true);
        templates.push(createResult.data!);

        // 为每个模板创建实例
        for (let j = 0; j < 2; j++) {
          const instanceResult = await templateManager.instantiateTemplate(
            template.id,
            'project-scene',
            `node-${i}-${j}`,
            { color: [1, 0, 0], duration: 1000, intensity: 1.0 }
          );
          expect(instanceResult.success).toBe(true);
          allInstances.push(instanceResult.data!);
        }
      }

      // 步骤1：项目经理决定统一调整持续时间为3秒
      const newDuration = 3000;
      const templateIds = templates.map(t => t.id);

      // 步骤2：批量更新模板定义
      const batchUpdateResult = await templateManager.batchUpdateManager.batchUpdateTemplates(
        templateIds,
        {
          definition: {
            parameters: [
              { name: 'color', type: 'array', defaultValue: [1, 0, 0], required: true },
              { name: 'duration', type: 'number', defaultValue: newDuration, required: true },
              { name: 'intensity', type: 'number', defaultValue: 1.0, required: false }
            ],
            dependencies: [],
            validation: { required: ['color', 'duration'], constraints: {} }
          }
        }
      );

      expect(batchUpdateResult.success).toBe(true);
      expect(batchUpdateResult.successCount).toBe(3);
      expect(batchUpdateResult.failureCount).toBe(0);

      // 步骤3：检测参数变更
      let totalChanges = 0;
      for (const template of templates) {
        const updatedTemplate = templateManager.getTemplate(template.id);
        expect(updatedTemplate).toBeDefined();
        
        const changes = templateManager.parameterSyncManager.detectParameterChanges(
          template,
          updatedTemplate!
        );
        
        const durationChanges = changes.filter(c => 
          c.parameterName === 'duration' && c.changeType === 'modified'
        );
        expect(durationChanges).toHaveLength(1);
        totalChanges += changes.length;
      }

      expect(totalChanges).toBeGreaterThan(0);

      // 步骤4：批量同步所有实例
      const batchSyncResult = await templateManager.batchSyncTemplates(templateIds, {
        strategy: 'smart' // 智能策略：保留用户覆盖，更新默认值
      });

      expect(batchSyncResult.success).toBe(true);
      expect(batchSyncResult.processedTemplates).toBe(3);

      // 步骤5：验证同步结果
      for (const templateId of templateIds) {
        const instances = templateManager.instanceTracker.getTemplateInstances(templateId);
        expect(instances).toHaveLength(2);
        
        instances.forEach(instance => {
          expect(instance.syncStatus).toBe('synced');
          // 如果实例没有覆盖duration，应该使用新的默认值
          if (!instance.overrides.duration) {
            expect(instance.parameters.duration).toBe(newDuration);
          }
        });
      }

      // 步骤6：验证变更历史记录
      for (const templateId of templateIds) {
        const history = templateManager.parameterSyncManager.getParameterChangeHistory(templateId);
        expect(history.length).toBeGreaterThan(0);
        
        const latestChange = history[0];
        expect(latestChange.templateId).toBe(templateId);
        expect(latestChange.changes.some(c => c.parameterName === 'duration')).toBe(true);
      }
    });

    it('用户故事3：开发者处理参数冲突', async () => {
      // 场景：开发者更新模板时遇到参数冲突，需要智能解决
      
      // 准备：创建模板和实例
      const originalTemplate = TestUtils.createTestTemplate({
        name: '样式模板',
        type: 'STYLE' as any,
        definition: {
          parameters: [
            { name: 'backgroundColor', type: 'string', defaultValue: '#ffffff', required: false },
            { name: 'borderColor', type: 'string', defaultValue: '#000000', required: false },
            { name: 'borderWidth', type: 'number', defaultValue: 1, required: false },
            { name: 'opacity', type: 'number', defaultValue: 1.0, required: false }
          ],
          dependencies: [],
          validation: { required: [], constraints: {} }
        }
      });

      templateManager.createTemplate(originalTemplate);

      // 创建有自定义覆盖的实例
      const instances = [];
      const customInstances = [
        {
          nodeId: 'custom-node-1',
          overrides: { backgroundColor: '#ff0000', borderWidth: 2 }
        },
        {
          nodeId: 'custom-node-2', 
          overrides: { borderColor: '#0000ff', opacity: 0.8 }
        },
        {
          nodeId: 'default-node-1',
          overrides: {} // 使用默认值
        }
      ];

      for (const config of customInstances) {
        const instanceResult = await templateManager.instantiateTemplate(
          originalTemplate.id,
          'style-scene',
          config.nodeId,
          {
            backgroundColor: config.overrides.backgroundColor || '#ffffff',
            borderColor: config.overrides.borderColor || '#000000',
            borderWidth: config.overrides.borderWidth || 1,
            opacity: config.overrides.opacity || 1.0
          }
        );
        expect(instanceResult.success).toBe(true);
        
        // 设置覆盖信息
        const instance = instanceResult.data!;
        instance.overrides = config.overrides;
        instances.push(instance);
      }

      // 步骤1：开发者更新模板，引入冲突
      const conflictingTemplate = {
        ...originalTemplate,
        definition: {
          parameters: [
            { name: 'backgroundColor', type: 'string', defaultValue: '#f0f0f0', required: false }, // 默认值变更
            { name: 'borderColor', type: 'number', defaultValue: 0x000000, required: false }, // 类型变更！
            { name: 'borderRadius', type: 'number', defaultValue: 4, required: false }, // 新增参数
            // opacity 参数被删除！
          ],
          dependencies: [],
          validation: { required: [], constraints: {} }
        }
      };

      const updateResult = templateManager.updateTemplate(originalTemplate.id, conflictingTemplate);
      expect(updateResult.success).toBe(true);

      // 步骤2：检测冲突
      const conflicts = templateManager.getParameterConflicts(originalTemplate.id);
      expect(conflicts.length).toBeGreaterThan(0);

      // 应该检测到类型不匹配冲突
      const typeMismatchConflicts = conflicts.filter(c => c.conflictType === 'type_mismatch');
      expect(typeMismatchConflicts.length).toBeGreaterThan(0);
      expect(typeMismatchConflicts[0].parameterName).toBe('borderColor');

      // 应该检测到参数删除冲突
      const parameterRemovedConflicts = conflicts.filter(c => c.conflictType === 'parameter_removed');
      expect(parameterRemovedConflicts.length).toBeGreaterThan(0);
      expect(parameterRemovedConflicts[0].parameterName).toBe('opacity');

      // 步骤3：开发者选择智能解决策略
      const resolutionResults = [];
      for (const conflict of conflicts) {
        let resolution: 'use_template' | 'keep_instance';
        
        if (conflict.conflictType === 'type_mismatch') {
          // 类型冲突：保持实例值，避免数据损坏
          resolution = 'keep_instance';
        } else if (conflict.conflictType === 'parameter_removed') {
          // 参数删除：如果实例有覆盖值，保留；否则接受删除
          const hasOverride = conflict.affectedInstances.some(instanceId => {
            const instance = instances.find(i => i.id === instanceId);
            return instance && instance.overrides[conflict.parameterName] !== undefined;
          });
          resolution = hasOverride ? 'keep_instance' : 'use_template';
        } else {
          // 其他冲突：使用模板值
          resolution = 'use_template';
        }

        const resolveResult = await templateManager.resolveParameterConflict(conflict.id, resolution);
        expect(resolveResult).toBe(true);
        resolutionResults.push({ conflict, resolution, success: resolveResult });
      }

      // 步骤4：验证冲突解决结果
      const remainingConflicts = templateManager.getParameterConflicts(originalTemplate.id);
      expect(remainingConflicts).toHaveLength(0);

      // 步骤5：同步所有实例
      const syncResult = await templateManager.syncTemplateParameters(originalTemplate.id);
      expect(syncResult.success).toBe(true);

      // 步骤6：验证最终状态
      const finalInstances = templateManager.instanceTracker.getTemplateInstances(originalTemplate.id);
      expect(finalInstances).toHaveLength(3);

      finalInstances.forEach(instance => {
        expect(instance.syncStatus).toBe('synced');
        
        // 验证新参数已添加
        expect(instance.parameters.borderRadius).toBe(4);
        
        // 验证类型冲突处理：borderColor应该保持原始字符串类型
        if (instance.overrides.borderColor) {
          expect(typeof instance.parameters.borderColor).toBe('string');
        }
        
        // 验证参数删除处理：有覆盖的实例应该保留opacity
        if (instance.overrides.opacity !== undefined) {
          expect(instance.parameters.opacity).toBeDefined();
        }
      });

      // 步骤7：验证变更历史和冲突解决记录
      const changeHistory = templateManager.parameterSyncManager.getParameterChangeHistory(originalTemplate.id);
      expect(changeHistory.length).toBeGreaterThan(0);
      
      const conflictHistory = templateManager.conflictResolver.getConflictHistory(originalTemplate.id);
      expect(conflictHistory.length).toBe(resolutionResults.length);
    });
  });

  describe('性能和稳定性测试', () => {
    it('应该在高负载下保持稳定', async () => {
      const startTime = Date.now();
      
      // 创建大量模板和实例
      const templateCount = 50;
      const instancesPerTemplate = 20;
      
      const templates = TestUtils.createTestTemplates(templateCount);
      const createdTemplates = [];
      
      // 批量创建模板
      for (const template of templates) {
        const result = templateManager.createTemplate(template);
        expect(result.success).toBe(true);
        createdTemplates.push(result.data!);
      }
      
      // 并发创建实例
      const instancePromises = [];
      for (const template of createdTemplates) {
        for (let i = 0; i < instancesPerTemplate; i++) {
          const promise = templateManager.instantiateTemplate(
            template.id,
            'load-test-scene',
            `load-test-node-${template.id}-${i}`,
            { size: 1.0 + i * 0.1 }
          );
          instancePromises.push(promise);
        }
      }
      
      const instanceResults = await Promise.all(instancePromises);
      const successfulInstances = instanceResults.filter(r => r.success);
      
      expect(successfulInstances).toHaveLength(templateCount * instancesPerTemplate);
      
      // 验证性能指标
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(10000); // 应该在10秒内完成
      
      // 验证内存使用
      const metrics = templateManager.getPerformanceMetrics();
      expect(metrics.templateCount).toBe(templateCount);
      expect(metrics.instanceCount).toBe(templateCount * instancesPerTemplate);
      expect(metrics.memoryUsage.total).toBeLessThan(100 * 1024 * 1024); // 小于100MB
    });

    it('应该正确处理系统资源限制', async () => {
      // 模拟内存不足情况
      const originalCreateTemplate = templateManager.createTemplate.bind(templateManager);
      let creationCount = 0;
      
      templateManager.createTemplate = function(template: any) {
        creationCount++;
        if (creationCount > 10) {
          return { success: false, errors: ['内存不足'] };
        }
        return originalCreateTemplate(template);
      };
      
      try {
        const templates = TestUtils.createTestTemplates(20);
        const results = templates.map(template => templateManager.createTemplate(template));
        
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        
        expect(successCount).toBe(10);
        expect(failureCount).toBe(10);
        
        // 验证系统仍然可用
        const stats = templateManager.getUsageStats();
        expect(stats.totalTemplates).toBe(10);
        
      } finally {
        // 恢复原始方法
        templateManager.createTemplate = originalCreateTemplate;
      }
    });
  });
});
