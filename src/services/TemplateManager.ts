/**
 * 模板管理器 - 模板系统的核心管理组件
 */

import { reactive, ref, computed } from 'vue';
import { Cell } from '@antv/x6';
import { ConfigImporter } from './ConfigImporter';
import { ConfigExporter } from './ConfigExporter';
import { TemplateExtractor } from './TemplateExtractor';

// 模板管理器类型定义
export type TemplateManagerResult<T = any> = {
  success: boolean;
  data?: T;
  errors?: string[];
};

// 模板类型枚举
export enum TemplateType {
  MESH = 'mesh',
  STYLE = 'style',
  CAMERA = 'camera',
  ACTION = 'action',
  LABEL = 'label',
  POSITION = 'position',
  INTERACTION = 'interaction',
  ENVIRONMENT = 'environment',
  CALLBACK = 'callback'
}

// 模板参数接口
export interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  defaultValue: any;
  required: boolean;
  options?: any[];
}

// 模板版本接口
export interface TemplateVersion {
  version: string;
  data: any;
  createdAt: Date;
  createdBy: string;
  comment: string;
}

// 模板元数据接口
export interface TemplateMetadata {
  tags: string[];
  author: string;
  usage: number;
  dependencies: string[];
  preview?: string;
  category: string;
  sourceType: 'user-created' | 'config-imported' | 'node-converted';
}

// 模板定义接口
export interface TemplateDefinition {
  nodeType: string;
  properties: Record<string, any>;
  parameters: TemplateParameter[];
  dependencies: string[];
  configPath?: string; // 在config.js中的路径
}

// 模板接口
export interface Template {
  id: string;
  name: string;
  type: TemplateType;
  description: string;
  data: any;
  definition?: TemplateDefinition;
  metadata: TemplateMetadata;
  version: string;
  path: string; // 模板在配置中的路径，如 "templates.1.meshes.buildingLevels"
  createdAt: Date;
  updatedAt: Date;
  versionHistory?: TemplateVersion[];
}

// 模板实例接口
export interface TemplateInstance {
  id: string;
  templateId: string;
  templateVersion: string;
  nodeId: string;
  sceneId: string;

  // 实例特定参数（覆盖模板默认值）
  overrides: Record<string, any>;

  // 同步状态
  syncStatus: 'synced' | 'outdated' | 'conflict';
  lastSyncAt: Date;

  // config.js引用信息
  configRef?: {
    path: string; // 如 "templates.1.styles.orangeGradient"
    isReference: boolean; // 是否使用$ref引用
  };
}

// 模板引用接口
export interface TemplateReference {
  $ref: string;
  $extend?: Record<string, any>;
  $override?: Record<string, any>;
}

// 配置引用接口
export interface ConfigReference {
  path: string; // 如 "templates.1.styles.orangeGradient"
  resolvedValue: any; // 解析后的实际值
  dependencies: string[]; // 依赖的其他引用路径
}

// 模板注册表
export class TemplateRegistry {
  private templates = reactive<Map<string, Template>>(new Map());
  private templatesByType = reactive<Map<TemplateType, Template[]>>(new Map());
  private templatesByCategory = reactive<Map<string, Template[]>>(new Map());

  /**
   * 注册模板
   */
  register(template: Template): void {
    this.templates.set(template.id, template);
    
    // 按类型分组
    if (!this.templatesByType.has(template.type)) {
      this.templatesByType.set(template.type, []);
    }
    this.templatesByType.get(template.type)!.push(template);
    
    // 按分类分组
    const category = template.metadata.category;
    if (!this.templatesByCategory.has(category)) {
      this.templatesByCategory.set(category, []);
    }
    this.templatesByCategory.get(category)!.push(template);
  }

  /**
   * 获取模板
   */
  get(id: string): Template | undefined {
    return this.templates.get(id);
  }

  /**
   * 获取所有模板
   */
  getAll(): Template[] {
    return Array.from(this.templates.values());
  }

  /**
   * 按类型获取模板
   */
  getByType(type: TemplateType): Template[] {
    return this.templatesByType.get(type) || [];
  }

  /**
   * 按分类获取模板
   */
  getByCategory(category: string): Template[] {
    return this.templatesByCategory.get(category) || [];
  }

  /**
   * 搜索模板
   */
  search(query: string): Template[] {
    const lowerQuery = query.toLowerCase();
    return this.getAll().filter(template => 
      template.name.toLowerCase().includes(lowerQuery) ||
      template.description.toLowerCase().includes(lowerQuery) ||
      template.metadata.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 删除模板
   */
  unregister(id: string): boolean {
    const template = this.templates.get(id);
    if (!template) return false;

    this.templates.delete(id);
    
    // 从类型分组中删除
    const typeTemplates = this.templatesByType.get(template.type);
    if (typeTemplates) {
      const index = typeTemplates.findIndex(t => t.id === id);
      if (index !== -1) {
        typeTemplates.splice(index, 1);
      }
    }
    
    // 从分类分组中删除
    const categoryTemplates = this.templatesByCategory.get(template.metadata.category);
    if (categoryTemplates) {
      const index = categoryTemplates.findIndex(t => t.id === id);
      if (index !== -1) {
        categoryTemplates.splice(index, 1);
      }
    }
    
    return true;
  }

  /**
   * 清空所有模板
   */
  clear(): void {
    this.templates.clear();
    this.templatesByType.clear();
    this.templatesByCategory.clear();
  }
}

// 模板解析器
export class TemplateResolver {
  constructor(private registry: TemplateRegistry) {}

  /**
   * 解析模板引用
   */
  resolve(reference: string | TemplateReference): any {
    const refPath = typeof reference === 'string' ? reference : reference.$ref;
    
    // 从路径中提取模板ID
    const templateId = this.extractTemplateId(refPath);
    const template = this.registry.get(templateId);
    
    if (!template) {
      console.warn(`模板未找到: ${refPath}`);
      return null;
    }

    let resolvedData = JSON.parse(JSON.stringify(template.data)); // 深拷贝

    // 应用扩展参数
    if (typeof reference === 'object' && reference.$extend) {
      resolvedData = this.mergeData(resolvedData, reference.$extend);
    }

    // 应用覆盖参数
    if (typeof reference === 'object' && reference.$override) {
      resolvedData = { ...resolvedData, ...reference.$override };
    }

    return resolvedData;
  }

  /**
   * 从路径提取模板ID
   */
  private extractTemplateId(path: string): string {
    // 将 "templates.1.meshes.buildingLevels" 转换为 "meshes.buildingLevels"
    const parts = path.split('.');
    if (parts.length >= 4 && parts[0] === 'templates') {
      return parts.slice(2).join('.');
    }
    return path;
  }

  /**
   * 合并数据
   */
  private mergeData(base: any, extend: any): any {
    if (Array.isArray(base) && Array.isArray(extend)) {
      return [...base, ...extend];
    }
    if (typeof base === 'object' && typeof extend === 'object') {
      return { ...base, ...extend };
    }
    return extend;
  }

  /**
   * 验证模板引用
   */
  validateReference(reference: string | TemplateReference): boolean {
    const refPath = typeof reference === 'string' ? reference : reference.$ref;
    const templateId = this.extractTemplateId(refPath);
    return this.registry.get(templateId) !== undefined;
  }
}

// 模板验证器
export class TemplateValidator {
  /**
   * 验证模板数据
   */
  validate(template: Partial<Template>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!template.name || template.name.trim() === '') {
      errors.push('模板名称不能为空');
    }

    if (!template.type || !Object.values(TemplateType).includes(template.type)) {
      errors.push('模板类型无效');
    }

    if (!template.data) {
      errors.push('模板数据不能为空');
    }

    if (!template.metadata?.category) {
      errors.push('模板分类不能为空');
    }

    // 验证模板数据格式
    if (template.type && template.data) {
      const dataValidation = this.validateTemplateData(template.type, template.data);
      if (!dataValidation.valid) {
        errors.push(...dataValidation.errors);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证特定类型的模板数据
   */
  private validateTemplateData(type: TemplateType, data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (type) {
      case TemplateType.MESH:
        if (!Array.isArray(data)) {
          errors.push('网格模板数据必须是数组');
        } else if (data.some(item => typeof item !== 'string')) {
          errors.push('网格模板数组中的所有项目必须是字符串');
        }
        break;

      case TemplateType.STYLE:
        if (typeof data !== 'object' || data === null) {
          errors.push('样式模板数据必须是对象');
        }
        break;

      case TemplateType.CAMERA:
        if (typeof data !== 'object' || data === null) {
          errors.push('相机模板数据必须是对象');
        } else {
          const requiredFields = ['position', 'target'];
          for (const field of requiredFields) {
            if (!data[field]) {
              errors.push(`相机模板缺少必需字段: ${field}`);
            }
          }
        }
        break;

      case TemplateType.POSITION:
        if (!Array.isArray(data) || data.length !== 3) {
          errors.push('位置模板数据必须是包含3个数字的数组');
        } else if (data.some(item => typeof item !== 'number')) {
          errors.push('位置模板数组中的所有项目必须是数字');
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// 模板管理器主类
export class TemplateManager {
  private static instance: TemplateManager;

  public readonly registry = new TemplateRegistry();
  public readonly resolver = new TemplateResolver(this.registry);
  public readonly validator = new TemplateValidator();

  // 组件化功能
  public readonly configImporter = new ConfigImporter();
  public readonly configExporter = new ConfigExporter();
  public readonly templateExtractor = new TemplateExtractor();

  // 实例追踪
  private instances = reactive<Map<string, TemplateInstance[]>>(new Map());

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): TemplateManager {
    if (!TemplateManager.instance) {
      TemplateManager.instance = new TemplateManager();
    }
    return TemplateManager.instance;
  }

  /**
   * 从配置对象加载模板
   */
  loadFromConfig(config: any): void {
    if (!config.templates) {
      console.warn('配置中没有找到模板定义');
      return;
    }

    this.registry.clear();

    // 使用新的配置导入器
    const importedTemplates = this.configImporter.importTemplates(config);

    // 注册导入的模板
    importedTemplates.forEach(templateData => {
      const result = this.createTemplate(templateData);
      if (!result.success) {
        console.error(`导入模板失败: ${templateData.name}`, result.errors);
      }
    });

    console.log(`已加载 ${this.registry.getAll().length} 个模板`);
  }

  /**
   * 加载特定版本的模板
   */
  private loadVersionTemplates(version: string, versionTemplates: any): void {
    Object.entries(versionTemplates).forEach(([category, categoryTemplates]: [string, any]) => {
      this.loadCategoryTemplates(version, category, categoryTemplates);
    });
  }

  /**
   * 加载特定分类的模板
   */
  private loadCategoryTemplates(version: string, category: string, categoryTemplates: any): void {
    const templateType = this.mapCategoryToType(category);
    
    Object.entries(categoryTemplates).forEach(([name, data]: [string, any]) => {
      const template: Template = {
        id: `${category}.${name}`,
        name,
        type: templateType,
        description: this.generateDescription(templateType, name, data),
        data,
        metadata: {
          tags: this.generateTags(templateType, name),
          author: 'system',
          usage: 0,
          dependencies: this.extractDependencies(data),
          category: this.formatCategory(category),
          sourceType: 'config-imported',
        },
        version,
        path: `templates.${version}.${category}.${name}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.registry.register(template);
    });
  }

  /**
   * 映射分类到模板类型
   */
  private mapCategoryToType(category: string): TemplateType {
    const mapping: Record<string, TemplateType> = {
      'meshes': TemplateType.MESH,
      'styles': TemplateType.STYLE,
      'cameras': TemplateType.CAMERA,
      'actions': TemplateType.ACTION,
      'labels': TemplateType.LABEL,
      'positions': TemplateType.POSITION,
      'interactions': TemplateType.INTERACTION,
    };
    return mapping[category] || TemplateType.ACTION;
  }

  /**
   * 生成模板描述
   */
  private generateDescription(type: TemplateType, name: string, data: any): string {
    switch (type) {
      case TemplateType.MESH:
        return `网格集合模板，包含 ${Array.isArray(data) ? data.length : 0} 个网格`;
      case TemplateType.STYLE:
        return `样式模板：${name}`;
      case TemplateType.CAMERA:
        return `相机配置模板：${name}`;
      case TemplateType.ACTION:
        return `动作模板：${name}`;
      case TemplateType.LABEL:
        return `标签模板：${name}`;
      case TemplateType.POSITION:
        return `位置模板：${name}`;
      default:
        return `${name} 模板`;
    }
  }

  /**
   * 生成模板标签
   */
  private generateTags(type: TemplateType, name: string): string[] {
    const baseTags = [type.toString()];

    // 根据名称添加额外标签
    if (name.includes('building')) baseTags.push('建筑');
    if (name.includes('floor')) baseTags.push('楼层');
    if (name.includes('door')) baseTags.push('门');
    if (name.includes('device')) baseTags.push('设备');
    if (name.includes('gis')) baseTags.push('GIS');

    return baseTags;
  }

  /**
   * 提取模板依赖
   */
  private extractDependencies(data: any): string[] {
    const dependencies: string[] = [];
    
    const extractRefs = (obj: any) => {
      if (typeof obj === 'object' && obj !== null) {
        if (obj.$ref) {
          dependencies.push(obj.$ref);
        }
        Object.values(obj).forEach(value => extractRefs(value));
      }
    };
    
    extractRefs(data);
    return dependencies;
  }

  /**
   * 格式化分类名称
   */
  private formatCategory(category: string): string {
    const mapping: Record<string, string> = {
      'meshes': '网格',
      'styles': '样式',
      'cameras': '相机',
      'actions': '动作',
      'labels': '标签',
      'positions': '位置',
      'interactions': '交互',
    };
    return mapping[category] || category;
  }

  /**
   * 创建新模板
   */
  createTemplate(templateData: Partial<Template>): { success: boolean; template?: Template; errors?: string[] } {
    const validation = this.validator.validate(templateData);
    if (!validation.valid) {
      return { success: false, errors: validation.errors };
    }

    const template: Template = {
      id: templateData.id || `${templateData.type}.${Date.now()}`,
      name: templateData.name!,
      type: templateData.type!,
      description: templateData.description || '',
      data: templateData.data,
      metadata: {
        tags: templateData.metadata?.tags || [],
        author: templateData.metadata?.author || 'user',
        usage: 0,
        dependencies: templateData.metadata?.dependencies || [],
        category: templateData.metadata?.category || '自定义',
        sourceType: templateData.metadata?.sourceType || 'user-created',
      },
      version: templateData.version || '1',
      path: templateData.path || `templates.1.custom.${templateData.name}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.registry.register(template);
    return { success: true, template };
  }

  /**
   * 更新模板
   */
  updateTemplate(id: string, updates: Partial<Template>): { success: boolean; template?: Template; errors?: string[] } {
    const existingTemplate = this.registry.get(id);
    if (!existingTemplate) {
      return { success: false, errors: ['模板不存在'] };
    }

    const updatedTemplate = { ...existingTemplate, ...updates, updatedAt: new Date() };
    const validation = this.validator.validate(updatedTemplate);
    
    if (!validation.valid) {
      return { success: false, errors: validation.errors };
    }

    this.registry.register(updatedTemplate);
    return { success: true, template: updatedTemplate };
  }

  /**
   * 删除模板
   */
  deleteTemplate(id: string): boolean {
    return this.registry.unregister(id);
  }

  /**
   * 获取模板使用统计
   */
  getUsageStats(): { totalTemplates: number; byType: Record<TemplateType, number>; byCategory: Record<string, number> } {
    const templates = this.registry.getAll();
    const byType: Record<TemplateType, number> = {} as any;
    const byCategory: Record<string, number> = {};

    templates.forEach(template => {
      byType[template.type] = (byType[template.type] || 0) + 1;
      byCategory[template.metadata.category] = (byCategory[template.metadata.category] || 0) + 1;
    });

    return {
      totalTemplates: templates.length,
      byType,
      byCategory,
    };
  }

  // ==================== 组件化功能 ====================

  /**
   * 从节点创建模板
   */
  createTemplateFromNode(
    node: Cell,
    templateName: string,
    description: string = '',
    category: string = '自定义'
  ): TemplateManagerResult<Template> {
    try {
      // 使用模板提取器从节点提取模板
      const templateData = this.templateExtractor.extractFromNode(node, templateName, description, category);

      // 检查config.js兼容性
      if (!this.templateExtractor.checkConfigCompatibility(templateData)) {
        return {
          success: false,
          errors: ['模板不兼容config.js格式，包含不支持的属性']
        };
      }

      // 创建模板
      return this.createTemplate(templateData);
    } catch (error) {
      return {
        success: false,
        errors: [`从节点创建模板失败: ${error}`]
      };
    }
  }

  /**
   * 创建模板实例
   */
  createTemplateInstance(
    templateId: string,
    nodeId: string,
    sceneId: string,
    overrides: Record<string, any> = {}
  ): TemplateManagerResult<TemplateInstance> {
    try {
      const template = this.registry.get(templateId);
      if (!template) {
        return {
          success: false,
          errors: [`模板不存在: ${templateId}`]
        };
      }

      // 创建实例
      const instance: TemplateInstance = {
        id: `${templateId}_${nodeId}_${Date.now()}`,
        templateId,
        templateVersion: template.version,
        nodeId,
        sceneId,
        overrides,
        syncStatus: 'synced',
        lastSyncAt: new Date(),
        configRef: template.definition?.configPath ? {
          path: template.definition.configPath,
          isReference: true
        } : undefined
      };

      // 注册实例
      if (!this.instances.has(templateId)) {
        this.instances.set(templateId, []);
      }
      this.instances.get(templateId)!.push(instance);

      return {
        success: true,
        data: instance
      };
    } catch (error) {
      return {
        success: false,
        errors: [`创建模板实例失败: ${error}`]
      };
    }
  }

  /**
   * 获取模板的所有实例
   */
  getTemplateInstances(templateId: string): TemplateInstance[] {
    return this.instances.get(templateId) || [];
  }

  /**
   * 更新模板实例
   */
  updateTemplateInstance(instanceId: string, overrides: Record<string, any>): TemplateManagerResult<TemplateInstance> {
    try {
      // 查找实例
      let foundInstance: TemplateInstance | null = null;

      for (const instances of this.instances.values()) {
        const instance = instances.find(inst => inst.id === instanceId);
        if (instance) {
          foundInstance = instance;
          break;
        }
      }

      if (!foundInstance) {
        return {
          success: false,
          errors: [`实例不存在: ${instanceId}`]
        };
      }

      // 更新实例
      foundInstance.overrides = { ...foundInstance.overrides, ...overrides };
      foundInstance.lastSyncAt = new Date();
      foundInstance.syncStatus = 'synced';

      return {
        success: true,
        data: foundInstance
      };
    } catch (error) {
      return {
        success: false,
        errors: [`更新模板实例失败: ${error}`]
      };
    }
  }

  /**
   * 同步模板更新到所有实例
   */
  syncTemplateToInstances(templateId: string): TemplateManagerResult<TemplateInstance[]> {
    try {
      const instances = this.getTemplateInstances(templateId);
      const template = this.registry.get(templateId);

      if (!template) {
        return {
          success: false,
          errors: [`模板不存在: ${templateId}`]
        };
      }

      // 更新所有实例
      instances.forEach(instance => {
        instance.templateVersion = template.version;
        instance.syncStatus = 'synced';
        instance.lastSyncAt = new Date();
      });

      return {
        success: true,
        data: instances
      };
    } catch (error) {
      return {
        success: false,
        errors: [`同步模板到实例失败: ${error}`]
      };
    }
  }

  /**
   * 导出模板到config.js格式
   */
  exportToConfig(templateIds?: string[]): any {
    const templates = templateIds
      ? templateIds.map(id => this.registry.get(id)).filter(Boolean) as Template[]
      : this.registry.getAll();

    return this.configExporter.exportTemplates(templates);
  }

  /**
   * 导出实例到config.js格式
   */
  exportInstancesToConfig(sceneId?: string): any {
    let allInstances: TemplateInstance[] = [];

    for (const instances of this.instances.values()) {
      if (sceneId) {
        allInstances.push(...instances.filter(inst => inst.sceneId === sceneId));
      } else {
        allInstances.push(...instances);
      }
    }

    return this.configExporter.exportInstances(allInstances);
  }

  /**
   * 解析config.js引用
   */
  resolveConfigReferences(obj: any, config: any): any {
    return this.configImporter.resolveReferences(obj, config);
  }
}
