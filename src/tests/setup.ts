/**
 * setup.ts
 * 测试环境配置和工具函数
 */

// import { vi } from 'vitest'; // 仅在测试环境中使用
import { Template, TemplateType, TemplateInstance } from '../types/TemplateTypes';

/**
 * 测试工具类
 */
export class TestUtils {
  /**
   * 创建测试模板
   */
  static createTestTemplate(overrides: Partial<Template> = {}): Template {
    const baseTemplate: Template = {
      id: `test-template-${Date.now()}`,
      name: '测试模板',
      type: TemplateType.MESH,
      version: '1.0.0',
      description: '这是一个测试模板',
      data: ['test-mesh-1', 'test-mesh-2'],
      definition: {
        parameters: [
          {
            name: 'color',
            type: 'string',
            defaultValue: '#ffffff',
            required: false,
            description: '颜色参数'
          },
          {
            name: 'size',
            type: 'number',
            defaultValue: 1.0,
            required: true,
            description: '大小参数'
          }
        ],
        dependencies: [],
        validation: {
          required: ['size'],
          constraints: {
            size: { min: 0.1, max: 10.0 }
          }
        }
      },
      metadata: {
        category: '测试分类',
        tags: ['test', 'mesh'],
        author: 'test-user',
        createdAt: new Date(),
        updatedAt: new Date(),
        usageCount: 0,
        isBuiltIn: false,
        dependencies: [],
        sourceType: 'user-created'
      },
      ...overrides
    };

    return baseTemplate;
  }

  /**
   * 创建测试实例
   */
  static createTestInstance(templateId: string, overrides: Partial<TemplateInstance> = {}): TemplateInstance {
    const baseInstance: TemplateInstance = {
      id: `test-instance-${Date.now()}`,
      templateId,
      sceneId: 'test-scene',
      nodeId: 'test-node',
      parameters: {
        color: '#ff0000',
        size: 2.0
      },
      overrides: {
        color: '#ff0000'
      },
      status: 'active',
      syncStatus: 'synced',
      createdAt: new Date(),
      lastSyncAt: new Date(),
      ...overrides
    };

    return baseInstance;
  }

  /**
   * 创建多个测试模板
   */
  static createTestTemplates(count: number): Template[] {
    return Array.from({ length: count }, (_, index) => 
      this.createTestTemplate({
        id: `test-template-${index}`,
        name: `测试模板 ${index + 1}`,
        type: index % 2 === 0 ? TemplateType.MESH : TemplateType.STYLE,
        metadata: {
          ...this.createTestTemplate().metadata,
          category: index % 3 === 0 ? '网格' : index % 3 === 1 ? '样式' : '动作',
          tags: [`tag-${index}`, 'test'],
          usageCount: Math.floor(Math.random() * 100)
        }
      })
    );
  }

  /**
   * 创建多个测试实例
   */
  static createTestInstances(templateId: string, count: number): TemplateInstance[] {
    return Array.from({ length: count }, (_, index) => 
      this.createTestInstance(templateId, {
        id: `test-instance-${templateId}-${index}`,
        nodeId: `test-node-${index}`,
        status: index % 4 === 0 ? 'inactive' : 'active',
        syncStatus: index % 3 === 0 ? 'outdated' : 'synced'
      })
    );
  }

  /**
   * 模拟异步延迟
   */
  static async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建模拟的DOM元素
   */
  static createMockElement(tagName: string = 'div'): HTMLElement {
    const element = document.createElement(tagName);
    element.getBoundingClientRect = () => ({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
      top: 0,
      right: 100,
      bottom: 100,
      left: 0,
      toJSON: () => ({})
    });
    return element;
  }

  /**
   * 创建模拟的拖拽事件
   */
  static createMockDragEvent(type: string, data?: any): DragEvent {
    const event = new DragEvent(type, {
      bubbles: true,
      cancelable: true
    });

    // 模拟 dataTransfer
    const mockDataTransfer = {
      setData: () => {},
      getData: () => JSON.stringify(data || {}),
      effectAllowed: 'copy',
      dropEffect: 'copy',
      files: [],
      items: [],
      types: []
    };

    Object.defineProperty(event, 'dataTransfer', {
      value: mockDataTransfer,
      writable: false
    });

    return event;
  }

  /**
   * 验证模板对象的完整性
   */
  static validateTemplate(template: Template): boolean {
    const requiredFields = ['id', 'name', 'type', 'version', 'data', 'definition', 'metadata'];
    
    for (const field of requiredFields) {
      if (!(field in template)) {
        console.error(`模板缺少必需字段: ${field}`);
        return false;
      }
    }

    // 验证参数定义
    if (template.definition.parameters) {
      for (const param of template.definition.parameters) {
        if (!param.name || !param.type) {
          console.error(`参数定义不完整:`, param);
          return false;
        }
      }
    }

    // 验证元数据
    const requiredMetadata = ['category', 'tags', 'createdAt', 'updatedAt'];
    for (const field of requiredMetadata) {
      if (!(field in template.metadata)) {
        console.error(`模板元数据缺少必需字段: ${field}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 验证实例对象的完整性
   */
  static validateInstance(instance: TemplateInstance): boolean {
    const requiredFields = ['id', 'templateId', 'sceneId', 'nodeId', 'parameters', 'status', 'syncStatus', 'createdAt'];
    
    for (const field of requiredFields) {
      if (!(field in instance)) {
        console.error(`实例缺少必需字段: ${field}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 比较两个对象是否深度相等
   */
  static deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;
    
    if (obj1 == null || obj2 == null) return false;
    
    if (typeof obj1 !== typeof obj2) return false;
    
    if (typeof obj1 !== 'object') return obj1 === obj2;
    
    if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!this.deepEqual(obj1[key], obj2[key])) return false;
    }
    
    return true;
  }

  /**
   * 生成随机字符串
   */
  static randomString(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成随机数字
   */
  static randomNumber(min: number = 0, max: number = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 清理测试数据
   */
  static cleanup(): void {
    // 清理本地存储
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('test-') || key.startsWith('template-test-')) {
          localStorage.removeItem(key);
        }
      });
    }

    // 清理会话存储
    if (typeof sessionStorage !== 'undefined') {
      const keys = Object.keys(sessionStorage);
      keys.forEach(key => {
        if (key.startsWith('test-') || key.startsWith('template-test-')) {
          sessionStorage.removeItem(key);
        }
      });
    }
  }
}

/**
 * 测试断言工具
 */
export class TestAssertions {
  /**
   * 断言值为真
   */
  static assertTrue(value: any, message?: string): void {
    if (!value) {
      throw new Error(message || `期望值为真，但得到: ${value}`);
    }
  }

  /**
   * 断言值为假
   */
  static assertFalse(value: any, message?: string): void {
    if (value) {
      throw new Error(message || `期望值为假，但得到: ${value}`);
    }
  }

  /**
   * 断言相等
   */
  static assertEqual(actual: any, expected: any, message?: string): void {
    if (!TestUtils.deepEqual(actual, expected)) {
      throw new Error(message || `期望 ${JSON.stringify(expected)}，但得到 ${JSON.stringify(actual)}`);
    }
  }

  /**
   * 断言不相等
   */
  static assertNotEqual(actual: any, expected: any, message?: string): void {
    if (TestUtils.deepEqual(actual, expected)) {
      throw new Error(message || `期望值不等于 ${JSON.stringify(expected)}，但实际相等`);
    }
  }

  /**
   * 断言抛出异常
   */
  static async assertThrows(fn: () => any, message?: string): Promise<void> {
    try {
      await fn();
      throw new Error(message || '期望抛出异常，但没有抛出');
    } catch (error) {
      // 期望的异常
    }
  }

  /**
   * 断言不抛出异常
   */
  static async assertNotThrows(fn: () => any, message?: string): Promise<void> {
    try {
      await fn();
    } catch (error) {
      throw new Error(message || `期望不抛出异常，但抛出了: ${error}`);
    }
  }

  /**
   * 断言数组包含元素
   */
  static assertContains<T>(array: T[], element: T, message?: string): void {
    if (!array.includes(element)) {
      throw new Error(message || `数组不包含元素: ${JSON.stringify(element)}`);
    }
  }

  /**
   * 断言数组不包含元素
   */
  static assertNotContains<T>(array: T[], element: T, message?: string): void {
    if (array.includes(element)) {
      throw new Error(message || `数组包含不应该存在的元素: ${JSON.stringify(element)}`);
    }
  }

  /**
   * 断言数组长度
   */
  static assertLength(array: any[], expectedLength: number, message?: string): void {
    if (array.length !== expectedLength) {
      throw new Error(message || `期望数组长度为 ${expectedLength}，但实际为 ${array.length}`);
    }
  }

  /**
   * 断言对象有属性
   */
  static assertHasProperty(obj: any, property: string, message?: string): void {
    if (!(property in obj)) {
      throw new Error(message || `对象缺少属性: ${property}`);
    }
  }

  /**
   * 断言对象没有属性
   */
  static assertNotHasProperty(obj: any, property: string, message?: string): void {
    if (property in obj) {
      throw new Error(message || `对象不应该有属性: ${property}`);
    }
  }
}

// 全局测试配置
export const testConfig = {
  timeout: 5000,
  retries: 3,
  verbose: true
};

// 导出便捷别名
export const { createTestTemplate, createTestInstance, createTestTemplates, createTestInstances } = TestUtils;
export const { assertTrue, assertFalse, assertEqual, assertNotEqual } = TestAssertions;
