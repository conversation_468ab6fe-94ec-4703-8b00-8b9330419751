{"name": "ddd-flow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-vue-shape": "^2.1.2", "vue": "^3.3.11"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "typescript": "^5.2.2", "vite": "^5.0.8", "vue-tsc": "^1.8.25"}}