/**
 * TemplateSystemDemo.ts
 * 模板系统完整功能演示
 */

import { TemplateManager } from '../services/TemplateManager';
import { nodeSelector } from '../services/NodeSelector';
import { templateInstantiator } from '../services/TemplateInstantiator';
import { instanceTracker } from '../services/InstanceTracker';
import { dragDropManager } from '../services/DragDropManager';

/**
 * 模板系统完整演示
 */
export class TemplateSystemDemo {
  private templateManager: TemplateManager;

  constructor() {
    this.templateManager = TemplateManager.getInstance();
  }

  /**
   * 运行完整演示
   */
  async runCompleteDemo(): Promise<void> {
    console.log('🚀 开始模板系统完整功能演示');
    console.log('='.repeat(60));

    try {
      // 1. 基础功能演示
      await this.demoBasicFeatures();
      
      // 2. 节点转模板演示
      await this.demoNodeToTemplate();
      
      // 3. 模板实例化演示
      await this.demoTemplateInstantiation();
      
      // 4. 实例追踪演示
      await this.demoInstanceTracking();
      
      // 5. 拖拽功能演示
      await this.demoDragDrop();
      
      // 6. 配置导入导出演示
      await this.demoConfigImportExport();
      
      // 7. 高级功能演示
      await this.demoAdvancedFeatures();

      console.log('='.repeat(60));
      console.log('✅ 模板系统完整功能演示完成！');
      
      // 显示最终统计
      this.showFinalStats();
      
    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 基础功能演示
   */
  private async demoBasicFeatures(): Promise<void> {
    console.log('\n📦 1. 基础功能演示');
    console.log('-'.repeat(40));

    // 显示当前模板统计
    const stats = this.templateManager.getUsageStats();
    console.log(`当前模板数量: ${stats.totalTemplates}`);
    console.log('按类型分布:', stats.byType);
    console.log('按分类分布:', stats.byCategory);

    // 获取所有模板
    const allTemplates = this.templateManager.getAllTemplates();
    console.log(`\n模板列表 (${allTemplates.length} 个):`);
    allTemplates.forEach((template, index) => {
      console.log(`  ${index + 1}. ${template.name} (${template.type}) - ${template.metadata.category}`);
    });
  }

  /**
   * 节点转模板演示
   */
  private async demoNodeToTemplate(): Promise<void> {
    console.log('\n🔄 2. 节点转模板演示');
    console.log('-'.repeat(40));

    // 创建演示节点
    const demoNode = nodeSelector.createMockNode('button');
    console.log('创建演示节点:', nodeSelector.getNodeDisplayInfo(demoNode));

    // 从节点创建模板
    const result = this.templateManager.createTemplateFromNode(
      demoNode,
      '演示按钮模板_' + Date.now(),
      '从演示节点创建的按钮模板',
      '演示'
    );

    if (result.success && result.data) {
      console.log('✅ 节点转模板成功:');
      console.log(`  模板ID: ${result.data.id}`);
      console.log(`  模板名称: ${result.data.name}`);
      console.log(`  参数数量: ${result.data.definition?.parameters.length || 0}`);
      console.log(`  依赖数量: ${result.data.metadata.dependencies.length}`);
    } else {
      console.log('❌ 节点转模板失败:', result.errors);
    }
  }

  /**
   * 模板实例化演示
   */
  private async demoTemplateInstantiation(): Promise<void> {
    console.log('\n🏗️ 3. 模板实例化演示');
    console.log('-'.repeat(40));

    const allTemplates = this.templateManager.getAllTemplates();
    if (allTemplates.length === 0) {
      console.log('❌ 没有可用的模板');
      return;
    }

    // 选择第一个模板进行实例化
    const template = allTemplates[0];
    console.log(`实例化模板: ${template.name}`);

    // 单个实例化
    const singleResult = this.templateManager.instantiateTemplate(template.id, {
      position: { x: 100, y: 100 },
      sceneId: 'demo-scene',
      parameterOverrides: {
        backgroundColor: '#ff6b6b',
        textColor: '#ffffff'
      }
    });

    if (singleResult.success && singleResult.data) {
      console.log('✅ 单个实例化成功:');
      console.log(`  节点ID: ${singleResult.data.node.id}`);
      console.log(`  实例ID: ${singleResult.data.instance.id}`);
    }

    // 批量实例化
    const batchRequests = allTemplates.slice(0, 3).map((tmpl, index) => ({
      templateId: tmpl.id,
      options: {
        position: { x: 200 + index * 150, y: 200 },
        sceneId: 'demo-scene'
      }
    }));

    const batchResult = this.templateManager.instantiateMultipleTemplates(batchRequests);
    if (batchResult.success && batchResult.data) {
      console.log(`✅ 批量实例化成功: ${batchResult.data.length} 个实例`);
    }
  }

  /**
   * 实例追踪演示
   */
  private async demoInstanceTracking(): Promise<void> {
    console.log('\n📊 4. 实例追踪演示');
    console.log('-'.repeat(40));

    // 获取实例统计
    const instanceStats = this.templateManager.getInstanceStats();
    console.log('实例统计:');
    console.log(`  总数: ${instanceStats.total}`);
    console.log(`  按模板分布:`, instanceStats.byTemplate);
    console.log(`  按场景分布:`, instanceStats.byScene);
    console.log(`  按状态分布:`, instanceStats.byStatus);

    // 查询实例
    const allInstances = this.templateManager.queryInstances();
    console.log(`\n所有实例 (${allInstances.length} 个):`);
    allInstances.forEach((instance, index) => {
      console.log(`  ${index + 1}. ${instance.id} - 模板: ${instance.templateId} - 场景: ${instance.sceneId}`);
    });

    // 演示实例同步
    if (allInstances.length > 0) {
      const firstInstance = allInstances[0];
      const syncResult = this.templateManager.syncTemplateInstances(firstInstance.templateId);
      if (syncResult.success) {
        console.log(`✅ 同步模板实例: ${syncResult.data} 个实例已同步`);
      }
    }
  }

  /**
   * 拖拽功能演示
   */
  private async demoDragDrop(): Promise<void> {
    console.log('\n🎯 5. 拖拽功能演示');
    console.log('-'.repeat(40));

    // 启用拖拽功能
    this.templateManager.enableTemplateDragging('.app-container');
    console.log('✅ 拖拽功能已启用');

    // 模拟拖拽事件
    const allTemplates = this.templateManager.getAllTemplates();
    if (allTemplates.length > 0) {
      const template = allTemplates[0];
      console.log(`模拟拖拽模板: ${template.name}`);
      
      // 这里在实际应用中会有真实的拖拽交互
      console.log('💡 在实际应用中，用户可以:');
      console.log('  1. 从模板面板拖拽模板项');
      console.log('  2. 拖拽到画布区域');
      console.log('  3. 自动创建模板实例');
      console.log('  4. 实例会出现在拖拽位置');
    }
  }

  /**
   * 配置导入导出演示
   */
  private async demoConfigImportExport(): Promise<void> {
    console.log('\n📤 6. 配置导入导出演示');
    console.log('-'.repeat(40));

    // 导出模板配置
    const templateConfig = this.templateManager.exportToConfig();
    console.log('模板配置导出:');
    if (templateConfig.templates) {
      Object.keys(templateConfig.templates).forEach(sceneId => {
        const sceneTemplates = templateConfig.templates[sceneId];
        console.log(`  场景 ${sceneId}:`);
        Object.keys(sceneTemplates).forEach(typeKey => {
          const typeTemplates = sceneTemplates[typeKey];
          const count = Object.keys(typeTemplates).length;
          console.log(`    ${typeKey}: ${count} 个模板`);
        });
      });
    }

    // 导出实例配置
    const instanceConfig = this.templateManager.exportInstancesToConfig();
    console.log('\n实例配置导出:');
    if (instanceConfig.scenes && Object.keys(instanceConfig.scenes).length > 0) {
      Object.keys(instanceConfig.scenes).forEach(sceneId => {
        const sceneInstances = instanceConfig.scenes[sceneId];
        const instanceCount = Object.keys(sceneInstances).length;
        console.log(`  场景 ${sceneId}: ${instanceCount} 个实例`);
      });
    } else {
      console.log('  暂无实例配置');
    }

    // 生成配置文件
    const configFileContent = this.templateManager.configExporter.generateConfigFile(templateConfig);
    console.log(`\n配置文件大小: ${configFileContent.length} 字符`);
    console.log('配置文件预览 (前100字符):');
    console.log(configFileContent.substring(0, 100) + '...');
  }

  /**
   * 高级功能演示
   */
  private async demoAdvancedFeatures(): Promise<void> {
    console.log('\n🚀 7. 高级功能演示');
    console.log('-'.repeat(40));

    // 演示参数解析器
    console.log('参数解析器演示:');
    templateInstantiator.addParameterResolver('customColor', (value: any) => {
      if (typeof value === 'string' && value.startsWith('custom:')) {
        return value.replace('custom:', '#');
      }
      return value;
    });
    console.log('✅ 自定义参数解析器已添加');

    // 演示依赖解析器
    templateInstantiator.setDependencyResolver((path: string) => {
      console.log(`🔗 解析依赖: ${path}`);
      return { resolved: true, path };
    });
    console.log('✅ 依赖解析器已设置');

    // 演示事件监听
    instanceTracker.addEventListener('created', (event) => {
      console.log(`📝 实例创建事件: ${event.instanceId}`);
    });
    console.log('✅ 实例事件监听器已添加');

    // 演示拖拽事件监听
    dragDropManager.addEventListener('drop', (event) => {
      console.log(`🎯 拖拽放置事件: ${event.data.template.name} at (${event.position.x}, ${event.position.y})`);
    });
    console.log('✅ 拖拽事件监听器已添加');
  }

  /**
   * 显示最终统计
   */
  private showFinalStats(): void {
    console.log('\n📈 最终统计信息');
    console.log('-'.repeat(40));

    // 模板统计
    const templateStats = this.templateManager.getUsageStats();
    console.log('模板统计:');
    console.log(`  总数: ${templateStats.totalTemplates}`);
    console.log(`  类型分布: ${Object.keys(templateStats.byType).length} 种类型`);
    console.log(`  分类分布: ${Object.keys(templateStats.byCategory).length} 个分类`);

    // 实例统计
    const instanceStats = this.templateManager.getInstanceStats();
    console.log('\n实例统计:');
    console.log(`  总数: ${instanceStats.total}`);
    console.log(`  活跃实例: ${instanceTracker.activeInstanceCount}`);
    console.log(`  过期实例: ${instanceTracker.outdatedInstanceCount}`);

    // 系统状态
    console.log('\n系统状态:');
    console.log(`  ✅ 模板管理器: 已初始化`);
    console.log(`  ✅ 实例化引擎: 已初始化`);
    console.log(`  ✅ 实例追踪器: 已初始化`);
    console.log(`  ✅ 拖拽管理器: 已初始化`);
    console.log(`  ✅ 配置导入导出: 已初始化`);
  }

  /**
   * 清理演示数据
   */
  clearDemoData(): void {
    console.log('🧹 清理演示数据...');
    
    // 清除节点选择
    nodeSelector.clearSelection();
    
    // 清理事件历史
    instanceTracker.cleanupEvents();
    
    console.log('✅ 演示数据清理完成');
  }

  /**
   * 获取功能清单
   */
  getFeatureList(): string[] {
    return [
      '✅ 模板创建和管理',
      '✅ 节点转模板功能',
      '✅ 模板实例化引擎',
      '✅ 实例追踪系统',
      '✅ 拖拽创建功能',
      '✅ 配置导入导出',
      '✅ 参数化和依赖解析',
      '✅ 事件系统',
      '✅ 版本控制',
      '✅ config.js兼容性',
      '✅ 深色主题UI',
      '✅ 响应式设计'
    ];
  }

  /**
   * 获取使用指南
   */
  getUsageGuide(): string[] {
    return [
      '🎯 基本使用:',
      '  1. 在模板面板查看现有模板',
      '  2. 点击"从节点创建"按钮创建新模板',
      '  3. 拖拽模板到画布创建实例',
      '',
      '🔧 高级功能:',
      '  1. 使用参数覆盖自定义实例',
      '  2. 通过实例追踪管理模板关联',
      '  3. 导出配置到config.js格式',
      '',
      '💡 开发者功能:',
      '  1. 运行 templateSystemDemo.runCompleteDemo() 查看完整演示',
      '  2. 使用 debugTemplateSystem() 查看系统状态',
      '  3. 查看控制台输出了解系统运行情况'
    ];
  }
}

// 导出演示实例
export const templateSystemDemo = new TemplateSystemDemo();

// 如果在浏览器环境中，将演示添加到全局对象
if (typeof window !== 'undefined') {
  (window as any).templateSystemDemo = templateSystemDemo;
  console.log('💡 完整演示已添加到全局对象，可以通过 window.templateSystemDemo 访问');
  console.log('💡 运行 window.templateSystemDemo.runCompleteDemo() 开始完整演示');
  
  // 显示功能清单
  console.log('\n🎉 模板系统功能清单:');
  templateSystemDemo.getFeatureList().forEach(feature => console.log(feature));
  
  // 显示使用指南
  console.log('\n📖 使用指南:');
  templateSystemDemo.getUsageGuide().forEach(guide => console.log(guide));
}
