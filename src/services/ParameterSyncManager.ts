/**
 * ParameterSyncManager.ts
 * 参数同步管理器 - 处理模板参数变更检测和同步更新
 */

import { reactive, computed } from 'vue';
import { Template, TemplateInstance, TemplateParameter } from './TemplateManager';
import { instanceTracker } from './InstanceTracker';

/**
 * 参数变更类型
 */
export type ParameterChangeType = 
  | 'added'      // 新增参数
  | 'removed'    // 删除参数
  | 'modified'   // 修改参数
  | 'renamed'    // 重命名参数
  | 'type_changed'; // 类型变更

/**
 * 参数变更记录
 */
export interface ParameterChange {
  id: string;
  templateId: string;
  templateVersion: string;
  changeType: ParameterChangeType;
  parameterName: string;
  oldValue?: any;
  newValue?: any;
  oldType?: string;
  newType?: string;
  timestamp: Date;
  description: string;
}

/**
 * 冲突类型
 */
export type ConflictType = 
  | 'parameter_removed'     // 参数被删除但实例中有覆盖值
  | 'type_mismatch'        // 参数类型不匹配
  | 'value_incompatible'   // 值不兼容
  | 'dependency_missing'   // 依赖缺失
  | 'custom_override';     // 自定义覆盖冲突

/**
 * 参数冲突
 */
export interface ParameterConflict {
  id: string;
  instanceId: string;
  templateId: string;
  parameterName: string;
  conflictType: ConflictType;
  templateValue: any;
  instanceValue: any;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  autoResolvable: boolean;
  suggestedResolution?: string;
  timestamp: Date;
}

/**
 * 同步策略
 */
export type SyncStrategy = 
  | 'auto_update'          // 自动更新
  | 'preserve_overrides'   // 保留覆盖值
  | 'prompt_user'          // 提示用户
  | 'create_backup'        // 创建备份
  | 'merge_changes';       // 合并变更

/**
 * 同步选项
 */
export interface SyncOptions {
  strategy: SyncStrategy;
  batchSize?: number;
  preserveUserOverrides?: boolean;
  createBackup?: boolean;
  notifyUsers?: boolean;
  dryRun?: boolean;
}

/**
 * 同步结果
 */
export interface SyncResult {
  success: boolean;
  totalInstances: number;
  updatedInstances: number;
  skippedInstances: number;
  conflictedInstances: number;
  conflicts: ParameterConflict[];
  changes: ParameterChange[];
  errors: string[];
  warnings: string[];
  duration: number;
}

/**
 * 参数同步管理器
 */
export class ParameterSyncManager {
  private static instance: ParameterSyncManager;
  
  // 变更历史
  private changeHistory = reactive<ParameterChange[]>([]);
  
  // 冲突记录
  private conflicts = reactive<Map<string, ParameterConflict>>(new Map());
  
  // 同步状态
  private syncInProgress = reactive<Map<string, boolean>>(new Map());
  
  // 事件监听器
  private eventListeners: Map<string, ((data: any) => void)[]> = new Map();

  private constructor() {
    this.initializeEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ParameterSyncManager {
    if (!ParameterSyncManager.instance) {
      ParameterSyncManager.instance = new ParameterSyncManager();
    }
    return ParameterSyncManager.instance;
  }

  /**
   * 检测模板参数变更
   */
  detectParameterChanges(
    oldTemplate: Template, 
    newTemplate: Template
  ): ParameterChange[] {
    console.log(`🔍 检测模板参数变更: ${newTemplate.name}`);
    
    const changes: ParameterChange[] = [];
    const oldParams = this.normalizeParameters(oldTemplate.definition?.parameters || []);
    const newParams = this.normalizeParameters(newTemplate.definition?.parameters || []);
    
    // 检测新增参数
    for (const [name, newParam] of newParams) {
      if (!oldParams.has(name)) {
        changes.push(this.createParameterChange(
          newTemplate,
          'added',
          name,
          undefined,
          newParam,
          `新增参数: ${name}`
        ));
      }
    }
    
    // 检测删除和修改的参数
    for (const [name, oldParam] of oldParams) {
      const newParam = newParams.get(name);
      
      if (!newParam) {
        // 参数被删除
        changes.push(this.createParameterChange(
          newTemplate,
          'removed',
          name,
          oldParam,
          undefined,
          `删除参数: ${name}`
        ));
      } else {
        // 检测参数修改
        const paramChanges = this.detectParameterModifications(
          newTemplate,
          name,
          oldParam,
          newParam
        );
        changes.push(...paramChanges);
      }
    }
    
    // 记录变更历史
    changes.forEach(change => this.recordChange(change));
    
    console.log(`✅ 检测完成，发现 ${changes.length} 个变更`);
    return changes;
  }

  /**
   * 检测参数冲突
   */
  detectConflicts(
    templateId: string, 
    changes: ParameterChange[]
  ): ParameterConflict[] {
    console.log(`🔍 检测参数冲突: ${templateId}`);
    
    const conflicts: ParameterConflict[] = [];
    const instances = instanceTracker.getTemplateInstances(templateId);
    
    for (const instance of instances) {
      for (const change of changes) {
        const conflict = this.checkInstanceConflict(instance, change);
        if (conflict) {
          conflicts.push(conflict);
          this.conflicts.set(conflict.id, conflict);
        }
      }
    }
    
    console.log(`✅ 冲突检测完成，发现 ${conflicts.length} 个冲突`);
    return conflicts;
  }

  /**
   * 同步模板参数到实例
   */
  async syncParametersToInstances(
    templateId: string,
    options: SyncOptions = { strategy: 'prompt_user' }
  ): Promise<SyncResult> {
    console.log(`🔄 开始同步模板参数: ${templateId}`);
    
    const startTime = Date.now();
    const result: SyncResult = {
      success: false,
      totalInstances: 0,
      updatedInstances: 0,
      skippedInstances: 0,
      conflictedInstances: 0,
      conflicts: [],
      changes: [],
      errors: [],
      warnings: [],
      duration: 0
    };

    try {
      // 设置同步状态
      this.syncInProgress.set(templateId, true);
      
      // 获取模板实例
      const instances = instanceTracker.getTemplateInstances(templateId);
      result.totalInstances = instances.length;
      
      if (instances.length === 0) {
        result.warnings.push('没有找到需要同步的实例');
        result.success = true;
        return result;
      }

      // 批量处理实例
      const batchSize = options.batchSize || 10;
      for (let i = 0; i < instances.length; i += batchSize) {
        const batch = instances.slice(i, i + batchSize);
        const batchResult = await this.syncInstanceBatch(batch, options);
        
        result.updatedInstances += batchResult.updated;
        result.skippedInstances += batchResult.skipped;
        result.conflictedInstances += batchResult.conflicted;
        result.conflicts.push(...batchResult.conflicts);
        result.errors.push(...batchResult.errors);
        result.warnings.push(...batchResult.warnings);
      }

      result.success = result.errors.length === 0;
      
      // 触发同步完成事件
      this.emitEvent('syncCompleted', { templateId, result });
      
    } catch (error) {
      result.errors.push(`同步过程中发生错误: ${error}`);
      console.error('❌ 参数同步失败:', error);
    } finally {
      this.syncInProgress.set(templateId, false);
      result.duration = Date.now() - startTime;
    }

    console.log(`✅ 参数同步完成: ${result.updatedInstances}/${result.totalInstances} 个实例已更新`);
    return result;
  }

  /**
   * 解决参数冲突
   */
  async resolveConflict(
    conflictId: string,
    resolution: 'use_template' | 'keep_instance' | 'merge' | 'custom',
    customValue?: any
  ): Promise<boolean> {
    const conflict = this.conflicts.get(conflictId);
    if (!conflict) {
      console.warn(`⚠️ 冲突不存在: ${conflictId}`);
      return false;
    }

    console.log(`🔧 解决参数冲突: ${conflict.parameterName} (${resolution})`);

    try {
      let resolvedValue: any;
      
      switch (resolution) {
        case 'use_template':
          resolvedValue = conflict.templateValue;
          break;
        case 'keep_instance':
          resolvedValue = conflict.instanceValue;
          break;
        case 'merge':
          resolvedValue = this.mergeValues(conflict.templateValue, conflict.instanceValue);
          break;
        case 'custom':
          resolvedValue = customValue;
          break;
        default:
          throw new Error(`未知的解决方案: ${resolution}`);
      }

      // 更新实例
      const instance = instanceTracker.getInstance(conflict.instanceId);
      if (instance) {
        instance.overrides[conflict.parameterName] = resolvedValue;
        instanceTracker.updateInstance(conflict.instanceId, {
          overrides: instance.overrides,
          syncStatus: 'synced'
        });
      }

      // 移除冲突
      this.conflicts.delete(conflictId);
      
      // 触发冲突解决事件
      this.emitEvent('conflictResolved', { conflictId, resolution, resolvedValue });
      
      console.log(`✅ 冲突解决完成: ${conflict.parameterName}`);
      return true;
      
    } catch (error) {
      console.error(`❌ 冲突解决失败: ${error}`);
      return false;
    }
  }

  /**
   * 获取变更历史
   */
  getChangeHistory(templateId?: string, limit?: number): ParameterChange[] {
    let history = this.changeHistory;
    
    if (templateId) {
      history = history.filter(change => change.templateId === templateId);
    }
    
    if (limit) {
      history = history.slice(-limit);
    }
    
    return [...history].reverse(); // 最新的在前
  }

  /**
   * 获取冲突列表
   */
  getConflicts(templateId?: string): ParameterConflict[] {
    const conflicts = Array.from(this.conflicts.values());
    
    if (templateId) {
      return conflicts.filter(conflict => conflict.templateId === templateId);
    }
    
    return conflicts;
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(templateId: string): boolean {
    return this.syncInProgress.get(templateId) || false;
  }

  /**
   * 标准化参数列表
   */
  private normalizeParameters(parameters: TemplateParameter[]): Map<string, TemplateParameter> {
    const map = new Map<string, TemplateParameter>();
    parameters.forEach(param => {
      map.set(param.name, param);
    });
    return map;
  }

  /**
   * 检测参数修改
   */
  private detectParameterModifications(
    template: Template,
    paramName: string,
    oldParam: TemplateParameter,
    newParam: TemplateParameter
  ): ParameterChange[] {
    const changes: ParameterChange[] = [];
    
    // 检测类型变更
    if (oldParam.type !== newParam.type) {
      changes.push(this.createParameterChange(
        template,
        'type_changed',
        paramName,
        oldParam,
        newParam,
        `参数类型变更: ${oldParam.type} -> ${newParam.type}`,
        oldParam.type,
        newParam.type
      ));
    }
    
    // 检测默认值变更
    if (JSON.stringify(oldParam.defaultValue) !== JSON.stringify(newParam.defaultValue)) {
      changes.push(this.createParameterChange(
        template,
        'modified',
        paramName,
        oldParam.defaultValue,
        newParam.defaultValue,
        `参数默认值变更: ${JSON.stringify(oldParam.defaultValue)} -> ${JSON.stringify(newParam.defaultValue)}`
      ));
    }
    
    // 检测必需性变更
    if (oldParam.required !== newParam.required) {
      changes.push(this.createParameterChange(
        template,
        'modified',
        paramName,
        oldParam.required,
        newParam.required,
        `参数必需性变更: ${oldParam.required} -> ${newParam.required}`
      ));
    }
    
    return changes;
  }

  /**
   * 创建参数变更记录
   */
  private createParameterChange(
    template: Template,
    changeType: ParameterChangeType,
    parameterName: string,
    oldValue: any,
    newValue: any,
    description: string,
    oldType?: string,
    newType?: string
  ): ParameterChange {
    return {
      id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      templateId: template.id,
      templateVersion: template.version,
      changeType,
      parameterName,
      oldValue,
      newValue,
      oldType,
      newType,
      timestamp: new Date(),
      description
    };
  }

  /**
   * 检查实例冲突
   */
  private checkInstanceConflict(
    instance: TemplateInstance,
    change: ParameterChange
  ): ParameterConflict | null {
    const paramName = change.parameterName;
    const hasOverride = paramName in instance.overrides;
    
    if (!hasOverride) {
      return null; // 没有覆盖值，不会产生冲突
    }

    const instanceValue = instance.overrides[paramName];
    let conflictType: ConflictType;
    let severity: 'low' | 'medium' | 'high' | 'critical';
    let description: string;
    let autoResolvable = false;

    switch (change.changeType) {
      case 'removed':
        conflictType = 'parameter_removed';
        severity = 'high';
        description = `参数 ${paramName} 已从模板中删除，但实例中有覆盖值`;
        break;
        
      case 'type_changed':
        conflictType = 'type_mismatch';
        severity = 'medium';
        description = `参数 ${paramName} 类型已变更，实例覆盖值可能不兼容`;
        autoResolvable = this.canAutoConvertType(instanceValue, change.newType);
        break;
        
      case 'modified':
        conflictType = 'custom_override';
        severity = 'low';
        description = `参数 ${paramName} 默认值已变更，实例有自定义覆盖值`;
        autoResolvable = true;
        break;
        
      default:
        return null;
    }

    return {
      id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      instanceId: instance.id,
      templateId: instance.templateId,
      parameterName: paramName,
      conflictType,
      templateValue: change.newValue,
      instanceValue,
      description,
      severity,
      autoResolvable,
      suggestedResolution: this.getSuggestedResolution(conflictType, autoResolvable),
      timestamp: new Date()
    };
  }

  /**
   * 同步实例批次
   */
  private async syncInstanceBatch(
    instances: TemplateInstance[],
    options: SyncOptions
  ): Promise<{
    updated: number;
    skipped: number;
    conflicted: number;
    conflicts: ParameterConflict[];
    errors: string[];
    warnings: string[];
  }> {
    const result = {
      updated: 0,
      skipped: 0,
      conflicted: 0,
      conflicts: [] as ParameterConflict[],
      errors: [] as string[],
      warnings: [] as string[]
    };

    for (const instance of instances) {
      try {
        // 检查是否有冲突
        const instanceConflicts = this.getConflicts(instance.templateId)
          .filter(conflict => conflict.instanceId === instance.id);

        if (instanceConflicts.length > 0) {
          result.conflicted++;
          result.conflicts.push(...instanceConflicts);
          
          if (options.strategy === 'auto_update') {
            // 尝试自动解决冲突
            let autoResolved = 0;
            for (const conflict of instanceConflicts) {
              if (conflict.autoResolvable) {
                const resolved = await this.resolveConflict(conflict.id, 'use_template');
                if (resolved) autoResolved++;
              }
            }
            
            if (autoResolved === instanceConflicts.length) {
              result.updated++;
              result.conflicted--;
            }
          }
        } else {
          // 没有冲突，直接更新
          instanceTracker.syncInstance(instance.id);
          result.updated++;
        }
        
      } catch (error) {
        result.errors.push(`实例 ${instance.id} 同步失败: ${error}`);
        result.skipped++;
      }
    }

    return result;
  }

  /**
   * 记录变更
   */
  private recordChange(change: ParameterChange): void {
    this.changeHistory.push(change);
    
    // 限制历史记录长度
    if (this.changeHistory.length > 1000) {
      this.changeHistory.splice(0, 100);
    }
    
    // 触发变更事件
    this.emitEvent('parameterChanged', change);
  }

  /**
   * 合并值
   */
  private mergeValues(templateValue: any, instanceValue: any): any {
    // 简单的合并策略，可以根据需要扩展
    if (typeof templateValue === 'object' && typeof instanceValue === 'object') {
      return { ...templateValue, ...instanceValue };
    }
    return instanceValue; // 保留实例值
  }

  /**
   * 检查是否可以自动转换类型
   */
  private canAutoConvertType(value: any, targetType?: string): boolean {
    if (!targetType) return false;
    
    const currentType = typeof value;
    
    // 定义可以自动转换的类型对
    const autoConvertible = [
      ['string', 'number'],
      ['number', 'string'],
      ['boolean', 'string']
    ];
    
    return autoConvertible.some(([from, to]) => 
      currentType === from && targetType === to
    );
  }

  /**
   * 获取建议的解决方案
   */
  private getSuggestedResolution(conflictType: ConflictType, autoResolvable: boolean): string {
    if (autoResolvable) {
      return '建议使用模板值自动解决';
    }
    
    switch (conflictType) {
      case 'parameter_removed':
        return '建议保留实例值或手动处理';
      case 'type_mismatch':
        return '建议检查类型兼容性后手动解决';
      case 'custom_override':
        return '建议保留实例自定义值';
      default:
        return '建议手动检查并解决';
    }
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    const eventTypes = ['parameterChanged', 'syncCompleted', 'conflictResolved'];
    eventTypes.forEach(type => {
      this.eventListeners.set(type, []);
    });
  }

  /**
   * 触发事件
   */
  private emitEvent(type: string, data: any): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('参数同步事件监听器执行失败:', error);
        }
      });
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: string, listener: (data: any) => void): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
}

// 导出单例实例
export const parameterSyncManager = ParameterSyncManager.getInstance();
