/**
 * 测试配置导入功能
 */

import { ConfigParser } from './services/ConfigParser.js';

// 创建一个简单的测试配置
const testConfig = {
  scenes: {
    "test-scene": {
      name: "测试场景",
      models: ["test.glb"],
      scene: "DefaultScene",
      envTemplate: {
        $ref: "templates.1.environments.techStyle"
      },
      camera: {
        $ref: "templates.1.cameras.building"
      },
      actions: [
        {
          actionType: "doubleClick",
          meshNames: ["testMesh"],
          config: {
            highlight: {
              color: [1, 0, 0],
              duration: 500
            },
            callback: "CameraService.focusToDevice",
            parameters: {
              deviceId: "test-device"
            }
          }
        }
      ],
      lifecycle: {
        onActivated: [
          {
            trigger: "immediate",
            callback: "DeviceService.setupDataDrivenPolling",
            parameters: {
              dataSource: {
                type: "polling",
                interval: 5000,
                url: "http://localhost:4523/api/test",
                method: "get"
              },
              mappings: "test.csv",
              callback: "AnimationService.toggleModelVisibility"
            }
          }
        ]
      },
      staticLabels: [
        {
          meshNames: ["labelMesh"],
          customNames: {
            "labelMesh": "测试标签"
          },
          click: {
            enabled: true,
            callback: "CameraService.moveCamera",
            parameters: {
              position: [0, 0, 10]
            }
          }
        }
      ]
    }
  },
  defaultScene: "test-scene"
};

// 测试配置解析
function testConfigParsing() {
  console.log("开始测试配置解析...");
  
  const parser = new ConfigParser();
  
  try {
    const parsedData = parser.parseScene("test-scene", testConfig.scenes["test-scene"]);
    
    console.log("解析结果:", parsedData);
    console.log("场景配置节点:", parsedData.sceneConfig);
    console.log("事件节点数量:", parsedData.eventNodes.length);
    console.log("动作节点数量:", parsedData.actionNodes.length);
    console.log("生命周期节点数量:", parsedData.lifecycleNodes.length);
    console.log("静态标签节点数量:", parsedData.staticLabelNodes.length);
    console.log("引用节点数量:", parsedData.referenceNodes.length);
    console.log("数据节点数量:", parsedData.dataNodes.length);
    console.log("连接数量:", parsedData.connections.length);
    
    // 验证节点数据
    if (parsedData.eventNodes.length > 0) {
      console.log("第一个事件节点:", parsedData.eventNodes[0]);
    }
    
    if (parsedData.actionNodes.length > 0) {
      console.log("第一个动作节点:", parsedData.actionNodes[0]);
    }
    
    if (parsedData.lifecycleNodes.length > 0) {
      console.log("第一个生命周期节点:", parsedData.lifecycleNodes[0]);
    }
    
    if (parsedData.staticLabelNodes.length > 0) {
      console.log("第一个静态标签节点:", parsedData.staticLabelNodes[0]);
    }
    
    console.log("配置解析测试完成！");
    return true;
    
  } catch (error) {
    console.error("配置解析测试失败:", error);
    return false;
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testConfigParsing = testConfigParsing;
  console.log("测试函数已添加到window对象，可以在控制台中调用 testConfigParsing()");
}

export { testConfigParsing, testConfig };
