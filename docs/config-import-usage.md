# 配置导入功能使用说明

## 概述

ddd-flow现在支持从`public/config.js`配置文件导入复杂的3D场景配置，并自动生成对应的可视化节点图。这个功能实现了类似UE蓝图的反向工程能力，将复杂的配置结构转换为直观的节点图表示。

## 功能特性

### ✅ 已实现功能

1. **完整配置解析**
   - 支持场景基本配置（models、environment、camera等）
   - 支持Actions配置解析（事件、高亮、回调等）
   - 支持Lifecycle配置解析（生命周期事件、数据驱动服务等）
   - 支持StaticLabels配置解析（静态标签、点击事件等）
   - 支持模板引用（$ref语法）解析

2. **智能节点生成**
   - 自动创建对应的节点类型
   - 智能推断节点间的连接关系
   - 优化节点布局和视觉效果

3. **新增节点类型**
   - **模板引用节点** (ReferenceNode) - 支持$ref引用语法
   - **静态标签节点** (StaticLabelNode) - 支持静态标签配置
   - **数据驱动服务节点** (DataDrivenServiceNode) - 支持复杂数据驱动配置

4. **连接推断算法**
   - 事件节点 → 动作节点的自动连接
   - 生命周期节点 → 回调节点的自动连接
   - 静态标签节点 → 事件节点的自动连接
   - 数据流节点的自动连接

## 使用方法

### 1. 通过场景管理器导入

1. 打开ddd-flow应用
2. 在场景管理器中点击"从配置导入"按钮
3. 系统会自动加载`public/config.js`文件
4. 解析完成后，所有场景会显示在场景列表中
5. 选择任意场景查看生成的节点图

### 2. 通过代码调用

```javascript
import { SceneService } from './services/SceneService.js';

// 获取场景服务实例
const sceneService = SceneService.getInstance();

// 从配置对象导入
sceneService.importFromConfigObject(configObject);

// 从配置文件导入
sceneService.importFromConfig();
```

### 3. 测试和验证

在浏览器控制台中运行测试函数：

```javascript
// 测试配置解析功能
testConfigParsing();

// 测试连接推断算法
testConnectionInference();

// 运行完整集成测试
runFullIntegrationTest();
```

## 支持的配置格式

### Actions配置

```javascript
actions: [
  {
    actionType: "doubleClick",
    meshNames: ["mesh1", "mesh2"],
    config: {
      highlight: {
        color: [1, 0, 0],
        duration: 500
      },
      callback: "CameraService.focusToDevice",
      parameters: {
        deviceId: "device1"
      }
    }
  }
]
```

**生成节点：**
- 1个事件节点（双击事件）
- 1个高亮动作节点
- 1个回调动作节点
- 自动连接：事件 → 高亮 → 回调

### Lifecycle配置

```javascript
lifecycle: {
  onActivated: [
    {
      trigger: "immediate",
      callback: "DeviceService.setupDataDrivenPolling",
      parameters: {
        dataSource: { type: "polling", interval: 5000 },
        mappings: "mapping.csv",
        callback: "AnimationService.toggleModelVisibility"
      }
    }
  ]
}
```

**生成节点：**
- 1个生命周期节点（场景激活）
- 1个数据驱动服务节点
- 自动连接：生命周期 → 数据驱动服务

### StaticLabels配置

```javascript
staticLabels: [
  {
    meshNames: ["floor1"],
    customNames: { "floor1": "一层" },
    click: {
      enabled: true,
      callback: "CameraService.moveCamera",
      parameters: { position: [0, 0, 10] }
    }
  }
]
```

**生成节点：**
- 1个静态标签节点
- 1个点击事件节点
- 1个回调动作节点
- 自动连接：静态标签 → 点击事件 → 回调动作

### 模板引用配置

```javascript
{
  $ref: "templates.1.cameras.building",
  $extend: {
    position: [10, 20, 30],
    customParam: "value"
  }
}
```

**生成节点：**
- 1个引用节点，包含引用路径和扩展参数

## 节点类型说明

### 新增节点类型

| 节点类型 | 颜色 | 功能描述 |
|---------|------|----------|
| 模板引用节点 | 粉色 (#eb2f96) | 表示$ref引用，支持路径选择和参数扩展 |
| 静态标签节点 | 青色 (#13c2c2) | 表示静态标签配置，支持位置、样式、点击事件 |
| 数据驱动服务节点 | 深橙红色 (#d4380d) | 表示复杂数据驱动服务，支持完整参数配置 |

### 连接类型

| 连接类型 | 端口 | 描述 |
|---------|------|------|
| 执行连接 | out-exec → in-exec | 表示执行流程的顺序 |
| 数据连接 | out-data → in-data | 表示数据流向 |
| 触发连接 | out-click → in-trigger | 表示事件触发关系 |
| 引用连接 | out-ref → in-ref | 表示模板引用关系 |

## 测试和调试

### 1. 配置解析测试

```javascript
// 测试单个场景的配置解析
const parser = new ConfigParser();
const parsedData = parser.parseScene("scene-id", sceneConfig);
console.log("解析结果:", parsedData);
```

### 2. 连接推断测试

```javascript
// 测试连接推断算法
const inference = new ConnectionInference();
const connections = inference.inferAllConnections(parsedData);
console.log("推断出的连接:", connections);
```

### 3. 完整集成测试

```javascript
// 运行完整的集成测试
const result = await runFullIntegrationTest();
if (result.success) {
  console.log("测试成功，统计信息:", result.stats);
} else {
  console.error("测试失败:", result.error);
}
```

## 已知限制

1. **布局优化**：当前使用基础的坐标布局，复杂场景可能需要手动调整
2. **复杂引用**：嵌套层级很深的$ref引用可能解析不完整
3. **自定义回调**：非标准的回调函数可能无法正确识别类型

## 下一步计划

- [ ] 实现自动布局优化算法
- [ ] 支持更复杂的模板引用解析
- [ ] 添加配置验证和错误恢复机制
- [ ] 实现节点图到配置的反向编译验证
- [ ] 优化大型配置文件的处理性能

## 技术架构

```
ConfigParser (配置解析器)
├── parseScene() - 解析单个场景
├── parseActions() - 解析交互配置
├── parseLifecycle() - 解析生命周期配置
├── parseStaticLabels() - 解析静态标签配置
└── extractReferences() - 提取模板引用

ConnectionInference (连接推断器)
├── eventToAction() - 事件到动作连接
├── lifecycleToCallback() - 生命周期到回调连接
├── staticLabelToEvent() - 标签到事件连接
└── dataFlow() - 数据流连接

GraphBuilder (图构建器)
├── buildGraph() - 构建完整节点图
├── createNodes() - 创建各种节点
├── createConnections() - 创建连接
└── optimizeLayout() - 优化布局
```

这个功能将ddd-flow从一个简单的节点编辑器升级为了一个强大的配置可视化工具，支持复杂3D场景配置的双向转换。
