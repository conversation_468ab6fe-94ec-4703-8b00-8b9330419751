/**
 * PerformanceOptimizer.ts
 * 性能监控和优化服务
 */

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  // 时间指标
  templateCreationTime: number;
  templateInstantiationTime: number;
  parameterSyncTime: number;
  batchOperationTime: number;
  
  // 内存指标
  memoryUsage: {
    templates: number;
    instances: number;
    cache: number;
    total: number;
  };
  
  // 操作指标
  operationCounts: {
    templateCreations: number;
    instantiations: number;
    syncOperations: number;
    batchOperations: number;
  };
  
  // 缓存指标
  cacheMetrics: {
    hitRate: number;
    missRate: number;
    evictionCount: number;
    size: number;
  };
}

/**
 * 性能优化配置
 */
export interface OptimizationConfig {
  // 缓存配置
  cache: {
    maxSize: number;
    ttl: number; // 生存时间(毫秒)
    enableLRU: boolean;
  };
  
  // 批量操作配置
  batch: {
    maxConcurrency: number;
    chunkSize: number;
    delayBetweenChunks: number;
  };
  
  // 内存管理配置
  memory: {
    maxTemplates: number;
    maxInstances: number;
    gcThreshold: number; // 垃圾回收阈值
  };
  
  // 性能监控配置
  monitoring: {
    enableMetrics: boolean;
    sampleRate: number;
    alertThresholds: {
      memoryUsage: number;
      operationTime: number;
    };
  };
}

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * LRU缓存实现
 */
class LRUCache<T> {
  private cache = new Map<string, CacheItem<T>>();
  private maxSize: number;
  private ttl: number;

  constructor(maxSize: number = 1000, ttl: number = 300000) { // 默认5分钟TTL
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key: string): T | undefined {
    const item = this.cache.get(key);
    if (!item) return undefined;

    // 检查是否过期
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccessed = Date.now();
    
    return item.value;
  }

  set(key: string, value: T): void {
    const now = Date.now();
    
    // 如果已存在，更新
    if (this.cache.has(key)) {
      const item = this.cache.get(key)!;
      item.value = value;
      item.timestamp = now;
      item.lastAccessed = now;
      item.accessCount++;
      return;
    }

    // 如果缓存已满，移除最少使用的项
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    // 添加新项
    this.cache.set(key, {
      value,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now
    });
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  private evictLRU(): void {
    let lruKey = '';
    let lruTime = Date.now();

    for (const [key, item] of this.cache) {
      if (item.lastAccessed < lruTime) {
        lruTime = item.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey);
    }
  }

  getMetrics() {
    const totalAccess = Array.from(this.cache.values()).reduce((sum, item) => sum + item.accessCount, 0);
    return {
      size: this.cache.size,
      totalAccess,
      averageAccessCount: totalAccess / this.cache.size || 0
    };
  }
}

/**
 * 性能优化器
 */
export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  
  private config: OptimizationConfig;
  private metrics: PerformanceMetrics;
  private caches: Map<string, LRUCache<any>>;
  private operationTimings: Map<string, number[]>;
  private startTimes: Map<string, number>;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.metrics = this.initializeMetrics();
    this.caches = new Map();
    this.operationTimings = new Map();
    this.startTimes = new Map();
    
    this.initializeCaches();
    this.startPerformanceMonitoring();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): OptimizationConfig {
    return {
      cache: {
        maxSize: 1000,
        ttl: 300000, // 5分钟
        enableLRU: true
      },
      batch: {
        maxConcurrency: 10,
        chunkSize: 50,
        delayBetweenChunks: 10
      },
      memory: {
        maxTemplates: 10000,
        maxInstances: 100000,
        gcThreshold: 0.8 // 80%内存使用率触发GC
      },
      monitoring: {
        enableMetrics: true,
        sampleRate: 0.1, // 10%采样率
        alertThresholds: {
          memoryUsage: 100 * 1024 * 1024, // 100MB
          operationTime: 5000 // 5秒
        }
      }
    };
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): PerformanceMetrics {
    return {
      templateCreationTime: 0,
      templateInstantiationTime: 0,
      parameterSyncTime: 0,
      batchOperationTime: 0,
      memoryUsage: {
        templates: 0,
        instances: 0,
        cache: 0,
        total: 0
      },
      operationCounts: {
        templateCreations: 0,
        instantiations: 0,
        syncOperations: 0,
        batchOperations: 0
      },
      cacheMetrics: {
        hitRate: 0,
        missRate: 0,
        evictionCount: 0,
        size: 0
      }
    };
  }

  /**
   * 初始化缓存
   */
  private initializeCaches(): void {
    const cacheTypes = ['templates', 'instances', 'analysis', 'validation'];
    
    for (const type of cacheTypes) {
      this.caches.set(type, new LRUCache(
        this.config.cache.maxSize,
        this.config.cache.ttl
      ));
    }
  }

  /**
   * 开始性能监控
   */
  private startPerformanceMonitoring(): void {
    if (!this.config.monitoring.enableMetrics) return;

    // 定期更新内存使用情况
    setInterval(() => {
      this.updateMemoryMetrics();
      this.updateCacheMetrics();
      this.checkAlertThresholds();
    }, 5000); // 每5秒更新一次
  }

  /**
   * 开始操作计时
   */
  startTiming(operationId: string): void {
    this.startTimes.set(operationId, performance.now());
  }

  /**
   * 结束操作计时
   */
  endTiming(operationId: string, operationType: keyof PerformanceMetrics['operationCounts']): number {
    const startTime = this.startTimes.get(operationId);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.startTimes.delete(operationId);

    // 记录操作时间
    if (!this.operationTimings.has(operationType)) {
      this.operationTimings.set(operationType, []);
    }
    this.operationTimings.get(operationType)!.push(duration);

    // 更新平均时间
    this.updateAverageTime(operationType, duration);
    
    // 增加操作计数
    this.metrics.operationCounts[operationType]++;

    return duration;
  }

  /**
   * 获取缓存
   */
  getCache<T>(cacheType: string): LRUCache<T> | undefined {
    return this.caches.get(cacheType) as LRUCache<T>;
  }

  /**
   * 缓存数据
   */
  cache<T>(cacheType: string, key: string, value: T): void {
    const cache = this.caches.get(cacheType);
    if (cache) {
      cache.set(key, value);
    }
  }

  /**
   * 获取缓存数据
   */
  getCached<T>(cacheType: string, key: string): T | undefined {
    const cache = this.caches.get(cacheType);
    return cache ? cache.get(key) : undefined;
  }

  /**
   * 批量处理优化
   */
  async optimizedBatchProcess<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    options?: { concurrency?: number; chunkSize?: number }
  ): Promise<R[]> {
    const concurrency = options?.concurrency || this.config.batch.maxConcurrency;
    const chunkSize = options?.chunkSize || this.config.batch.chunkSize;
    
    const results: R[] = [];
    
    // 分块处理
    for (let i = 0; i < items.length; i += chunkSize) {
      const chunk = items.slice(i, i + chunkSize);
      
      // 并发处理当前块
      const chunkPromises = chunk.map(async (item, index) => {
        // 控制并发数
        if (index >= concurrency) {
          await new Promise(resolve => setTimeout(resolve, this.config.batch.delayBetweenChunks));
        }
        return processor(item);
      });
      
      const chunkResults = await Promise.all(chunkPromises);
      results.push(...chunkResults);
      
      // 块之间的延迟
      if (i + chunkSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, this.config.batch.delayBetweenChunks));
      }
    }
    
    return results;
  }

  /**
   * 内存优化
   */
  optimizeMemory(): void {
    // 清理过期缓存
    for (const cache of this.caches.values()) {
      // LRU缓存会自动处理过期项
    }

    // 触发垃圾回收（如果可用）
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }

    console.log('🧹 内存优化完成');
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    this.updateMemoryMetrics();
    this.updateCacheMetrics();
    return { ...this.metrics };
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics();
    this.operationTimings.clear();
    console.log('📊 性能指标已重置');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ 性能配置已更新');
  }

  /**
   * 更新平均时间
   */
  private updateAverageTime(operationType: string, duration: number): void {
    const timings = this.operationTimings.get(operationType) || [];
    const average = timings.reduce((sum, time) => sum + time, 0) / timings.length;
    
    switch (operationType) {
      case 'templateCreations':
        this.metrics.templateCreationTime = average;
        break;
      case 'instantiations':
        this.metrics.templateInstantiationTime = average;
        break;
      case 'syncOperations':
        this.metrics.parameterSyncTime = average;
        break;
      case 'batchOperations':
        this.metrics.batchOperationTime = average;
        break;
    }
  }

  /**
   * 更新内存指标
   */
  private updateMemoryMetrics(): void {
    // 估算内存使用（简化实现）
    let totalMemory = 0;
    
    for (const [type, cache] of this.caches) {
      const cacheSize = cache.size() * 1024; // 假设每个缓存项1KB
      totalMemory += cacheSize;
      
      if (type === 'templates') {
        this.metrics.memoryUsage.templates = cacheSize;
      } else if (type === 'instances') {
        this.metrics.memoryUsage.instances = cacheSize;
      }
    }
    
    this.metrics.memoryUsage.cache = totalMemory;
    this.metrics.memoryUsage.total = totalMemory;
  }

  /**
   * 更新缓存指标
   */
  private updateCacheMetrics(): void {
    let totalSize = 0;
    let totalHits = 0;
    let totalAccess = 0;
    
    for (const cache of this.caches.values()) {
      const metrics = cache.getMetrics();
      totalSize += metrics.size;
      totalAccess += metrics.totalAccess;
    }
    
    this.metrics.cacheMetrics.size = totalSize;
    this.metrics.cacheMetrics.hitRate = totalAccess > 0 ? totalHits / totalAccess : 0;
    this.metrics.cacheMetrics.missRate = 1 - this.metrics.cacheMetrics.hitRate;
  }

  /**
   * 检查警报阈值
   */
  private checkAlertThresholds(): void {
    const { alertThresholds } = this.config.monitoring;
    
    // 检查内存使用
    if (this.metrics.memoryUsage.total > alertThresholds.memoryUsage) {
      console.warn(`⚠️ 内存使用超过阈值: ${this.metrics.memoryUsage.total / 1024 / 1024}MB`);
      this.optimizeMemory();
    }
    
    // 检查操作时间
    const avgOperationTime = (
      this.metrics.templateCreationTime +
      this.metrics.templateInstantiationTime +
      this.metrics.parameterSyncTime +
      this.metrics.batchOperationTime
    ) / 4;
    
    if (avgOperationTime > alertThresholds.operationTime) {
      console.warn(`⚠️ 平均操作时间超过阈值: ${avgOperationTime}ms`);
    }
  }
}

// 导出单例实例
export const performanceOptimizer = PerformanceOptimizer.getInstance();

// 如果在浏览器环境中，将性能优化器添加到全局对象
if (typeof window !== 'undefined') {
  (window as any).performanceOptimizer = performanceOptimizer;
  console.log('⚡ 性能优化器已添加到全局对象');
}
