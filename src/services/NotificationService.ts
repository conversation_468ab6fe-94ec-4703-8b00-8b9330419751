/**
 * NotificationService.ts
 * 全局通知服务 - 提供统一的用户反馈机制
 */

import { reactive } from 'vue';

/**
 * 通知类型
 */
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

/**
 * 通知接口
 */
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
  timestamp: Date;
}

/**
 * 通知操作
 */
export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

/**
 * 通知选项
 */
export interface NotificationOptions {
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
}

/**
 * 通知服务
 */
export class NotificationService {
  private static instance: NotificationService;
  
  // 通知列表
  private notifications = reactive<Notification[]>([]);
  
  // 默认配置
  private defaultDuration = 5000; // 5秒
  private maxNotifications = 5;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * 显示成功通知
   */
  success(options: NotificationOptions): string {
    return this.show({
      type: 'success',
      ...options
    });
  }

  /**
   * 显示错误通知
   */
  error(options: NotificationOptions): string {
    return this.show({
      type: 'error',
      persistent: true, // 错误通知默认持久显示
      ...options
    });
  }

  /**
   * 显示警告通知
   */
  warning(options: NotificationOptions): string {
    return this.show({
      type: 'warning',
      ...options
    });
  }

  /**
   * 显示信息通知
   */
  info(options: NotificationOptions): string {
    return this.show({
      type: 'info',
      ...options
    });
  }

  /**
   * 显示通知
   */
  show(notification: Omit<Notification, 'id' | 'timestamp'>): string {
    const id = this.generateId();
    const newNotification: Notification = {
      id,
      timestamp: new Date(),
      duration: notification.duration || this.defaultDuration,
      ...notification
    };

    // 添加到通知列表
    this.notifications.push(newNotification);

    // 限制通知数量
    if (this.notifications.length > this.maxNotifications) {
      this.notifications.splice(0, this.notifications.length - this.maxNotifications);
    }

    // 设置自动移除（如果不是持久通知）
    if (!newNotification.persistent && newNotification.duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, newNotification.duration);
    }

    console.log(`📢 通知: [${notification.type.toUpperCase()}] ${notification.title}`);
    return id;
  }

  /**
   * 移除通知
   */
  remove(id: string): void {
    const index = this.notifications.findIndex(n => n.id === id);
    if (index > -1) {
      this.notifications.splice(index, 1);
    }
  }

  /**
   * 清除所有通知
   */
  clear(): void {
    this.notifications.splice(0);
  }

  /**
   * 获取所有通知
   */
  getNotifications(): Notification[] {
    return [...this.notifications];
  }

  /**
   * 快捷方法：模板操作成功
   */
  templateSuccess(action: string, templateName: string): string {
    return this.success({
      title: '模板操作成功',
      message: `${action} "${templateName}" 完成`
    });
  }

  /**
   * 快捷方法：模板操作失败
   */
  templateError(action: string, templateName: string, error?: string): string {
    return this.error({
      title: '模板操作失败',
      message: `${action} "${templateName}" 失败${error ? `: ${error}` : ''}`
    });
  }

  /**
   * 快捷方法：同步操作通知
   */
  syncNotification(type: 'start' | 'success' | 'error', details?: string): string {
    switch (type) {
      case 'start':
        return this.info({
          title: '开始同步',
          message: '正在同步模板参数...',
          duration: 3000
        });
      case 'success':
        return this.success({
          title: '同步完成',
          message: details || '所有模板参数已成功同步'
        });
      case 'error':
        return this.error({
          title: '同步失败',
          message: details || '模板参数同步过程中发生错误'
        });
      default:
        return '';
    }
  }

  /**
   * 快捷方法：冲突解决通知
   */
  conflictResolved(parameterName: string, resolution: string): string {
    return this.success({
      title: '冲突已解决',
      message: `参数 "${parameterName}" 冲突已通过 ${resolution} 方式解决`
    });
  }

  /**
   * 快捷方法：批量操作通知
   */
  batchOperation(
    type: 'start' | 'progress' | 'complete' | 'error',
    operation: string,
    details?: { current?: number; total?: number; error?: string }
  ): string {
    switch (type) {
      case 'start':
        return this.info({
          title: `开始${operation}`,
          message: `正在处理 ${details?.total || 0} 个项目...`,
          duration: 3000
        });
      case 'progress':
        return this.info({
          title: `${operation}进行中`,
          message: `已处理 ${details?.current || 0}/${details?.total || 0} 个项目`,
          duration: 2000
        });
      case 'complete':
        return this.success({
          title: `${operation}完成`,
          message: `成功处理了 ${details?.total || 0} 个项目`
        });
      case 'error':
        return this.error({
          title: `${operation}失败`,
          message: details?.error || '批量操作过程中发生错误'
        });
      default:
        return '';
    }
  }

  /**
   * 快捷方法：确认操作
   */
  confirmAction(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ): string {
    return this.warning({
      title,
      message,
      persistent: true,
      actions: [
        {
          label: '确认',
          action: () => {
            const id = this.generateId();
            onConfirm();
            this.remove(id);
          },
          style: 'danger'
        },
        {
          label: '取消',
          action: () => {
            const id = this.generateId();
            if (onCancel) onCancel();
            this.remove(id);
          },
          style: 'secondary'
        }
      ]
    });
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出单例实例
export const notificationService = NotificationService.getInstance();

// 导出便捷的全局方法
export const notify = {
  success: (options: NotificationOptions) => notificationService.success(options),
  error: (options: NotificationOptions) => notificationService.error(options),
  warning: (options: NotificationOptions) => notificationService.warning(options),
  info: (options: NotificationOptions) => notificationService.info(options),
  
  // 模板相关快捷方法
  templateSuccess: (action: string, name: string) => notificationService.templateSuccess(action, name),
  templateError: (action: string, name: string, error?: string) => notificationService.templateError(action, name, error),
  
  // 同步相关快捷方法
  syncStart: () => notificationService.syncNotification('start'),
  syncSuccess: (details?: string) => notificationService.syncNotification('success', details),
  syncError: (details?: string) => notificationService.syncNotification('error', details),
  
  // 冲突解决快捷方法
  conflictResolved: (param: string, resolution: string) => notificationService.conflictResolved(param, resolution),
  
  // 批量操作快捷方法
  batchStart: (operation: string, total: number) => notificationService.batchOperation('start', operation, { total }),
  batchProgress: (operation: string, current: number, total: number) => notificationService.batchOperation('progress', operation, { current, total }),
  batchComplete: (operation: string, total: number) => notificationService.batchOperation('complete', operation, { total }),
  batchError: (operation: string, error: string) => notificationService.batchOperation('error', operation, { error }),
  
  // 确认操作快捷方法
  confirm: (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => 
    notificationService.confirmAction(title, message, onConfirm, onCancel)
};

// 如果在浏览器环境中，将通知服务添加到全局对象
if (typeof window !== 'undefined') {
  (window as any).notificationService = notificationService;
  (window as any).notify = notify;
  console.log('💡 通知服务已添加到全局对象，可以通过 window.notify 访问');
}
