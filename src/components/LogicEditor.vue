<script setup lang="ts">
import { Graph, Node } from "@antv/x6";
import { MiniMap } from "@antv/x6-plugin-minimap";
import { inject, onMounted, onUnmounted, ref, Ref, watch } from "vue";
import { compileGraph } from "../compiler/graph-compiler";
import { sceneService } from "../services/SceneService";

// 创建一个简化的节点数据接口
interface NodeData {
  nodeType:
    | "event"
    | "action-highlight"
    | "action-callback"
    | "logic-branch"
    | "logic-sequence"
    | "scene-config"
    | "data-source"
    | "data-transform"
    | "data-mapping"
    | "data-consumer"
    | "lifecycle"; // 新增生命周期节点类型
  displayName?: string; // 显示名称，默认使用中文
  [key: string]: any;
}

// 节点类型映射表 - 用于默认显示名称
const nodeTypeNameMap: Record<string, string> = {
  // 事件类型
  "event-doubleClick": "双击事件",
  "event-click": "单击事件",
  "event-hover": "悬停事件",
  "event-rightDoubleClick": "右键双击事件",
  "event-hotkey": "按键事件",

  // 动作类型
  "action-highlight": "高亮",
  "action-callback-CameraService.focusToDevice": "相机聚焦",
  "action-callback-CameraService.moveCamera": "相机移动",
  "action-callback-AnimationService.playMeshAnimation": "播放动画",
  "action-callback-UIService.showMessage": "显示消息",

  // 逻辑类型
  "logic-branch": "条件分支",
  "logic-sequence": "顺序执行",

  // 场景配置
  "scene-config": "场景配置",

  // 数据节点类型
  "data-source": "数据源",
  "data-source-polling": "轮询数据源",
  "data-source-websocket": "WebSocket数据源",

  // 数据转换类型
  "data-transform": "数据转换",
  "data-transform-map": "映射转换",
  "data-transform-range": "范围转换",

  // 数据映射类型
  "data-mapping": "CSV映射文件",

  // 数据消费类型
  "data-consumer": "数据消费",
  "data-consumer-toggle": "切换可见性",
  "data-consumer-animate": "动画控制",
  "data-consumer-property": "属性控制",

  // 生命周期事件类型
  lifecycle: "生命周期",
  "lifecycle-onActivated": "场景激活",
  "lifecycle-onDeactivated": "场景退出",
  "lifecycle-onModelLoaded": "模型加载完成",
  "lifecycle-onInit": "场景初始化",
};

// 获取节点显示名称
function getNodeDisplayName(nodeType: string, subType?: string): string {
  if (subType) {
    const key = `${nodeType}-${subType}`;
    return nodeTypeNameMap[key] || subType;
  }
  return nodeTypeNameMap[nodeType] || nodeType;
}

const container = ref<HTMLElement | null>(null);
const graphInstance = ref<Graph | null>(null);
const contextMenu = ref<HTMLElement | null>(null);
const contextMenuPosition = ref({ x: 0, y: 0 });
const contextMenuType = ref<"blank" | "node" | null>(null);
const contextMenuNodeId = ref<string | null>(null);

// 获取当前场景ID
const currentSceneId = inject<Ref<string | null>>("currentSceneId");

const emit = defineEmits(["config-generated", "node-selected"]);

// 监听当前场景变化
watch(
  () => currentSceneId?.value,
  (newSceneId) => {
    if (newSceneId && graphInstance.value) {
      // 检查场景配置节点是否存在
      const sceneNodes = findSceneConfigNodes();
      if (sceneNodes.length === 0) {
        // 如果不存在场景配置节点，创建一个新的
        createSceneConfigNodeFromCurrentScene();
      } else {
        // 如果已存在场景配置节点，更新它
        updateSceneConfigNodesFromCurrentScene(sceneNodes);
      }
    }
  },
  { immediate: false }
);

// 查找场景配置节点
function findSceneConfigNodes() {
  if (!graphInstance.value) return [];

  return graphInstance.value.getNodes().filter((node) => {
    const data = node.getData() as NodeData;
    return data && data.nodeType === "scene-config";
  });
}

// 从当前场景创建场景配置节点
function createSceneConfigNodeFromCurrentScene() {
  if (!graphInstance.value || !currentSceneId?.value) return;

  const scene = sceneService.getCurrentScene();
  if (!scene) return;

  createSceneConfigNode(50, 50, {
    nodeType: "scene-config",
    sceneId: scene.id,
    sceneName: scene.name,
    models: ["陆地开关站/陆地开关站楼-三层-GIS室.glb"], // 默认模型，可以根据需要更改
    environment: "techStyle",
    description: `场景配置节点: ${scene.name}`,
  });
}

// 更新场景配置节点
function updateSceneConfigNodesFromCurrentScene(sceneNodes: Node[]) {
  if (!currentSceneId?.value) return;

  const scene = sceneService.getCurrentScene();
  if (!scene) return;

  // 更新所有场景配置节点
  sceneNodes.forEach((node) => {
    const oldData = node.getData();
    const displayName = `场景: ${scene.name}`;

    node.setData({
      ...oldData,
      sceneId: scene.id,
      sceneName: scene.name,
      displayName,
      description: `场景配置节点: ${scene.name}`,
    });

    // 更新节点标签
    node.setAttrByPath("label/text", displayName);
  });
}

// 生成唯一ID
function generateId(prefix: string): string {
  return `${prefix}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
}

// 处理节点数据更新事件的处理函数
function handleNodeDataUpdate(event: CustomEvent) {
  if (!graphInstance.value) return;

  const { nodeId, data } = event.detail;
  const node = graphInstance.value.getCellById(nodeId);

  if (node) {
    // 更新节点数据
    node.setData(data);

    // 更新节点标签为中文显示名称
    if (data.displayName) {
      node.setAttrByPath("label/text", data.displayName);
    } else if (data.nodeType === "action-callback" && data.callback) {
      const displayName = getNodeDisplayName("action-callback", data.callback);
      node.setAttrByPath("label/text", displayName);

      // 更新data中的displayName
      const updatedData = { ...data, displayName };
      node.setData(updatedData);
    } else if (data.nodeType) {
      // 为其他节点类型设置默认显示名称
      const displayName = getNodeDisplayName(data.nodeType, data.actionType);
      node.setAttrByPath("label/text", displayName);

      // 更新data中的displayName
      const updatedData = { ...data, displayName };
      node.setData(updatedData);
    }
  }
}

// 处理添加节点请求
function handleAddNodeRequest(event: CustomEvent) {
  if (!graphInstance.value) return;

  const { type, subtype } = event.detail;

  // 根据节点类型创建不同的节点
  switch (type) {
    case "event":
      createEventNode(subtype);
      break;
    case "action-highlight":
      createHighlightNode();
      break;
    case "action-callback":
      createCallbackNode(subtype);
      break;
    case "logic-branch":
      createBranchNode();
      break;
    case "logic-sequence":
      createSequenceNode();
      break;
    case "scene-config":
      createSceneConfigNode();
      break;
    case "data-source":
      createDataSourceNode(subtype);
      break;
    case "data-transform":
      createDataTransformNode(subtype);
      break;
    case "data-mapping":
      createDataMappingNode();
      break;
    case "data-consumer":
      createDataConsumerNode(subtype);
      break;
    case "lifecycle":
      createLifecycleNode(subtype);
      break;
    case "reference":
      createReferenceNode("templates.1.cameras.building");
      break;
    case "static-label":
      createStaticLabelNode();
      break;
    case "data-driven-service":
      createDataDrivenServiceNode();
      break;
  }
}

// 显示右键菜单
function showContextMenu(
  e: MouseEvent,
  type: "blank" | "node",
  nodeId?: string
) {
  e.preventDefault();

  // 设置菜单位置
  contextMenuPosition.value = { x: e.clientX, y: e.clientY };
  contextMenuType.value = type;
  contextMenuNodeId.value = nodeId || null;

  // 显示菜单
  if (contextMenu.value) {
    contextMenu.value.style.display = "block";
    contextMenu.value.style.left = `${e.clientX}px`;
    contextMenu.value.style.top = `${e.clientY}px`;
  }

  // 添加一次性点击事件监听器，点击其他地方时隐藏菜单
  document.addEventListener("click", hideContextMenu, { once: true });
}

// 隐藏右键菜单
function hideContextMenu() {
  if (contextMenu.value) {
    contextMenu.value.style.display = "none";
  }
  contextMenuType.value = null;
  contextMenuNodeId.value = null;
}

// 处理菜单项点击
function handleMenuItemClick(action: string) {
  if (!graphInstance.value) return;

  switch (action) {
    case "add-event":
      createEventNode(
        "doubleClick",
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-highlight":
      createHighlightNode(
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-callback":
      createCallbackNode(
        "CameraService.focusToDevice",
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-branch":
      createBranchNode(
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-scene-config":
      createSceneConfigNode(
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-reference":
      createReferenceNode(
        "templates.1.cameras.building",
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-static-label":
      createStaticLabelNode(
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "delete-node":
      if (contextMenuNodeId.value) {
        graphInstance.value.removeCell(contextMenuNodeId.value);
      }
      break;
    case "copy-node":
      if (contextMenuNodeId.value) {
        const node = graphInstance.value.getCellById(contextMenuNodeId.value);
        if (node) {
          const nodeData = node.getData();
          const nodeType = nodeData.nodeType;

          // 获取节点位置
          const position = node.getBBox().getCenter();
          const x = position.x + 20;
          const y = position.y + 20;

          // 根据节点类型复制节点
          switch (nodeType) {
            case "event":
              createEventNode(nodeData.actionType, x, y);
              break;
            case "action-highlight":
              createHighlightNode(x, y, nodeData);
              break;
            case "action-callback":
              createCallbackNode(nodeData.callback, x, y, nodeData.parameters);
              break;
            case "logic-branch":
              createBranchNode(x, y, nodeData);
              break;
            case "scene-config":
              createSceneConfigNode(x, y, nodeData);
              break;
          }
        }
      }
      break;
  }

  hideContextMenu();
}

// 创建事件节点
function createEventNode(
  eventType: string = "doubleClick",
  x: number = 100,
  y: number = 100,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("event");

  // 设置节点显示名称
  const displayName =
    data?.displayName || getNodeDisplayName("event", eventType);
  let description = data?.description || "事件触发";

  switch (eventType) {
    case "click":
      description = data?.description || "单击触发";
      break;
    case "hover":
      description = data?.description || "悬停触发";
      break;
    case "rightDoubleClick":
      description = data?.description || "右键双击触发";
      break;
    case "hotkey":
      description = data?.description || "按键触发";
      break;
    case "doubleClick":
    default:
      description = data?.description || "双击触发";
      break;
  }

  // 使用传入的数据或创建新数据
  const nodeData = data
    ? { ...data }
    : {
        nodeType: "event",
        actionType: eventType,
        displayName,
        meshNames: { $ref: "templates.1.meshes.buildingLevels" },
        description,
      };

  // 确保基本属性存在
  nodeData.nodeType = "event";
  nodeData.actionType = eventType;
  nodeData.displayName = displayName;

  // 创建节点
  const node = graph.addNode({
    id,
    x,
    y,
    shape: "event-node",
    label: displayName,
    data: nodeData,
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  return node;
}

// 创建高亮节点
function createHighlightNode(
  x: number = 350,
  y: number = 100,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("highlight");

  const displayName =
    data?.displayName || getNodeDisplayName("action-highlight");

  const nodeData = data || {
    nodeType: "action-highlight",
    displayName,
    color: [1, 1, 0],
    duration: 1500,
  };

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "action-node",
    label: displayName,
    data: nodeData,
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  return node;
}

// 创建回调节点
function createCallbackNode(
  callbackType: string = "CameraService.focusToDevice",
  x: number = 600,
  y: number = 100,
  customParams: any = null,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("callback");

  // 设置节点显示名称
  const displayName =
    data?.displayName || getNodeDisplayName("action-callback", callbackType);

  // 根据回调类型设置不同的参数
  let parameters: any = customParams || {};

  if (!customParams) {
    switch (callbackType) {
      case "CameraService.focusToDevice":
        parameters = {
          deviceName: "{{meshName}}",
          duration: 120,
          paddingFactor: 1,
        };
        break;
      case "CameraService.moveCamera":
        parameters = {
          position: [0, 0, 0],
          target: [0, 0, 0],
          duration: 120,
        };
        break;
      case "AnimationService.playMeshAnimation":
        parameters = {
          meshName: "{{meshName}}",
          speed: 1,
        };
        break;
      case "UIService.showMessage":
        parameters = {
          message: "操作成功",
          duration: 2000,
          position: "top-center",
        };
        break;
    }
  }

  // 使用传入的数据或创建新数据
  const nodeData = data
    ? { ...data }
    : {
        nodeType: "action-callback",
        callback: callbackType,
        displayName,
        parameters,
      };

  // 确保基本属性存在
  nodeData.nodeType = "action-callback";
  nodeData.callback = callbackType;
  nodeData.displayName = displayName;
  nodeData.parameters = parameters;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "action-node",
    label: displayName,
    data: nodeData,
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-exec", group: "in" }],
    },
  });

  return node;
}

// 创建分支节点
function createBranchNode(x: number = 350, y: number = 200, data: any = null) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("branch");

  const displayName = data?.displayName || getNodeDisplayName("logic-branch");

  const nodeData = data || {
    nodeType: "logic-branch",
    displayName,
    conditionProperty: "meshName",
    conditionOperator: "==",
    conditionValue: "GIS室",
    description: "条件分支判断",
  };

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "branch-node",
    label: displayName,
    data: nodeData,
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        outTrue: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#0f0", stroke: "#31d0c6" },
          },
        },
        outFalse: {
          position: "bottom",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#f00", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "true", group: "outTrue", port: "true" },
        { id: "false", group: "outFalse", port: "false" },
      ],
    },
  });

  return node;
}

// 创建顺序节点
function createSequenceNode(x: number = 350, y: number = 300) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("sequence");

  const displayName = getNodeDisplayName("logic-sequence");

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "action-node",
    label: displayName,
    data: {
      nodeType: "logic-sequence",
      displayName,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out1: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out2: {
          position: "bottom",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec-1", group: "out1" },
        { id: "out-exec-2", group: "out2" },
      ],
    },
  });

  return node;
}

// 创建场景配置节点
function createSceneConfigNode(
  x: number = 100,
  y: number = 50,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("scene-config");

  const scene = sceneService.getCurrentScene();
  const sceneName = scene ? scene.name : "默认场景";
  const configId = scene?.configId || "";
  const models = scene?.models || ["陆地开关站/陆地开关站楼-三层-GIS室.glb"];
  const environment = scene?.environment || "techStyle";
  const sceneType = scene?.scene || "DefaultScene";

  const displayName = data?.displayName || `场景: ${sceneName}`;

  const nodeData = data || {
    nodeType: "scene-config",
    sceneId: configId,
    sceneName: sceneName,
    displayName,
    models: models,
    environment: environment,
    scene: sceneType,
    description: `场景配置节点: ${sceneName}${
      configId ? ` (${configId})` : ""
    }`,
  };

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "scene-config-node",
    label: displayName,
    data: nodeData,
  });

  return node;
}

// 创建数据源节点
function createDataSourceNode(
  sourceType: string = "polling",
  x: number = 100,
  y: number = 200,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("data-source");

  // 设置节点显示名称
  const displayName =
    data?.displayName || getNodeDisplayName("data-source", sourceType);

  // 根据数据源类型设置不同的默认配置
  let defaultConfig: any = {};

  switch (sourceType) {
    case "polling":
      defaultConfig = {
        type: "polling",
        interval: 5000,
        url: "http://127.0.0.1:4523/api/data",
        method: "get",
        params: {},
        headers: {
          "Content-Type": "application/json",
        },
      };
      break;
    case "websocket":
      defaultConfig = {
        type: "websocket",
        url: "ws://127.0.0.1:8080",
        reconnectInterval: 3000,
        maxRetries: 5,
      };
      break;
  }

  // 使用传入的数据或创建新数据
  const nodeData = data
    ? { ...data }
    : {
        nodeType: "data-source",
        sourceType: sourceType,
        displayName,
        dataSource: defaultConfig,
        description: `${sourceType} 数据源配置`,
      };

  // 确保基本属性存在
  nodeData.nodeType = "data-source";
  nodeData.sourceType = sourceType;
  nodeData.displayName = displayName;
  nodeData.description = nodeData.description || `${sourceType} 数据源配置`;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "data-node", // 使用特殊的形状
    label: displayName,
    data: nodeData,
    attrs: {
      body: {
        fill: "#f0f7ff",
        stroke: "#1890ff",
        strokeWidth: 2,
      },
      label: {
        fill: "#333333",
        fontSize: 14,
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-data", group: "out" }],
    },
  });

  return node;
}

// 创建数据转换节点
function createDataTransformNode(
  transformType: string = "map",
  x: number = 300,
  y: number = 200,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("data-transform");

  // 设置节点显示名称
  const displayName =
    data?.displayName || getNodeDisplayName("data-transform", transformType);

  // 根据转换类型设置不同的默认配置
  let defaultConfig: any = {};

  switch (transformType) {
    case "map":
      defaultConfig = {
        type: "map",
        input: "value",
        output: "transformedValue",
        mapping: {
          "0": "关",
          "1": "开",
        },
      };
      break;
    case "range":
      defaultConfig = {
        type: "range",
        input: "value",
        output: "normalizedValue",
        from: [0, 100],
        to: [0, 1],
      };
      break;
  }

  // 使用传入的数据或创建新数据
  const nodeData = data
    ? { ...data }
    : {
        nodeType: "data-transform",
        transformType: transformType,
        displayName,
        transform: defaultConfig,
        description: `${transformType} 数据转换`,
      };

  // 确保基本属性存在
  nodeData.nodeType = "data-transform";
  nodeData.transformType = transformType;
  nodeData.displayName = displayName;
  nodeData.description = nodeData.description || `${transformType} 数据转换`;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "transform-node", // 使用特殊的形状
    label: displayName,
    data: nodeData,
    attrs: {
      body: {
        fill: "#e6f7ff",
        stroke: "#1890ff",
        strokeWidth: 2,
      },
      label: {
        fill: "#333333",
        fontSize: 14,
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-data", group: "in" },
        { id: "out-data", group: "out" },
      ],
    },
  });

  return node;
}

// 创建数据映射节点
function createDataMappingNode(
  x: number = 300,
  y: number = 300,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("data-mapping");

  // 设置节点显示名称
  const displayName = data?.displayName || getNodeDisplayName("data-mapping");

  // 使用传入的数据或创建新数据
  const nodeData = data
    ? { ...data }
    : {
        nodeType: "data-mapping",
        displayName,
        mappingFile: "public/config/mappings/mapping_1-3-1.csv", // 默认映射文件
        match: {
          pointType: "digital",
          meshType: "分合状态",
        },
        description: "CSV映射文件配置",
      };

  // 确保基本属性存在
  nodeData.nodeType = "data-mapping";
  nodeData.displayName = displayName;
  nodeData.description = nodeData.description || "CSV映射文件配置";

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "mapping-node", // 使用特殊的形状
    label: displayName,
    data: nodeData,
    attrs: {
      body: {
        fill: "#722ed1", // 紫色
        stroke: "#5F95FF",
        strokeWidth: 2,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-data", group: "in" },
        { id: "out-data", group: "out" },
      ],
    },
  });

  return node;
}

// 创建数据消费节点
function createDataConsumerNode(
  consumerType: string = "toggle",
  x: number = 400,
  y: number = 300,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("data-consumer");

  // 设置节点显示名称
  const displayName =
    data?.displayName || getNodeDisplayName("data-consumer", consumerType);

  // 根据消费类型设置不同的默认配置
  let defaultCallback = "";
  let defaultParameters = {};

  switch (consumerType) {
    case "toggle":
      defaultCallback = "AnimationService.toggleModelVisibility";
      defaultParameters = {
        meshName: "{{meshName}}",
        isVisible: "{{meshValue}}",
      };
      break;
    case "animate":
      defaultCallback = "AnimationService.playMeshAnimation";
      defaultParameters = {
        meshName: "{{meshName}}",
        speed: "{{meshValue}}",
        loop: true,
      };
      break;
    case "property":
      defaultCallback = "MaterialService.setMeshProperty";
      defaultParameters = {
        meshName: "{{meshName}}",
        property: "emissiveColor",
        value: "{{meshValue}}",
      };
      break;
  }

  // 使用传入的数据或创建新数据
  const nodeData = data
    ? { ...data }
    : {
        nodeType: "data-consumer",
        consumerType: consumerType,
        displayName,
        callback: defaultCallback,
        parameters: defaultParameters,
        description: `数据消费配置 - ${displayName}`,
        triggerCondition: "onChange", // 默认在数据变化时触发
      };

  // 确保基本属性存在
  nodeData.nodeType = "data-consumer";
  nodeData.consumerType = consumerType;
  nodeData.displayName = displayName;
  nodeData.description =
    nodeData.description || `数据消费配置 - ${displayName}`;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "consumer-node", // 使用特殊的形状
    label: displayName,
    data: nodeData,
    attrs: {
      body: {
        fill: "#fa8c16", // 橙色
        stroke: "#5F95FF",
        strokeWidth: 2,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-data", group: "in" }],
    },
  });

  return node;
}

// 创建生命周期节点
function createLifecycleNode(
  lifecycleType: string = "onActivated",
  x: number = 400,
  y: number = 300,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("lifecycle");

  // 设置节点显示名称
  const displayName =
    data?.displayName || getNodeDisplayName("lifecycle", lifecycleType);

  // 默认触发配置
  let defaultTrigger = "immediate";
  if (lifecycleType === "onModelLoaded") {
    defaultTrigger = "afterLoad";
  } else if (lifecycleType === "onInit") {
    defaultTrigger = "beforeRender";
  }

  // 使用传入的数据或创建新数据
  const nodeData = data
    ? { ...data }
    : {
        nodeType: "lifecycle",
        lifecycleType: lifecycleType,
        displayName,
        trigger: defaultTrigger,
        description: `生命周期事件 - ${displayName}`,
      };

  // 确保基本属性存在
  nodeData.nodeType = "lifecycle";
  nodeData.lifecycleType = lifecycleType;
  nodeData.displayName = displayName;
  nodeData.description =
    nodeData.description || `生命周期事件 - ${displayName}`;
  nodeData.trigger = nodeData.trigger || defaultTrigger;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "lifecycle-node", // 使用特殊的形状
    label: displayName,
    data: nodeData,
    attrs: {
      body: {
        fill: "#722ed1", // 紫色，与其他生命周期相关节点区分
        stroke: "#5F95FF",
        strokeWidth: 2,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  return node;
}

// 保存画布数据为JSON格式
function saveGraphData() {
  if (graphInstance.value) {
    return graphInstance.value.toJSON();
  }
  return null;
}

// 从JSON加载画布数据
function loadGraphData(graphData: any) {
  if (!graphInstance.value || !graphData) return;

  // 清空当前画布
  clearGraph();

  // 加载新数据
  graphInstance.value.fromJSON(graphData);
}

// 清空画布
function clearGraph() {
  if (!graphInstance.value) return;
  graphInstance.value.clearCells();
}

// 监听场景创建事件
function handleSceneCreated(event: CustomEvent) {
  const { sceneId } = event.detail;
  if (sceneId && graphInstance.value) {
    // 检查是否有场景配置节点
    const sceneNodes = findSceneConfigNodes();
    if (sceneNodes.length === 0) {
      // 如果不存在，创建新的场景配置节点
      createSceneConfigNodeFromCurrentScene();
    } else {
      // 如果存在，更新场景配置节点
      updateSceneConfigNodesFromCurrentScene(sceneNodes);
    }
  }
}

onMounted(() => {
  if (container.value) {
    // 延迟初始化，确保DOM完全渲染
    setTimeout(() => {
      initGraph();
      window.addEventListener("resize", resizeGraph);
    }, 100);
  }

  // 添加全局事件监听器
  document.addEventListener(
    "update-node-data",
    handleNodeDataUpdate as EventListener
  );
  document.addEventListener(
    "add-node-request",
    handleAddNodeRequest as EventListener
  );
  document.addEventListener(
    "scene-created",
    handleSceneCreated as EventListener
  );
});

onUnmounted(() => {
  // 移除全局事件监听器
  document.removeEventListener(
    "update-node-data",
    handleNodeDataUpdate as EventListener
  );
  document.removeEventListener(
    "add-node-request",
    handleAddNodeRequest as EventListener
  );
  document.removeEventListener(
    "scene-created",
    handleSceneCreated as EventListener
  );

  // 移除resize监听器
  window.removeEventListener("resize", resizeGraph);
});

// 定义公开方法
defineExpose({
  saveGraphData,
  loadGraphData,
  clearGraph,
  changeNodeType,
  generate,
  updateNodeData,
});

// 更新节点数据的方法
function updateNodeData(nodeId: string, data: any) {
  if (!graphInstance.value) return;

  const node = graphInstance.value.getCellById(nodeId);
  if (node && node.isNode()) {
    // 更新节点数据
    node.setData(data);

    // 更新节点显示名称
    if (data.displayName) {
      node.setAttrByPath("label/text", data.displayName);
    }
  }
}

// 创建引用节点
function createReferenceNode(
  refPath: string = "templates.1.cameras.building",
  x: number = 600,
  y: number = 100,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("reference");

  const displayName = data?.displayName || `引用: ${refPath.split('.').pop()}`;

  const nodeData = data || {
    nodeType: "reference",
    refPath,
    refType: inferRefType(refPath),
    displayName,
    description: `模板引用: ${refPath}`,
    extendParams: {},
  };

  // 确保基本属性存在
  nodeData.nodeType = "reference";
  nodeData.refPath = refPath;
  nodeData.displayName = displayName;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "reference-node",
    label: displayName,
    data: nodeData,
  });

  return node;
}

// 创建静态标签节点
function createStaticLabelNode(
  x: number = 50,
  y: number = 500,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("static-label");

  const displayName = data?.displayName || "静态标签";

  const nodeData = data || {
    nodeType: "static-label",
    displayName,
    meshNames: ["默认网格"],
    customNames: { "默认网格": "标签文本" },
    description: "静态标签配置",
    position: [0, 0, 0],
    style: "default",
  };

  // 确保基本属性存在
  nodeData.nodeType = "static-label";
  nodeData.displayName = displayName;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "static-label-node",
    label: displayName,
    data: nodeData,
  });

  return node;
}

// 推断引用类型的辅助函数
function inferRefType(refPath: string): "camera" | "environment" | "style" | "label" | "callback" {
  if (refPath.includes("cameras")) return "camera";
  if (refPath.includes("environments")) return "environment";
  if (refPath.includes("styles")) return "style";
  if (refPath.includes("labels")) return "label";
  if (refPath.includes("callbacks")) return "callback";
  return "callback"; // 默认类型
}

// 创建数据驱动服务节点
function createDataDrivenServiceNode(
  x: number = 300,
  y: number = 400,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("data-driven-service");

  const displayName = data?.displayName || "数据驱动服务";

  const nodeData = data || {
    nodeType: "data-driven-service",
    displayName,
    serviceType: "DeviceService.setupDataDrivenPolling",
    description: "复杂数据驱动服务配置",
    config: {
      dataSource: {
        type: "polling",
        interval: 5000,
        url: "http://localhost:4523/api/data",
        method: "get",
        params: {},
        headers: {
          "Content-Type": "application/json"
        },
        transforms: []
      },
      mappings: "public/config/mappings/default.csv",
      transforms: [],
      callback: "AnimationService.toggleModelVisibility",
      parameters: {
        meshName: "{{meshName}}",
        isVisible: "{{meshValue}}"
      }
    }
  };

  // 确保基本属性存在
  nodeData.nodeType = "data-driven-service";
  nodeData.displayName = displayName;

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "data-driven-service-node",
    label: displayName,
    data: nodeData,
  });

  return node;
}

// 暴露的节点类型变更方法
function changeNodeType(
  nodeId: string,
  oldType: string,
  newType: string,
  newData: any
) {
  if (!graphInstance.value) return;

  const node = graphInstance.value.getCellById(nodeId);
  if (!node || !node.isNode()) return;

  // 获取原始节点的位置
  const position = node.getBBox();
  const x = position.x;
  const y = position.y;

  // 创建新节点
  let newNode;
  switch (newType) {
    case "event":
      newNode = createEventNode(
        newData.actionType || "doubleClick",
        x,
        y,
        newData
      );
      break;
    case "action-highlight":
      newNode = createHighlightNode(x, y, newData);
      break;
    case "action-callback":
      // 确保处理参数为空的情况
      const parameters = newData.parameters || {};
      newNode = createCallbackNode(
        newData.callback || "CameraService.focusToDevice",
        x,
        y,
        parameters,
        newData
      );
      break;
    case "logic-branch":
      newNode = createBranchNode(x, y, newData);
      break;
    case "scene-config":
      newNode = createSceneConfigNode(x, y, newData);
      break;
    default:
      console.error(`未知节点类型: ${newType}`);
      return;
  }

  if (!newNode) return;

  // 删除原节点
  graphInstance.value.removeCell(node);

  // 重新发送选中事件
  emit("node-selected", {
    id: newNode.id,
    data: newNode.getData(),
  });
}

// 调整画布大小
function resizeGraph() {
  if (graphInstance.value && container.value) {
    const { offsetWidth, offsetHeight } = container.value;
    graphInstance.value.resize(offsetWidth, offsetHeight);
    graphInstance.value.centerContent();
  }
}

// 初始化图形
function initGraph() {
  if (!container.value) return;

  const graph = new Graph({
    container: container.value,
    width: container.value.offsetWidth || 800,
    height: container.value.offsetHeight || 600,
    grid: {
      visible: true,
      type: "dot",
      size: 10,
    },
    panning: {
      enabled: true,
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.5,
      maxScale: 2,
    },
    connecting: {
      allowBlank: false,
      allowNode: false,
      allowLoop: false,
      snap: true,
      createEdge() {
        return this.createEdge({
          shape: "edge",
          attrs: {
            line: {
              stroke: "#8f8f8f",
              strokeWidth: 1,
              targetMarker: {
                name: "block",
                size: 6,
              },
            },
          },
        });
      },
      validateConnection({
        sourceView,
        targetView,
        sourceMagnet,
        targetMagnet,
      }) {
        if (!sourceMagnet || !targetMagnet) {
          return false;
        }

        // 不允许连接到同一个节点
        if (sourceView === targetView) {
          return false;
        }

        return true;
      },
    },
    highlighting: {
      magnetAvailable: {
        name: "stroke",
        args: {
          attrs: {
            fill: "#5F95FF",
            stroke: "#5F95FF",
          },
        },
      },
    },
  });

  graphInstance.value = graph;

  // 为自动调整视图添加延时
  setTimeout(() => {
    graph.zoomToFit({ padding: 20 });
    initMinimap(graph);
  }, 200);

  // 自定义节点样式
  Graph.registerNode("event-node", {
    inherit: "rect",
    width: 180,
    height: 40,
    attrs: {
      body: {
        fill: "#4a6b8a",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  Graph.registerNode("action-node", {
    inherit: "rect",
    width: 220,
    height: 40,
    attrs: {
      body: {
        fill: "#4a8a6b",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  Graph.registerNode("branch-node", {
    inherit: "rect",
    width: 220,
    height: 60,
    attrs: {
      body: {
        fill: "#8a4a6b",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        outTrue: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#0f0",
              stroke: "#5F95FF",
            },
          },
        },
        outFalse: {
          position: "bottom",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#f00",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  // 注册数据节点
  Graph.registerNode("data-node", {
    inherit: "rect",
    width: 220,
    height: 50,
    attrs: {
      body: {
        fill: "#0066cc",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  // 注册数据驱动服务节点
  Graph.registerNode("data-driven-service-node", {
    inherit: "rect",
    width: 250,
    height: 60,
    attrs: {
      body: {
        fill: "#d4380d", // 深橙红色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  // 注册数据转换节点
  Graph.registerNode("transform-node", {
    inherit: "rect",
    width: 220,
    height: 50,
    attrs: {
      body: {
        fill: "#13c2c2",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  // 注册数据映射节点
  Graph.registerNode("mapping-node", {
    inherit: "rect",
    width: 220,
    height: 50,
    attrs: {
      body: {
        fill: "#722ed1", // 紫色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  // 注册数据消费节点
  Graph.registerNode("consumer-node", {
    inherit: "rect",
    width: 220,
    height: 50,
    attrs: {
      body: {
        fill: "#fa8c16", // 橙色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  Graph.registerNode("lifecycle-node", {
    inherit: "rect",
    width: 220,
    height: 50,
    attrs: {
      body: {
        fill: "#722ed1", // 紫色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#31d0c6",
            },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  Graph.registerNode("scene-config-node", {
    inherit: "rect",
    width: 280,
    height: 60,
    attrs: {
      body: {
        fill: "#1a3f5c",
        stroke: "#5b8fb9",
        strokeWidth: 2,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 16,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
  });

  // 注册静态标签节点
  Graph.registerNode("static-label-node", {
    inherit: "rect",
    width: 220,
    height: 50,
    attrs: {
      body: {
        fill: "#13c2c2", // 青色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#31d0c6",
            },
          },
        },
      },
      items: [{ id: "out-click", group: "out" }],
    },
  });

  // 注册引用节点
  Graph.registerNode("reference-node", {
    inherit: "rect",
    width: 200,
    height: 45,
    attrs: {
      body: {
        fill: "#eb2f96", // 粉色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 12,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
      items: [
        { id: "in-ref", group: "in" },
        { id: "out-ref", group: "out" },
      ],
    },
  });

  // 注册高亮节点
  Graph.registerNode("highlight-node", {
    inherit: "rect",
    width: 180,
    height: 40,
    attrs: {
      body: {
        fill: "#faad14", // 黄色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  // 注册回调节点
  Graph.registerNode("callback-node", {
    inherit: "rect",
    width: 200,
    height: 40,
    attrs: {
      body: {
        fill: "#1890ff", // 蓝色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  // 注册数据源节点
  Graph.registerNode("data-source-node", {
    inherit: "rect",
    width: 220,
    height: 50,
    attrs: {
      body: {
        fill: "#52c41a", // 绿色
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
      items: [{ id: "out-data", group: "out" }],
    },
  });

  // --- 创建示例节点 ---

  // 不再自动创建场景配置节点，而是通过场景管理自动创建
  // 延时创建场景配置节点，确保场景数据加载完成
  setTimeout(() => {
    if (currentSceneId?.value) {
      const sceneNodes = findSceneConfigNodes();
      if (sceneNodes.length === 0) {
        createSceneConfigNodeFromCurrentScene();
      } else {
        updateSceneConfigNodesFromCurrentScene(sceneNodes);
      }
    }
  }, 300);

  // 1. 创建场景配置节点
  const sceneConfigNode = graph.addNode({
    id: "scene-config-1",
    x: 50,
    y: 50,
    shape: "scene-config-node",
    label: "场景: 陆地开关站",
    data: {
      nodeType: "scene-config",
      sceneId: "1",
      sceneName: "陆地开关站",
      displayName: "场景: 陆地开关站",
      models: ["陆地开关站/陆地开关站楼.glb"],
      environment: "techStyle",
      scene: "DefaultScene",
      description: "场景配置节点: 陆地开关站",
    },
  });

  // 2. 双击事件节点
  const doubleClickNode = graph.addNode({
    id: "event-1",
    x: 50,
    y: 200,
    shape: "event-node",
    label: "双击事件",
    data: {
      nodeType: "event",
      actionType: "doubleClick",
      displayName: "双击事件",
      meshNames: [
        "NA_LSZSC_QITA03",
        "电气楼五层",
        "NA_LSZSC_QITA04",
        "二层外墙",
        "NA_LSZ_YC_MOD10",
      ],
      description: "双击触发高亮和相机聚焦",
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  // 3. 高亮节点
  const highlightNode = graph.addNode({
    id: "action-highlight-1",
    x: 350,
    y: 200,
    shape: "action-node",
    label: "高亮",
    data: {
      nodeType: "action-highlight",
      displayName: "高亮",
      color: [1, 1, 0], // 黄色高亮
      duration: 0, // 持久高亮
      intensity: 1.2,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  // 4. 相机聚焦节点
  const cameraFocusNode = graph.addNode({
    id: "action-callback-1",
    x: 650,
    y: 200,
    shape: "action-node",
    label: "相机聚焦",
    data: {
      nodeType: "action-callback",
      callback: "CameraService.focusToDevice",
      displayName: "相机聚焦",
      parameters: {
        deviceName: "{{meshName}}",
        duration: 120,
        paddingFactor: 1,
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-exec", group: "in" }],
    },
  });

  // 5. 单击事件节点
  const clickNode = graph.addNode({
    id: "event-2",
    x: 50,
    y: 350,
    shape: "event-node",
    label: "单击事件",
    data: {
      nodeType: "event",
      actionType: "click",
      displayName: "单击事件",
      meshNames: [
        "NA_LSZSC_QITA03",
        "电气楼五层",
        "NA_LSZSC_QITA04",
        "二层外墙",
        "NA_LSZ_YC_MOD10",
      ],
      description: "单击红色高亮",
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  // 6. 红色高亮节点
  const redHighlightNode = graph.addNode({
    id: "action-highlight-2",
    x: 350,
    y: 350,
    shape: "action-node",
    label: "红色高亮",
    data: {
      nodeType: "action-highlight",
      displayName: "红色高亮",
      color: [1, 0, 0], // 红色高亮
      duration: 0, // 持久高亮
      intensity: 1.2,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-exec", group: "in" }],
    },
  });

  // 7. 悬停事件节点
  const hoverNode = graph.addNode({
    id: "event-3",
    x: 50,
    y: 500,
    shape: "event-node",
    label: "悬停事件",
    data: {
      nodeType: "event",
      actionType: "hover",
      displayName: "悬停事件",
      meshNames: [
        "NA_LSZSC_QITA03",
        "电气楼五层",
        "NA_LSZSC_QITA04",
        "二层外墙",
        "NA_LSZ_YC_MOD10",
      ],
      description: "悬停青色高亮",
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  // 8. 青色高亮节点
  const cyanHighlightNode = graph.addNode({
    id: "action-highlight-3",
    x: 350,
    y: 500,
    shape: "action-node",
    label: "青色高亮",
    data: {
      nodeType: "action-highlight",
      displayName: "青色高亮",
      color: [0, 1, 1], // 青色高亮
      duration: 0, // 持久高亮
      intensity: 1.2,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-exec", group: "in" }],
    },
  });

  // 连接节点 - 双击事件流程
  graph.addEdge({
    source: { cell: doubleClickNode.id, port: "out-exec" },
    target: { cell: highlightNode.id, port: "in-exec" },
  });
  graph.addEdge({
    source: { cell: highlightNode.id, port: "out-exec" },
    target: { cell: cameraFocusNode.id, port: "in-exec" },
  });

  // 连接节点 - 单击事件流程
  graph.addEdge({
    source: { cell: clickNode.id, port: "out-exec" },
    target: { cell: redHighlightNode.id, port: "in-exec" },
  });

  // 连接节点 - 悬停事件流程
  graph.addEdge({
    source: { cell: hoverNode.id, port: "out-exec" },
    target: { cell: cyanHighlightNode.id, port: "in-exec" },
  });

  // 添加节点选择事件监听
  graph.on("node:click", ({ node }) => {
    emit("node-selected", node);
  });

  // 点击空白处取消选择
  graph.on("blank:click", () => {
    emit("node-selected", null);
  });

  // 添加右键菜单事件监听
  graph.on("blank:contextmenu", ({ e }) => {
    showContextMenu(e as unknown as MouseEvent, "blank");
  });

  graph.on("node:contextmenu", ({ e, node }) => {
    showContextMenu(e as unknown as MouseEvent, "node", node.id);
  });

  // 添加键盘快捷键
  document.addEventListener("keydown", (e) => {
    // 删除：Delete 或 Backspace
    if (e.key === "Delete" || e.key === "Backspace") {
      const selectedNodes = graph
        .getNodes()
        .filter((node) => node.hasTools?.());
      if (selectedNodes.length) {
        e.preventDefault();
        graph.removeCells(selectedNodes);
      }
    }
  });
}

// 初始化小地图
function initMinimap(graph: Graph) {
  const minimapContainer = document.getElementById("minimap-container");
  if (!minimapContainer) {
    console.warn("未找到小地图容器元素");
    return;
  }

  try {
    graph.use(
      new MiniMap({
        container: minimapContainer,
        width: minimapContainer.clientWidth || 200,
        height: minimapContainer.clientHeight || 150,
        padding: 10,
        scalable: true,
        minScale: 0.1,
        maxScale: 0.9,
      })
    );
    console.log("小地图初始化成功");
  } catch (error) {
    console.error("小地图初始化失败:", error);
  }
}

// 生成配置
function generate() {
  if (graphInstance.value) {
    try {
      const graphData = graphInstance.value.toJSON();
      const compiledConfig = compileGraph(graphData);
      emit("config-generated", compiledConfig);
    } catch (error) {
      console.error("Error generating config:", error);
    }
  }
}
</script>

<template>
  <div class="editor-wrapper">
    <div ref="container" class="canvas"></div>
    <button @click="generate" class="generate-btn">生成配置</button>

    <!-- 右键菜单 -->
    <div ref="contextMenu" class="context-menu" style="display: none">
      <template v-if="contextMenuType === 'blank'">
        <div class="menu-item" @click="handleMenuItemClick('add-event')">
          添加事件节点
        </div>
        <div class="menu-item" @click="handleMenuItemClick('add-highlight')">
          添加高亮节点
        </div>
        <div class="menu-item" @click="handleMenuItemClick('add-callback')">
          添加回调节点
        </div>
        <div class="menu-item" @click="handleMenuItemClick('add-branch')">
          添加分支节点
        </div>
        <div class="menu-item" @click="handleMenuItemClick('add-scene-config')">
          添加场景配置
        </div>
      </template>

      <template v-else-if="contextMenuType === 'node'">
        <div class="menu-item" @click="handleMenuItemClick('copy-node')">
          复制节点
        </div>
        <div class="menu-item" @click="handleMenuItemClick('delete-node')">
          删除节点
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.editor-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.canvas {
  width: 100%;
  height: 100%;
  flex: 1;
  overflow: hidden;
}
.generate-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  z-index: 1000;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  min-width: 150px;
}

.menu-item {
  padding: 8px 12px;
  cursor: pointer;
  color: #e0e0e0;
  font-size: 14px;
}

.menu-item:hover {
  background-color: #3a3a3a;
}

.menu-item + .menu-item {
  border-top: 1px solid #444;
}
</style>
