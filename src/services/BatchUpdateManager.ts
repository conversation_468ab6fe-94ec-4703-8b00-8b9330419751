/**
 * BatchUpdateManager.ts
 * 批量更新管理器 - 处理大规模模板和实例的批量更新操作
 */

import { reactive } from 'vue';
import { Template, TemplateInstance } from './TemplateManager';
import { parameterSyncManager, SyncOptions, SyncResult } from './ParameterSyncManager';
import { conflictResolver, BatchResolutionOptions } from './ConflictResolver';
import { instanceTracker } from './InstanceTracker';

/**
 * 批量更新任务
 */
export interface BatchUpdateTask {
  id: string;
  name: string;
  description: string;
  type: 'template_sync' | 'parameter_update' | 'instance_cleanup' | 'conflict_resolution';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100
  totalItems: number;
  processedItems: number;
  failedItems: number;
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  errors: string[];
  warnings: string[];
  results?: any;
}

/**
 * 批量更新选项
 */
export interface BatchUpdateOptions {
  batchSize?: number;
  maxConcurrency?: number;
  retryAttempts?: number;
  retryDelay?: number;
  continueOnError?: boolean;
  createBackup?: boolean;
  notifyProgress?: boolean;
  progressCallback?: (task: BatchUpdateTask) => void;
}

/**
 * 批量更新结果
 */
export interface BatchUpdateResult {
  taskId: string;
  success: boolean;
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  duration: number;
  errors: string[];
  warnings: string[];
  details: any;
}

/**
 * 批量更新管理器
 */
export class BatchUpdateManager {
  private static instance: BatchUpdateManager;
  
  // 任务队列
  private tasks = reactive<Map<string, BatchUpdateTask>>(new Map());
  
  // 运行中的任务
  private runningTasks = reactive<Set<string>>(new Set());
  
  // 任务历史
  private taskHistory = reactive<BatchUpdateTask[]>([]);
  
  // 配置
  private defaultOptions: BatchUpdateOptions = {
    batchSize: 50,
    maxConcurrency: 3,
    retryAttempts: 2,
    retryDelay: 1000,
    continueOnError: true,
    createBackup: true,
    notifyProgress: true
  };

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): BatchUpdateManager {
    if (!BatchUpdateManager.instance) {
      BatchUpdateManager.instance = new BatchUpdateManager();
    }
    return BatchUpdateManager.instance;
  }

  /**
   * 批量同步模板到实例
   */
  async batchSyncTemplates(
    templateIds: string[],
    syncOptions: SyncOptions = { strategy: 'smart' },
    updateOptions: BatchUpdateOptions = {}
  ): Promise<BatchUpdateResult> {
    const options = { ...this.defaultOptions, ...updateOptions };
    
    const task = this.createTask({
      name: '批量同步模板',
      description: `同步 ${templateIds.length} 个模板到其实例`,
      type: 'template_sync',
      totalItems: templateIds.length
    });

    console.log(`🔄 开始批量同步模板: ${templateIds.length} 个模板`);

    try {
      this.startTask(task);
      
      const results: SyncResult[] = [];
      const errors: string[] = [];
      
      // 分批处理
      for (let i = 0; i < templateIds.length; i += options.batchSize!) {
        const batch = templateIds.slice(i, i + options.batchSize!);
        
        // 并发处理批次
        const batchPromises = batch.map(async (templateId) => {
          try {
            const result = await parameterSyncManager.syncParametersToInstances(templateId, syncOptions);
            results.push(result);
            
            this.updateTaskProgress(task, 1);
            return result;
            
          } catch (error) {
            const errorMsg = `模板 ${templateId} 同步失败: ${error}`;
            errors.push(errorMsg);
            
            if (!options.continueOnError) {
              throw new Error(errorMsg);
            }
            
            this.updateTaskProgress(task, 1, true);
            return null;
          }
        });

        // 等待批次完成
        await Promise.all(batchPromises);
        
        // 延迟以避免过载
        if (i + options.batchSize! < templateIds.length) {
          await this.delay(100);
        }
      }

      // 汇总结果
      const totalProcessed = results.length;
      const successCount = results.filter(r => r?.success).length;
      const failureCount = results.filter(r => !r?.success).length;
      
      const result: BatchUpdateResult = {
        taskId: task.id,
        success: errors.length === 0,
        totalProcessed,
        successCount,
        failureCount,
        skippedCount: 0,
        duration: task.duration || 0,
        errors,
        warnings: [],
        details: { syncResults: results }
      };

      this.completeTask(task, result);
      console.log(`✅ 批量同步完成: ${successCount}/${totalProcessed} 成功`);
      
      return result;

    } catch (error) {
      this.failTask(task, `批量同步失败: ${error}`);
      throw error;
    }
  }

  /**
   * 批量更新参数
   */
  async batchUpdateParameters(
    updates: Array<{
      templateId: string;
      parameterName: string;
      newValue: any;
      updateInstances?: boolean;
    }>,
    options: BatchUpdateOptions = {}
  ): Promise<BatchUpdateResult> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    
    const task = this.createTask({
      name: '批量更新参数',
      description: `更新 ${updates.length} 个参数`,
      type: 'parameter_update',
      totalItems: updates.length
    });

    console.log(`🔄 开始批量更新参数: ${updates.length} 个更新`);

    try {
      this.startTask(task);
      
      const results: any[] = [];
      const errors: string[] = [];
      
      for (const update of updates) {
        try {
          // 这里需要实际的参数更新逻辑
          // 暂时模拟更新过程
          await this.delay(50); // 模拟处理时间
          
          const result = {
            templateId: update.templateId,
            parameterName: update.parameterName,
            success: true,
            oldValue: 'old_value', // 实际应该从模板获取
            newValue: update.newValue
          };
          
          results.push(result);
          this.updateTaskProgress(task, 1);
          
        } catch (error) {
          const errorMsg = `参数更新失败 ${update.templateId}.${update.parameterName}: ${error}`;
          errors.push(errorMsg);
          
          if (!mergedOptions.continueOnError) {
            throw new Error(errorMsg);
          }
          
          this.updateTaskProgress(task, 1, true);
        }
      }

      const result: BatchUpdateResult = {
        taskId: task.id,
        success: errors.length === 0,
        totalProcessed: updates.length,
        successCount: results.filter(r => r.success).length,
        failureCount: errors.length,
        skippedCount: 0,
        duration: task.duration || 0,
        errors,
        warnings: [],
        details: { updateResults: results }
      };

      this.completeTask(task, result);
      console.log(`✅ 批量参数更新完成: ${result.successCount}/${result.totalProcessed} 成功`);
      
      return result;

    } catch (error) {
      this.failTask(task, `批量参数更新失败: ${error}`);
      throw error;
    }
  }

  /**
   * 批量解决冲突
   */
  async batchResolveConflicts(
    templateIds: string[],
    resolutionOptions: BatchResolutionOptions = { strategy: 'smart' },
    updateOptions: BatchUpdateOptions = {}
  ): Promise<BatchUpdateResult> {
    const options = { ...this.defaultOptions, ...updateOptions };
    
    const task = this.createTask({
      name: '批量解决冲突',
      description: `解决 ${templateIds.length} 个模板的参数冲突`,
      type: 'conflict_resolution',
      totalItems: templateIds.length
    });

    console.log(`🔄 开始批量解决冲突: ${templateIds.length} 个模板`);

    try {
      this.startTask(task);
      
      const results: any[] = [];
      const errors: string[] = [];
      
      for (const templateId of templateIds) {
        try {
          // 获取模板的冲突
          const conflicts = parameterSyncManager.getConflicts(templateId);
          
          if (conflicts.length === 0) {
            this.updateTaskProgress(task, 1);
            continue;
          }

          // 批量解决冲突
          const resolutionResult = await conflictResolver.resolveBatchConflicts(conflicts, resolutionOptions);
          
          results.push({
            templateId,
            totalConflicts: conflicts.length,
            resolvedConflicts: resolutionResult.resolved.length,
            pendingConflicts: resolutionResult.pending.length,
            stats: resolutionResult.stats
          });
          
          this.updateTaskProgress(task, 1);
          
        } catch (error) {
          const errorMsg = `模板 ${templateId} 冲突解决失败: ${error}`;
          errors.push(errorMsg);
          
          if (!options.continueOnError) {
            throw new Error(errorMsg);
          }
          
          this.updateTaskProgress(task, 1, true);
        }
      }

      const totalResolved = results.reduce((sum, r) => sum + r.resolvedConflicts, 0);
      const totalPending = results.reduce((sum, r) => sum + r.pendingConflicts, 0);

      const result: BatchUpdateResult = {
        taskId: task.id,
        success: errors.length === 0,
        totalProcessed: templateIds.length,
        successCount: results.length,
        failureCount: errors.length,
        skippedCount: 0,
        duration: task.duration || 0,
        errors,
        warnings: totalPending > 0 ? [`${totalPending} 个冲突需要手动处理`] : [],
        details: { 
          resolutionResults: results,
          totalResolved,
          totalPending
        }
      };

      this.completeTask(task, result);
      console.log(`✅ 批量冲突解决完成: ${totalResolved} 个冲突已解决, ${totalPending} 个待处理`);
      
      return result;

    } catch (error) {
      this.failTask(task, `批量冲突解决失败: ${error}`);
      throw error;
    }
  }

  /**
   * 批量清理过期实例
   */
  async batchCleanupInstances(
    criteria: {
      olderThan?: Date;
      status?: 'inactive' | 'error' | 'outdated';
      templateIds?: string[];
      sceneIds?: string[];
    },
    options: BatchUpdateOptions = {}
  ): Promise<BatchUpdateResult> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    
    const task = this.createTask({
      name: '批量清理实例',
      description: '清理符合条件的过期实例',
      type: 'instance_cleanup',
      totalItems: 0 // 将在查询后更新
    });

    console.log('🔄 开始批量清理实例');

    try {
      this.startTask(task);
      
      // 查询符合条件的实例
      const instancesToCleanup = this.findInstancesForCleanup(criteria);
      task.totalItems = instancesToCleanup.length;
      
      if (instancesToCleanup.length === 0) {
        const result: BatchUpdateResult = {
          taskId: task.id,
          success: true,
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          duration: 0,
          errors: [],
          warnings: ['没有找到需要清理的实例'],
          details: {}
        };
        
        this.completeTask(task, result);
        return result;
      }

      const cleanedInstances: string[] = [];
      const errors: string[] = [];
      
      for (const instance of instancesToCleanup) {
        try {
          // 执行清理
          const success = instanceTracker.unregisterInstance(instance.id);
          
          if (success) {
            cleanedInstances.push(instance.id);
          } else {
            errors.push(`实例 ${instance.id} 清理失败`);
          }
          
          this.updateTaskProgress(task, 1, !success);
          
        } catch (error) {
          const errorMsg = `实例 ${instance.id} 清理失败: ${error}`;
          errors.push(errorMsg);
          
          if (!mergedOptions.continueOnError) {
            throw new Error(errorMsg);
          }
          
          this.updateTaskProgress(task, 1, true);
        }
      }

      const result: BatchUpdateResult = {
        taskId: task.id,
        success: errors.length === 0,
        totalProcessed: instancesToCleanup.length,
        successCount: cleanedInstances.length,
        failureCount: errors.length,
        skippedCount: 0,
        duration: task.duration || 0,
        errors,
        warnings: [],
        details: { cleanedInstances }
      };

      this.completeTask(task, result);
      console.log(`✅ 批量清理完成: ${cleanedInstances.length} 个实例已清理`);
      
      return result;

    } catch (error) {
      this.failTask(task, `批量清理失败: ${error}`);
      throw error;
    }
  }

  /**
   * 获取任务状态
   */
  getTask(taskId: string): BatchUpdateTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): BatchUpdateTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取运行中的任务
   */
  getRunningTasks(): BatchUpdateTask[] {
    return Array.from(this.runningTasks)
      .map(id => this.tasks.get(id))
      .filter(Boolean) as BatchUpdateTask[];
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== 'running') {
      return false;
    }

    task.status = 'cancelled';
    task.endTime = new Date();
    task.duration = task.endTime.getTime() - (task.startTime?.getTime() || 0);
    
    this.runningTasks.delete(taskId);
    
    console.log(`❌ 任务已取消: ${task.name}`);
    return true;
  }

  /**
   * 清理任务历史
   */
  cleanupTaskHistory(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = new Date(Date.now() - maxAge);
    const originalLength = this.taskHistory.length;
    
    this.taskHistory.splice(0, this.taskHistory.findIndex(task => 
      (task.endTime || task.startTime || new Date()) > cutoff
    ));
    
    const cleaned = originalLength - this.taskHistory.length;
    if (cleaned > 0) {
      console.log(`🧹 清理了 ${cleaned} 个过期任务记录`);
    }
  }

  /**
   * 创建任务
   */
  private createTask(params: {
    name: string;
    description: string;
    type: BatchUpdateTask['type'];
    totalItems: number;
  }): BatchUpdateTask {
    const task: BatchUpdateTask = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: params.name,
      description: params.description,
      type: params.type,
      status: 'pending',
      progress: 0,
      totalItems: params.totalItems,
      processedItems: 0,
      failedItems: 0,
      errors: [],
      warnings: []
    };

    this.tasks.set(task.id, task);
    return task;
  }

  /**
   * 开始任务
   */
  private startTask(task: BatchUpdateTask): void {
    task.status = 'running';
    task.startTime = new Date();
    this.runningTasks.add(task.id);
  }

  /**
   * 更新任务进度
   */
  private updateTaskProgress(task: BatchUpdateTask, increment: number, failed: boolean = false): void {
    task.processedItems += increment;
    if (failed) {
      task.failedItems += increment;
    }
    
    task.progress = Math.round((task.processedItems / task.totalItems) * 100);
    
    // 调用进度回调
    if (this.defaultOptions.progressCallback) {
      this.defaultOptions.progressCallback(task);
    }
  }

  /**
   * 完成任务
   */
  private completeTask(task: BatchUpdateTask, result: BatchUpdateResult): void {
    task.status = 'completed';
    task.endTime = new Date();
    task.duration = task.endTime.getTime() - (task.startTime?.getTime() || 0);
    task.results = result;
    
    this.runningTasks.delete(task.id);
    this.taskHistory.push({ ...task });
  }

  /**
   * 任务失败
   */
  private failTask(task: BatchUpdateTask, error: string): void {
    task.status = 'failed';
    task.endTime = new Date();
    task.duration = task.endTime.getTime() - (task.startTime?.getTime() || 0);
    task.errors.push(error);
    
    this.runningTasks.delete(task.id);
    this.taskHistory.push({ ...task });
  }

  /**
   * 查找需要清理的实例
   */
  private findInstancesForCleanup(criteria: {
    olderThan?: Date;
    status?: 'inactive' | 'error' | 'outdated';
    templateIds?: string[];
    sceneIds?: string[];
  }): TemplateInstance[] {
    const queryOptions: any = {};
    
    if (criteria.status) {
      queryOptions.status = criteria.status;
    }
    
    let instances = instanceTracker.queryInstances(queryOptions);
    
    // 按模板ID过滤
    if (criteria.templateIds) {
      instances = instances.filter(instance => 
        criteria.templateIds!.includes(instance.templateId)
      );
    }
    
    // 按场景ID过滤
    if (criteria.sceneIds) {
      instances = instances.filter(instance => 
        criteria.sceneIds!.includes(instance.sceneId)
      );
    }
    
    // 按时间过滤
    if (criteria.olderThan) {
      instances = instances.filter(instance => 
        instance.lastSyncAt < criteria.olderThan!
      );
    }
    
    return instances;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出单例实例
export const batchUpdateManager = BatchUpdateManager.getInstance();
