/**
 * ConflictResolver.ts
 * 冲突解决策略系统 - 处理模板参数冲突的智能解决方案
 */

import { ParameterConflict, ConflictType, ParameterChange } from './ParameterSyncManager';
import { TemplateInstance } from './TemplateManager';

/**
 * 解决策略
 */
export interface ResolutionStrategy {
  id: string;
  name: string;
  description: string;
  applicableConflicts: ConflictType[];
  priority: number;
  autoApplicable: boolean;
  execute: (conflict: ParameterConflict, context: ResolutionContext) => ResolutionResult;
}

/**
 * 解决上下文
 */
export interface ResolutionContext {
  instance: TemplateInstance;
  templateValue: any;
  instanceValue: any;
  userPreferences?: Record<string, any>;
  historicalChoices?: Record<string, string>;
}

/**
 * 解决结果
 */
export interface ResolutionResult {
  success: boolean;
  resolvedValue: any;
  strategy: string;
  confidence: number; // 0-1，解决方案的置信度
  explanation: string;
  warnings?: string[];
  requiresUserConfirmation?: boolean;
}

/**
 * 批量解决选项
 */
export interface BatchResolutionOptions {
  strategy: 'conservative' | 'aggressive' | 'smart' | 'user_guided';
  maxAutoResolutions?: number;
  confidenceThreshold?: number;
  preserveUserChoices?: boolean;
  createBackup?: boolean;
}

/**
 * 冲突解决器
 */
export class ConflictResolver {
  private static instance: ConflictResolver;
  
  // 解决策略注册表
  private strategies: Map<string, ResolutionStrategy> = new Map();
  
  // 用户历史选择
  private userChoiceHistory: Map<string, string> = new Map();
  
  // 解决统计
  private resolutionStats = {
    totalResolved: 0,
    autoResolved: 0,
    userResolved: 0,
    byStrategy: {} as Record<string, number>
  };

  private constructor() {
    this.initializeDefaultStrategies();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ConflictResolver {
    if (!ConflictResolver.instance) {
      ConflictResolver.instance = new ConflictResolver();
    }
    return ConflictResolver.instance;
  }

  /**
   * 解决单个冲突
   */
  resolveConflict(
    conflict: ParameterConflict,
    context: ResolutionContext,
    preferredStrategy?: string
  ): ResolutionResult {
    console.log(`🔧 解决冲突: ${conflict.parameterName} (${conflict.conflictType})`);

    try {
      // 选择解决策略
      const strategy = preferredStrategy 
        ? this.strategies.get(preferredStrategy)
        : this.selectBestStrategy(conflict);

      if (!strategy) {
        return {
          success: false,
          resolvedValue: conflict.instanceValue,
          strategy: 'none',
          confidence: 0,
          explanation: '没有找到适用的解决策略'
        };
      }

      // 执行策略
      const result = strategy.execute(conflict, context);
      
      // 记录统计
      this.recordResolution(strategy.id, result.success);
      
      // 记录用户选择（如果是用户确认的）
      if (result.success && !strategy.autoApplicable) {
        this.recordUserChoice(conflict, strategy.id);
      }

      console.log(`✅ 冲突解决完成: ${strategy.name} (置信度: ${result.confidence})`);
      return result;

    } catch (error) {
      console.error(`❌ 冲突解决失败: ${error}`);
      return {
        success: false,
        resolvedValue: conflict.instanceValue,
        strategy: 'error',
        confidence: 0,
        explanation: `解决过程中发生错误: ${error}`
      };
    }
  }

  /**
   * 批量解决冲突
   */
  async resolveBatchConflicts(
    conflicts: ParameterConflict[],
    options: BatchResolutionOptions = { strategy: 'smart' }
  ): Promise<{
    resolved: ResolutionResult[];
    pending: ParameterConflict[];
    stats: {
      total: number;
      autoResolved: number;
      requiresUser: number;
      failed: number;
    };
  }> {
    console.log(`🔄 批量解决冲突: ${conflicts.length} 个冲突`);

    const resolved: ResolutionResult[] = [];
    const pending: ParameterConflict[] = [];
    const stats = {
      total: conflicts.length,
      autoResolved: 0,
      requiresUser: 0,
      failed: 0
    };

    // 按优先级排序冲突
    const sortedConflicts = this.prioritizeConflicts(conflicts);
    
    for (const conflict of sortedConflicts) {
      // 检查是否达到自动解决限制
      if (options.maxAutoResolutions && stats.autoResolved >= options.maxAutoResolutions) {
        pending.push(conflict);
        stats.requiresUser++;
        continue;
      }

      // 构建解决上下文
      const context: ResolutionContext = {
        instance: {} as TemplateInstance, // 这里需要从实际系统获取
        templateValue: conflict.templateValue,
        instanceValue: conflict.instanceValue,
        historicalChoices: this.getHistoricalChoices(conflict)
      };

      // 选择策略
      const strategy = this.selectStrategyForBatch(conflict, options);
      
      if (!strategy) {
        pending.push(conflict);
        stats.requiresUser++;
        continue;
      }

      // 执行解决
      const result = this.resolveConflict(conflict, context, strategy.id);
      
      // 检查置信度阈值
      if (options.confidenceThreshold && result.confidence < options.confidenceThreshold) {
        pending.push(conflict);
        stats.requiresUser++;
        continue;
      }

      resolved.push(result);
      
      if (result.success) {
        if (strategy.autoApplicable) {
          stats.autoResolved++;
        } else {
          stats.requiresUser++;
        }
      } else {
        stats.failed++;
      }
    }

    console.log(`✅ 批量解决完成: ${stats.autoResolved} 自动解决, ${stats.requiresUser} 需要用户确认`);
    
    return { resolved, pending, stats };
  }

  /**
   * 注册自定义策略
   */
  registerStrategy(strategy: ResolutionStrategy): void {
    this.strategies.set(strategy.id, strategy);
    console.log(`✅ 注册解决策略: ${strategy.name}`);
  }

  /**
   * 获取可用策略
   */
  getAvailableStrategies(conflictType?: ConflictType): ResolutionStrategy[] {
    const strategies = Array.from(this.strategies.values());
    
    if (conflictType) {
      return strategies.filter(strategy => 
        strategy.applicableConflicts.includes(conflictType)
      );
    }
    
    return strategies;
  }

  /**
   * 获取解决统计
   */
  getResolutionStats(): typeof this.resolutionStats {
    return { ...this.resolutionStats };
  }

  /**
   * 初始化默认策略
   */
  private initializeDefaultStrategies(): void {
    // 保留实例值策略
    this.registerStrategy({
      id: 'preserve_instance',
      name: '保留实例值',
      description: '保留实例中的自定义覆盖值',
      applicableConflicts: ['custom_override', 'parameter_removed'],
      priority: 1,
      autoApplicable: true,
      execute: (conflict, context) => ({
        success: true,
        resolvedValue: conflict.instanceValue,
        strategy: 'preserve_instance',
        confidence: 0.8,
        explanation: '保留了实例中的自定义值'
      })
    });

    // 使用模板值策略
    this.registerStrategy({
      id: 'use_template',
      name: '使用模板值',
      description: '使用模板中的新值覆盖实例值',
      applicableConflicts: ['modified', 'type_changed'],
      priority: 2,
      autoApplicable: true,
      execute: (conflict, context) => ({
        success: true,
        resolvedValue: conflict.templateValue,
        strategy: 'use_template',
        confidence: 0.7,
        explanation: '使用了模板中的新值'
      })
    });

    // 智能合并策略
    this.registerStrategy({
      id: 'smart_merge',
      name: '智能合并',
      description: '智能合并模板值和实例值',
      applicableConflicts: ['modified', 'custom_override'],
      priority: 3,
      autoApplicable: true,
      execute: (conflict, context) => {
        const merged = this.smartMerge(conflict.templateValue, conflict.instanceValue);
        return {
          success: true,
          resolvedValue: merged,
          strategy: 'smart_merge',
          confidence: 0.6,
          explanation: '智能合并了模板值和实例值'
        };
      }
    });

    // 类型转换策略
    this.registerStrategy({
      id: 'type_conversion',
      name: '类型转换',
      description: '尝试将实例值转换为新的参数类型',
      applicableConflicts: ['type_mismatch'],
      priority: 4,
      autoApplicable: true,
      execute: (conflict, context) => {
        const converted = this.attemptTypeConversion(
          conflict.instanceValue,
          this.inferTargetType(conflict.templateValue)
        );
        
        if (converted.success) {
          return {
            success: true,
            resolvedValue: converted.value,
            strategy: 'type_conversion',
            confidence: converted.confidence,
            explanation: `成功将值转换为 ${converted.targetType} 类型`
          };
        } else {
          return {
            success: false,
            resolvedValue: conflict.instanceValue,
            strategy: 'type_conversion',
            confidence: 0,
            explanation: '类型转换失败，保留原值'
          };
        }
      }
    });

    // 用户选择策略
    this.registerStrategy({
      id: 'user_choice',
      name: '用户选择',
      description: '提示用户手动选择解决方案',
      applicableConflicts: ['parameter_removed', 'value_incompatible', 'dependency_missing'],
      priority: 10,
      autoApplicable: false,
      execute: (conflict, context) => ({
        success: false,
        resolvedValue: conflict.instanceValue,
        strategy: 'user_choice',
        confidence: 0,
        explanation: '需要用户手动选择解决方案',
        requiresUserConfirmation: true
      })
    });
  }

  /**
   * 选择最佳策略
   */
  private selectBestStrategy(conflict: ParameterConflict): ResolutionStrategy | null {
    const applicableStrategies = this.getAvailableStrategies(conflict.conflictType)
      .sort((a, b) => a.priority - b.priority);

    // 检查历史选择
    const historicalChoice = this.getUserHistoricalChoice(conflict);
    if (historicalChoice) {
      const strategy = this.strategies.get(historicalChoice);
      if (strategy && strategy.applicableConflicts.includes(conflict.conflictType)) {
        return strategy;
      }
    }

    // 根据冲突严重程度选择策略
    if (conflict.severity === 'critical') {
      return applicableStrategies.find(s => !s.autoApplicable) || applicableStrategies[0];
    }

    return applicableStrategies[0] || null;
  }

  /**
   * 为批量处理选择策略
   */
  private selectStrategyForBatch(
    conflict: ParameterConflict,
    options: BatchResolutionOptions
  ): ResolutionStrategy | null {
    const strategies = this.getAvailableStrategies(conflict.conflictType);

    switch (options.strategy) {
      case 'conservative':
        return strategies.find(s => s.id === 'preserve_instance') || null;
        
      case 'aggressive':
        return strategies.find(s => s.id === 'use_template') || null;
        
      case 'smart':
        return this.selectBestStrategy(conflict);
        
      case 'user_guided':
        return strategies.find(s => !s.autoApplicable) || null;
        
      default:
        return this.selectBestStrategy(conflict);
    }
  }

  /**
   * 冲突优先级排序
   */
  private prioritizeConflicts(conflicts: ParameterConflict[]): ParameterConflict[] {
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    
    return conflicts.sort((a, b) => {
      // 首先按严重程度排序
      const severityDiff = severityOrder[a.severity] - severityOrder[b.severity];
      if (severityDiff !== 0) return severityDiff;
      
      // 然后按是否可自动解决排序
      if (a.autoResolvable !== b.autoResolvable) {
        return a.autoResolvable ? -1 : 1;
      }
      
      // 最后按时间排序
      return a.timestamp.getTime() - b.timestamp.getTime();
    });
  }

  /**
   * 智能合并值
   */
  private smartMerge(templateValue: any, instanceValue: any): any {
    // 如果两个值都是对象，尝试合并
    if (this.isPlainObject(templateValue) && this.isPlainObject(instanceValue)) {
      return { ...templateValue, ...instanceValue };
    }
    
    // 如果两个值都是数组，合并数组
    if (Array.isArray(templateValue) && Array.isArray(instanceValue)) {
      return [...new Set([...templateValue, ...instanceValue])];
    }
    
    // 其他情况保留实例值
    return instanceValue;
  }

  /**
   * 尝试类型转换
   */
  private attemptTypeConversion(
    value: any,
    targetType: string
  ): { success: boolean; value: any; confidence: number; targetType: string } {
    try {
      switch (targetType) {
        case 'string':
          return {
            success: true,
            value: String(value),
            confidence: 0.9,
            targetType
          };
          
        case 'number':
          const numValue = Number(value);
          return {
            success: !isNaN(numValue),
            value: numValue,
            confidence: !isNaN(numValue) ? 0.8 : 0,
            targetType
          };
          
        case 'boolean':
          return {
            success: true,
            value: Boolean(value),
            confidence: 0.7,
            targetType
          };
          
        default:
          return {
            success: false,
            value,
            confidence: 0,
            targetType
          };
      }
    } catch (error) {
      return {
        success: false,
        value,
        confidence: 0,
        targetType
      };
    }
  }

  /**
   * 推断目标类型
   */
  private inferTargetType(value: any): string {
    if (Array.isArray(value)) return 'array';
    return typeof value;
  }

  /**
   * 检查是否为普通对象
   */
  private isPlainObject(obj: any): boolean {
    return obj !== null && 
           typeof obj === 'object' && 
           !Array.isArray(obj) && 
           obj.constructor === Object;
  }

  /**
   * 记录解决统计
   */
  private recordResolution(strategyId: string, success: boolean): void {
    if (success) {
      this.resolutionStats.totalResolved++;
      this.resolutionStats.byStrategy[strategyId] = 
        (this.resolutionStats.byStrategy[strategyId] || 0) + 1;
        
      const strategy = this.strategies.get(strategyId);
      if (strategy?.autoApplicable) {
        this.resolutionStats.autoResolved++;
      } else {
        this.resolutionStats.userResolved++;
      }
    }
  }

  /**
   * 记录用户选择
   */
  private recordUserChoice(conflict: ParameterConflict, strategyId: string): void {
    const key = `${conflict.conflictType}_${conflict.parameterName}`;
    this.userChoiceHistory.set(key, strategyId);
  }

  /**
   * 获取用户历史选择
   */
  private getUserHistoricalChoice(conflict: ParameterConflict): string | null {
    const key = `${conflict.conflictType}_${conflict.parameterName}`;
    return this.userChoiceHistory.get(key) || null;
  }

  /**
   * 获取历史选择
   */
  private getHistoricalChoices(conflict: ParameterConflict): Record<string, string> {
    const choices: Record<string, string> = {};
    
    // 获取相同参数名的历史选择
    for (const [key, strategy] of this.userChoiceHistory) {
      if (key.includes(conflict.parameterName)) {
        choices[key] = strategy;
      }
    }
    
    return choices;
  }
}

// 导出单例实例
export const conflictResolver = ConflictResolver.getInstance();
