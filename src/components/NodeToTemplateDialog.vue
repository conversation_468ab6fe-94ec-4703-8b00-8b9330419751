<template>
  <div v-if="isVisible" class="dialog-overlay" @click="closeDialog">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3>🔄 从节点创建模板</h3>
        <button class="close-btn" @click="closeDialog">✕</button>
      </div>
      
      <div class="dialog-content">
        <!-- 节点选择区域 -->
        <div class="section">
          <h4>📍 选择节点</h4>
          <div class="node-selection">
            <div v-if="!selectedNode" class="no-node-selected">
              <p>请在画布上选择一个节点</p>
              <button class="select-btn" @click="selectNodeFromCanvas">
                🎯 从画布选择节点
              </button>
            </div>
            <div v-else class="selected-node">
              <div class="node-preview">
                <span class="node-icon">{{ getNodeIcon(selectedNode) }}</span>
                <div class="node-info">
                  <div class="node-name">{{ getNodeName(selectedNode) }}</div>
                  <div class="node-type">{{ getNodeType(selectedNode) }}</div>
                </div>
                <button class="change-btn" @click="selectNodeFromCanvas">更换</button>
              </div>
              
              <!-- 节点属性预览 -->
              <div class="node-properties">
                <h5>节点属性预览</h5>
                <div class="properties-list">
                  <div v-for="(value, key) in getNodeProperties(selectedNode)" :key="key" class="property-item">
                    <span class="property-key">{{ key }}:</span>
                    <span class="property-value">{{ formatPropertyValue(value) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板配置区域 -->
        <div class="section">
          <h4>⚙️ 模板配置</h4>
          <div class="template-config">
            <div class="form-group">
              <label for="templateName">模板名称 *</label>
              <input
                id="templateName"
                v-model="templateName"
                type="text"
                placeholder="输入模板名称"
                class="form-input"
                :class="{ error: errors.templateName }"
              />
              <span v-if="errors.templateName" class="error-message">{{ errors.templateName }}</span>
            </div>

            <div class="form-group">
              <label for="templateDescription">模板描述</label>
              <textarea
                id="templateDescription"
                v-model="templateDescription"
                placeholder="描述模板的用途和特点"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="templateCategory">模板分类</label>
              <select id="templateCategory" v-model="templateCategory" class="form-select">
                <option value="自定义">自定义</option>
                <option value="网格">网格</option>
                <option value="样式">样式</option>
                <option value="动作">动作</option>
                <option value="标签">标签</option>
                <option value="位置">位置</option>
                <option value="交互">交互</option>
                <option value="环境">环境</option>
                <option value="回调">回调</option>
              </select>
            </div>

            <div class="form-group">
              <label for="templateType">模板类型</label>
              <select id="templateType" v-model="templateType" class="form-select">
                <option :value="TemplateType.MESH">🏗️ 网格</option>
                <option :value="TemplateType.STYLE">🎨 样式</option>
                <option :value="TemplateType.CAMERA">📷 相机</option>
                <option :value="TemplateType.ACTION">⚡ 动作</option>
                <option :value="TemplateType.LABEL">🏷️ 标签</option>
                <option :value="TemplateType.POSITION">📍 位置</option>
                <option :value="TemplateType.INTERACTION">🎭 交互</option>
                <option :value="TemplateType.ENVIRONMENT">🌍 环境</option>
                <option :value="TemplateType.CALLBACK">🔄 回调</option>
              </select>
            </div>

            <div class="form-group">
              <label for="templateTags">标签</label>
              <input
                id="templateTags"
                v-model="templateTags"
                type="text"
                placeholder="用逗号分隔多个标签"
                class="form-input"
              />
              <small class="form-hint">例如: 按钮, 蓝色, 可点击</small>
            </div>
          </div>
        </div>

        <!-- 参数配置区域 -->
        <div v-if="extractedParameters.length > 0" class="section">
          <h4>🔧 参数配置</h4>
          <div class="parameters-config">
            <p class="section-description">以下参数将从节点中自动提取，您可以调整参数设置：</p>
            <div class="parameters-list">
              <div v-for="(param, index) in extractedParameters" :key="index" class="parameter-item">
                <div class="parameter-header">
                  <input
                    v-model="param.enabled"
                    type="checkbox"
                    class="parameter-checkbox"
                  />
                  <span class="parameter-name">{{ param.name }}</span>
                  <span class="parameter-type">{{ param.type }}</span>
                </div>
                <div v-if="param.enabled" class="parameter-details">
                  <input
                    v-model="param.description"
                    type="text"
                    placeholder="参数描述"
                    class="parameter-description"
                  />
                  <label class="parameter-required">
                    <input v-model="param.required" type="checkbox" />
                    必需参数
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="cancel-btn" @click="closeDialog">取消</button>
        <button 
          class="create-btn" 
          @click="createTemplate"
          :disabled="!canCreateTemplate"
          :class="{ loading: isCreating }"
        >
          <span v-if="isCreating">创建中...</span>
          <span v-else>✨ 创建模板</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { TemplateType } from '../services/TemplateManager';
import { nodeSelector } from '../services/NodeSelector';

// Props
interface Props {
  isVisible: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  created: [template: any];
}>();

// 响应式数据
const selectedNode = ref<any>(null);
const templateName = ref('');
const templateDescription = ref('');
const templateCategory = ref('自定义');
const templateType = ref<TemplateType>(TemplateType.STYLE);
const templateTags = ref('');
const extractedParameters = ref<any[]>([]);
const isCreating = ref(false);
const errors = ref<Record<string, string>>({});

// 计算属性
const canCreateTemplate = computed(() => {
  return selectedNode.value && 
         templateName.value.trim() && 
         !isCreating.value &&
         !errors.value.templateName;
});

// 监听模板名称变化，进行验证
watch(templateName, (newName) => {
  if (!newName.trim()) {
    errors.value.templateName = '模板名称不能为空';
  } else if (newName.length < 2) {
    errors.value.templateName = '模板名称至少需要2个字符';
  } else if (newName.length > 50) {
    errors.value.templateName = '模板名称不能超过50个字符';
  } else {
    delete errors.value.templateName;
  }
});

// 监听节点变化，自动提取参数
watch(selectedNode, (newNode) => {
  if (newNode) {
    extractParametersFromNode(newNode);
    autoDetectTemplateType(newNode);
    autoGenerateTemplateName(newNode);
  }
});

// 方法
const closeDialog = () => {
  emit('close');
  resetForm();
};

const resetForm = () => {
  selectedNode.value = null;
  templateName.value = '';
  templateDescription.value = '';
  templateCategory.value = '自定义';
  templateType.value = TemplateType.STYLE;
  templateTags.value = '';
  extractedParameters.value = [];
  isCreating.value = false;
  errors.value = {};
};

const selectNodeFromCanvas = async () => {
  console.log('从画布选择节点...');

  try {
    // 使用节点选择器从画布选择节点
    const node = await nodeSelector.selectFromCanvas();
    selectedNode.value = node;

    if (node) {
      console.log('✅ 节点选择成功:', node);
    } else {
      console.log('❌ 未选择任何节点');
    }
  } catch (error) {
    console.error('节点选择失败:', error);
    alert('节点选择失败，请重试');
  }
};

const getNodeIcon = (node: any): string => {
  if (!node) return '📄';
  return nodeSelector.getNodeDisplayInfo(node).icon;
};

const getNodeName = (node: any): string => {
  if (!node) return '未知节点';
  return nodeSelector.getNodeDisplayInfo(node).name;
};

const getNodeType = (node: any): string => {
  if (!node) return '未知类型';
  return nodeSelector.getNodeDisplayInfo(node).description;
};

const getNodeProperties = (node: any): Record<string, any> => {
  if (!node) return {};
  
  return {
    id: node.id,
    shape: node.shape,
    ...node.data,
    size: `${node.size?.width || 0} × ${node.size?.height || 0}`,
    position: `(${node.position?.x || 0}, ${node.position?.y || 0})`
  };
};

const formatPropertyValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  return String(value);
};

const extractParametersFromNode = (node: any) => {
  const parameters: any[] = [];
  
  if (node.data) {
    Object.entries(node.data).forEach(([key, value]) => {
      if (key !== 'id' && typeof value !== 'function') {
        parameters.push({
          name: key,
          type: Array.isArray(value) ? 'array' : typeof value,
          description: `${key}参数`,
          defaultValue: value,
          required: false,
          enabled: true
        });
      }
    });
  }
  
  extractedParameters.value = parameters;
};

const autoDetectTemplateType = (node: any) => {
  const data = node.data || {};
  
  if (data.backgroundColor || data.borderColor || data.textColor) {
    templateType.value = TemplateType.STYLE;
  } else if (data.meshNames || data.meshes) {
    templateType.value = TemplateType.MESH;
  } else if (data.camera || data.cameraPosition) {
    templateType.value = TemplateType.CAMERA;
  } else if (data.action || data.callback || data.onClick) {
    templateType.value = TemplateType.ACTION;
  } else if (data.label || data.text || data.fontSize) {
    templateType.value = TemplateType.LABEL;
  } else {
    templateType.value = TemplateType.STYLE;
  }
};

const autoGenerateTemplateName = (node: any) => {
  const nodeName = getNodeName(node);
  const typeNames = {
    [TemplateType.MESH]: '网格',
    [TemplateType.STYLE]: '样式',
    [TemplateType.CAMERA]: '相机',
    [TemplateType.ACTION]: '动作',
    [TemplateType.LABEL]: '标签',
    [TemplateType.POSITION]: '位置',
    [TemplateType.INTERACTION]: '交互',
    [TemplateType.ENVIRONMENT]: '环境',
    [TemplateType.CALLBACK]: '回调'
  };
  
  templateName.value = `${nodeName}_${typeNames[templateType.value] || '模板'}`;
};

const createTemplate = async () => {
  if (!canCreateTemplate.value) return;
  
  isCreating.value = true;
  
  try {
    // 准备模板数据
    const templateData = {
      name: templateName.value.trim(),
      description: templateDescription.value.trim(),
      category: templateCategory.value,
      type: templateType.value,
      tags: templateTags.value.split(',').map(tag => tag.trim()).filter(Boolean),
      node: selectedNode.value,
      parameters: extractedParameters.value.filter(p => p.enabled)
    };
    
    // 模拟创建过程
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('创建模板:', templateData);
    
    // 发送创建事件
    emit('created', templateData);
    
    // 关闭对话框
    closeDialog();
    
  } catch (error) {
    console.error('创建模板失败:', error);
    alert('创建模板失败，请重试');
  } finally {
    isCreating.value = false;
  }
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.dialog-container {
  background: #1a202c;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #4a5568;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.dialog-header h3 {
  margin: 0;
  color: #f7fafc;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #a0aec0;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
}

.section h4 {
  margin: 0 0 1rem 0;
  color: #f7fafc;
  font-size: 1.1rem;
  font-weight: 600;
}

.section-description {
  color: #a0aec0;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

/* 节点选择样式 */
.no-node-selected {
  text-align: center;
  padding: 2rem;
  border: 2px dashed #4a5568;
  border-radius: 8px;
  color: #a0aec0;
}

.select-btn {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: #3182ce;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.select-btn:hover {
  background: #2b6cb0;
  transform: translateY(-1px);
}

.selected-node {
  border: 1px solid #4a5568;
  border-radius: 8px;
  padding: 1rem;
  background: #2d3748;
}

.node-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.node-icon {
  font-size: 2rem;
}

.node-info {
  flex: 1;
}

.node-name {
  font-weight: 600;
  color: #f7fafc;
}

.node-type {
  font-size: 0.875rem;
  color: #a0aec0;
}

.change-btn {
  padding: 0.5rem 1rem;
  background: #4a5568;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.change-btn:hover {
  background: #718096;
}

.node-properties {
  margin-top: 1rem;
}

.node-properties h5 {
  margin: 0 0 0.5rem 0;
  color: #e2e8f0;
  font-size: 0.95rem;
}

.properties-list {
  max-height: 150px;
  overflow-y: auto;
  background: #1a202c;
  border-radius: 4px;
  padding: 0.75rem;
}

.property-item {
  display: flex;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.property-key {
  color: #63b3ed;
  min-width: 100px;
  font-weight: 500;
}

.property-value {
  color: #e2e8f0;
  word-break: break-all;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #e2e8f0;
  font-weight: 500;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #63b3ed;
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.form-input.error {
  border-color: #e53e3e;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-hint {
  display: block;
  margin-top: 0.25rem;
  color: #a0aec0;
  font-size: 0.75rem;
}

.error-message {
  display: block;
  margin-top: 0.25rem;
  color: #e53e3e;
  font-size: 0.75rem;
}

/* 参数配置样式 */
.parameters-list {
  max-height: 300px;
  overflow-y: auto;
}

.parameter-item {
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.parameter-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.parameter-checkbox {
  width: 16px;
  height: 16px;
}

.parameter-name {
  flex: 1;
  font-weight: 500;
  color: #f7fafc;
}

.parameter-type {
  background: #4a5568;
  color: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.parameter-details {
  margin-top: 0.75rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.parameter-description {
  flex: 1;
  padding: 0.5rem;
  background: #1a202c;
  border: 1px solid #4a5568;
  border-radius: 4px;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.parameter-required {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e2e8f0;
  font-size: 0.875rem;
  white-space: nowrap;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  background: #2d3748;
  border-top: 1px solid #4a5568;
}

.cancel-btn,
.create-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #4a5568;
  color: #e2e8f0;
}

.cancel-btn:hover {
  background: #718096;
}

.create-btn {
  background: #38a169;
  color: white;
}

.create-btn:hover:not(:disabled) {
  background: #2f855a;
  transform: translateY(-1px);
}

.create-btn:disabled {
  background: #4a5568;
  color: #a0aec0;
  cursor: not-allowed;
}

.create-btn.loading {
  background: #4a5568;
  cursor: wait;
}

/* 滚动条样式 */
.dialog-content::-webkit-scrollbar,
.properties-list::-webkit-scrollbar,
.parameters-list::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track,
.properties-list::-webkit-scrollbar-track,
.parameters-list::-webkit-scrollbar-track {
  background: #2d3748;
}

.dialog-content::-webkit-scrollbar-thumb,
.properties-list::-webkit-scrollbar-thumb,
.parameters-list::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover,
.properties-list::-webkit-scrollbar-thumb:hover,
.parameters-list::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}
</style>
