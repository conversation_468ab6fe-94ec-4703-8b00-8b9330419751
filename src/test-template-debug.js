/**
 * 模板系统调试测试
 */

// 简单的测试函数，用于在浏览器控制台中调试模板系统
function debugTemplateSystem() {
  console.log("🔍 开始调试模板系统...");
  
  try {
    // 1. 检查TemplateManager是否可用
    console.log("1. 检查TemplateManager导入...");
    
    // 动态导入TemplateManager
    import('./services/TemplateManager.js').then(({ TemplateManager, TemplateType }) => {
      console.log("✅ TemplateManager导入成功");
      
      // 2. 获取实例
      const templateManager = TemplateManager.getInstance();
      console.log("✅ TemplateManager实例获取成功");
      
      // 3. 检查初始状态
      const allTemplates = templateManager.registry.getAll();
      console.log(`📊 当前模板数量: ${allTemplates.length}`);
      
      if (allTemplates.length === 0) {
        console.log("⚠️ 没有模板，尝试创建测试模板...");
        
        // 创建测试模板
        const testTemplate = {
          name: "调试测试模板",
          type: TemplateType.MESH,
          data: ["debug1", "debug2", "debug3"],
          metadata: {
            category: "调试",
            tags: ["debug", "test"],
            author: "debug",
            usage: 0,
            dependencies: []
          }
        };
        
        const result = templateManager.createTemplate(testTemplate);
        if (result.success) {
          console.log("✅ 测试模板创建成功:", result.template.name);
        } else {
          console.log("❌ 测试模板创建失败:", result.errors);
        }
      }
      
      // 4. 测试搜索功能
      console.log("4. 测试搜索功能...");
      const searchResults = templateManager.registry.search("test");
      console.log(`🔍 搜索"test"结果: ${searchResults.length} 个模板`);
      
      // 5. 测试按类型获取
      console.log("5. 测试按类型获取...");
      const meshTemplates = templateManager.registry.getByType(TemplateType.MESH);
      console.log(`🏗️ 网格模板数量: ${meshTemplates.length}`);
      
      // 6. 显示所有模板详情
      console.log("6. 所有模板详情:");
      templateManager.registry.getAll().forEach((template, index) => {
        console.log(`  ${index + 1}. ${template.name} (${template.type}) - ${template.description}`);
      });
      
      // 7. 测试模板解析
      if (allTemplates.length > 0) {
        console.log("7. 测试模板解析...");
        const firstTemplate = allTemplates[0];
        const resolvedData = templateManager.resolver.resolve(firstTemplate.path);
        console.log(`🔧 解析模板 "${firstTemplate.name}":`, resolvedData);
      }
      
      console.log("🎉 模板系统调试完成！");
      
      // 返回有用的信息
      return {
        templateManager,
        templateCount: allTemplates.length,
        templates: allTemplates,
        stats: templateManager.getUsageStats()
      };
      
    }).catch(error => {
      console.error("❌ TemplateManager导入失败:", error);
    });
    
  } catch (error) {
    console.error("❌ 调试过程中发生错误:", error);
  }
}

// 测试模板面板组件
function debugTemplatePanel() {
  console.log("🎨 开始调试模板面板...");
  
  // 检查Vue组件是否正确挂载
  const templatePanels = document.querySelectorAll('.template-panel');
  console.log(`📱 找到 ${templatePanels.length} 个模板面板元素`);
  
  if (templatePanels.length === 0) {
    console.log("⚠️ 没有找到模板面板，检查是否已显示...");
    
    // 检查显示控制按钮
    const templateBtn = document.querySelector('.template-btn');
    if (templateBtn) {
      console.log("✅ 找到模板面板切换按钮");
      console.log("💡 尝试点击按钮显示模板面板...");
      templateBtn.click();
    } else {
      console.log("❌ 没有找到模板面板切换按钮");
    }
  } else {
    templatePanels.forEach((panel, index) => {
      console.log(`📱 模板面板 ${index + 1}:`, panel);
      
      // 检查面板内容
      const searchInput = panel.querySelector('.search-input');
      const filterButtons = panel.querySelectorAll('.filter-btn');
      const templateItems = panel.querySelectorAll('.template-item');
      
      console.log(`  🔍 搜索框: ${searchInput ? '存在' : '缺失'}`);
      console.log(`  🏷️ 过滤按钮: ${filterButtons.length} 个`);
      console.log(`  📄 模板项目: ${templateItems.length} 个`);
    });
  }
}

// 测试模板选择器
function debugTemplateSelector() {
  console.log("🎯 开始调试模板选择器...");
  
  // 查找属性面板中的模板选择器
  const templateSelectors = document.querySelectorAll('.template-selector');
  console.log(`🎯 找到 ${templateSelectors.length} 个模板选择器`);
  
  if (templateSelectors.length === 0) {
    console.log("⚠️ 没有找到模板选择器，检查属性面板...");
    
    // 检查是否有选中的事件节点
    const propertiesPanel = document.querySelector('.properties-panel');
    if (propertiesPanel) {
      console.log("✅ 找到属性面板");
      
      // 查找事件节点相关的元素
      const eventSection = propertiesPanel.querySelector('[class*="event"]');
      const meshSection = propertiesPanel.querySelector('[class*="mesh"]');
      
      console.log(`  🎯 事件相关元素: ${eventSection ? '存在' : '缺失'}`);
      console.log(`  🏗️ 网格相关元素: ${meshSection ? '存在' : '缺失'}`);
    } else {
      console.log("❌ 没有找到属性面板");
    }
  } else {
    templateSelectors.forEach((selector, index) => {
      console.log(`🎯 模板选择器 ${index + 1}:`, selector);
      
      // 检查选择器内容
      const pathInput = selector.querySelector('.path-input');
      const dropdownBtn = selector.querySelector('.dropdown-btn');
      
      console.log(`  📝 路径输入框: ${pathInput ? '存在' : '缺失'}`);
      console.log(`  ⬇️ 下拉按钮: ${dropdownBtn ? '存在' : '缺失'}`);
    });
  }
}

// 综合调试函数
function debugAll() {
  console.log("🚀 开始综合调试...");
  
  debugTemplateSystem();
  
  setTimeout(() => {
    debugTemplatePanel();
    debugTemplateSelector();
  }, 1000);
}

// 检查当前页面状态
function checkPageStatus() {
  console.log("📊 检查页面状态...");
  
  // 检查主要组件
  const components = {
    'app-container': document.querySelector('.app-container'),
    'logic-editor': document.querySelector('.logic-editor'),
    'properties-panel': document.querySelector('.properties-panel'),
    'node-library': document.querySelector('.node-library'),
    'template-panel-container': document.querySelector('.template-panel-container')
  };
  
  Object.entries(components).forEach(([name, element]) => {
    console.log(`  ${name}: ${element ? '✅ 存在' : '❌ 缺失'}`);
  });
  
  // 检查工具栏按钮
  const toolbarButtons = document.querySelectorAll('.toolbar-btn');
  console.log(`🔧 工具栏按钮数量: ${toolbarButtons.length}`);
  
  toolbarButtons.forEach((btn, index) => {
    console.log(`  按钮 ${index + 1}: ${btn.textContent?.trim()}`);
  });
  
  // 检查控制台错误
  console.log("💡 如果有错误，请检查浏览器控制台的错误信息");
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.debugTemplateSystem = debugTemplateSystem;
  window.debugTemplatePanel = debugTemplatePanel;
  window.debugTemplateSelector = debugTemplateSelector;
  window.debugAll = debugAll;
  window.checkPageStatus = checkPageStatus;
  
  console.log("🛠️ 调试函数已添加到window对象:");
  console.log("- debugTemplateSystem() - 调试模板系统核心");
  console.log("- debugTemplatePanel() - 调试模板面板");
  console.log("- debugTemplateSelector() - 调试模板选择器");
  console.log("- debugAll() - 综合调试");
  console.log("- checkPageStatus() - 检查页面状态");
  console.log("");
  console.log("💡 建议先运行 checkPageStatus() 检查页面状态");
}

export { debugTemplateSystem, debugTemplatePanel, debugTemplateSelector, debugAll, checkPageStatus };
