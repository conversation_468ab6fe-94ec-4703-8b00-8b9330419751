<template>
  <div class="parameter-sync-panel">
    <!-- 面板遮罩 -->
    <div class="panel-overlay" @click="$emit('close')"></div>
    
    <!-- 面板内容 -->
    <div class="panel-content">
      <!-- 头部 -->
      <div class="panel-header">
        <h3>🔄 参数同步管理</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <div class="stat-value">{{ instanceStats.total || 0 }}</div>
            <div class="stat-label">总实例数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⚠️</div>
          <div class="stat-info">
            <div class="stat-value">{{ conflicts.length }}</div>
            <div class="stat-label">冲突数量</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🔄</div>
          <div class="stat-info">
            <div class="stat-value">{{ runningTasks.length }}</div>
            <div class="stat-label">运行任务</div>
          </div>
        </div>
      </div>

      <!-- 冲突列表 -->
      <div v-if="conflicts.length > 0" class="conflicts-section">
        <h4>参数冲突</h4>
        <div class="conflicts-list">
          <div
            v-for="conflict in conflicts.slice(0, 5)"
            :key="conflict.id"
            class="conflict-item"
          >
            <div class="conflict-info">
              <div class="conflict-name">{{ conflict.parameterName }}</div>
              <div class="conflict-desc">{{ conflict.description }}</div>
            </div>
            <div class="conflict-actions">
              <button
                @click="resolveConflict(conflict, 'use_template')"
                class="resolve-btn template"
              >
                使用模板值
              </button>
              <button
                @click="resolveConflict(conflict, 'keep_instance')"
                class="resolve-btn instance"
              >
                保留实例值
              </button>
            </div>
          </div>
        </div>
        
        <div v-if="conflicts.length > 5" class="more-conflicts">
          还有 {{ conflicts.length - 5 }} 个冲突...
        </div>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions">
        <button
          @click="batchResolveConflicts('smart')"
          :disabled="isProcessing || conflicts.length === 0"
          class="batch-btn smart"
        >
          🤖 智能批量解决
        </button>
        <button
          @click="batchSyncAll"
          :disabled="isProcessing"
          class="batch-btn sync"
        >
          🔄 同步所有模板
        </button>
        <button
          @click="refreshData"
          :disabled="isProcessing"
          class="batch-btn refresh"
        >
          🔄 刷新数据
        </button>
      </div>

      <!-- 任务状态 -->
      <div v-if="runningTasks.length > 0" class="tasks-section">
        <h4>运行中的任务</h4>
        <div class="tasks-list">
          <div
            v-for="task in runningTasks.slice(0, 3)"
            :key="task.id"
            class="task-item"
          >
            <div class="task-info">
              <div class="task-name">{{ task.name }}</div>
              <div class="task-progress">
                {{ task.processedItems }}/{{ task.totalItems }} ({{ task.progress }}%)
              </div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${task.progress}%` }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近变更 -->
      <div v-if="recentChanges.length > 0" class="changes-section">
        <h4>最近变更</h4>
        <div class="changes-list">
          <div
            v-for="change in recentChanges.slice(0, 5)"
            :key="change.id"
            class="change-item"
          >
            <div class="change-icon">{{ getChangeIcon(change.changeType) }}</div>
            <div class="change-info">
              <div class="change-desc">{{ change.description }}</div>
              <div class="change-time">{{ formatTime(change.timestamp) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { TemplateManager } from '../services/TemplateManager';

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 状态
const isProcessing = ref(false);
const instanceStats = ref<any>({});
const conflicts = ref<any[]>([]);
const runningTasks = ref<any[]>([]);
const recentChanges = ref<any[]>([]);

// 服务
const templateManager = TemplateManager.getInstance();

// 方法
const refreshData = async () => {
  try {
    instanceStats.value = templateManager.getInstanceStats();
    conflicts.value = templateManager.getParameterConflicts();
    runningTasks.value = templateManager.getBatchUpdateTasks().filter(task => task.status === 'running');
    recentChanges.value = templateManager.getParameterChangeHistory(undefined, 10);
    
    console.log('✅ 参数同步数据刷新完成');
  } catch (error) {
    console.error('❌ 数据刷新失败:', error);
  }
};

const resolveConflict = async (conflict: any, resolution: 'use_template' | 'keep_instance') => {
  isProcessing.value = true;
  try {
    const success = await templateManager.resolveParameterConflict(conflict.id, resolution);
    if (success) {
      console.log(`✅ 冲突已解决: ${conflict.parameterName}`);
      await refreshData();
    } else {
      console.error(`❌ 冲突解决失败: ${conflict.parameterName}`);
    }
  } catch (error) {
    console.error('冲突解决失败:', error);
  } finally {
    isProcessing.value = false;
  }
};

const batchResolveConflicts = async (strategy: 'smart') => {
  isProcessing.value = true;
  try {
    const templateIds = [...new Set(conflicts.value.map(c => c.templateId))];
    const result = await templateManager.batchResolveConflicts(templateIds, { strategy });
    
    console.log(`✅ 批量冲突解决完成:`, result);
    await refreshData();
  } catch (error) {
    console.error('批量冲突解决失败:', error);
  } finally {
    isProcessing.value = false;
  }
};

const batchSyncAll = async () => {
  isProcessing.value = true;
  try {
    const templates = templateManager.getAllTemplates();
    const templateIds = templates.map(t => t.id);
    
    const result = await templateManager.batchSyncTemplates(templateIds, {
      strategy: 'preserve_overrides'
    });
    
    console.log(`✅ 批量同步完成:`, result);
    await refreshData();
  } catch (error) {
    console.error('批量同步失败:', error);
  } finally {
    isProcessing.value = false;
  }
};

const getChangeIcon = (changeType: string): string => {
  const icons: Record<string, string> = {
    added: '➕',
    removed: '➖',
    modified: '✏️',
    renamed: '🔄',
    type_changed: '🔀'
  };
  return icons[changeType] || '📝';
};

const formatTime = (timestamp: Date): string => {
  const now = new Date();
  const diff = now.getTime() - new Date(timestamp).getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}小时前`;
  
  const days = Math.floor(hours / 24);
  return `${days}天前`;
};

// 生命周期
onMounted(() => {
  refreshData();
  
  // 定期刷新数据
  const interval = setInterval(refreshData, 10000); // 10秒刷新一次
  
  // 组件卸载时清理定时器
  return () => clearInterval(interval);
});
</script>

<style scoped>
.parameter-sync-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.panel-content {
  position: relative;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  background: #1a202c;
  border-radius: 12px;
  border: 1px solid #4a5568;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.panel-header h3 {
  margin: 0;
  color: #f7fafc;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid #4a5568;
  border-radius: 6px;
  color: #a0aec0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

/* 统计卡片样式 */
.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #4a5568;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.stat-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-value {
  color: #f7fafc;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.125rem;
}

.stat-label {
  color: #a0aec0;
  font-size: 0.75rem;
}

/* 冲突列表样式 */
.conflicts-section {
  padding: 1.5rem;
  border-bottom: 1px solid #4a5568;
}

.conflicts-section h4 {
  margin: 0 0 1rem 0;
  color: #fc8181;
  font-size: 1rem;
  font-weight: 600;
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.conflict-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
  border-left: 3px solid #fc8181;
}

.conflict-name {
  color: #f7fafc;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.conflict-desc {
  color: #a0aec0;
  font-size: 0.875rem;
}

.conflict-actions {
  display: flex;
  gap: 0.5rem;
}

.resolve-btn {
  padding: 0.375rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.resolve-btn.template {
  background: #3182ce;
  color: white;
}

.resolve-btn.template:hover {
  background: #2b6cb0;
}

.resolve-btn.instance {
  background: #38a169;
  color: white;
}

.resolve-btn.instance:hover {
  background: #2f855a;
}

.more-conflicts {
  text-align: center;
  color: #a0aec0;
  font-size: 0.875rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #2d3748;
  border-radius: 6px;
}

/* 批量操作样式 */
.batch-actions {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #4a5568;
}

.batch-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.batch-btn.smart {
  background: #805ad5;
  color: white;
}

.batch-btn.smart:hover:not(:disabled) {
  background: #6b46c1;
}

.batch-btn.sync {
  background: #38a169;
  color: white;
}

.batch-btn.sync:hover:not(:disabled) {
  background: #2f855a;
}

.batch-btn.refresh {
  background: #4a5568;
  color: #e2e8f0;
}

.batch-btn.refresh:hover:not(:disabled) {
  background: #718096;
}

.batch-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 任务列表样式 */
.tasks-section {
  padding: 1.5rem;
  border-bottom: 1px solid #4a5568;
}

.tasks-section h4 {
  margin: 0 0 1rem 0;
  color: #63b3ed;
  font-size: 1rem;
  font-weight: 600;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.task-item {
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.task-name {
  color: #f7fafc;
  font-weight: 500;
}

.task-progress {
  color: #a0aec0;
  font-size: 0.875rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #4a5568;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #38a169;
  transition: width 0.3s ease;
}

/* 变更列表样式 */
.changes-section {
  padding: 1.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.changes-section h4 {
  margin: 0 0 1rem 0;
  color: #9ae6b4;
  font-size: 1rem;
  font-weight: 600;
}

.changes-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
}

.change-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

.change-desc {
  color: #e2e8f0;
  font-size: 0.875rem;
  margin-bottom: 0.125rem;
}

.change-time {
  color: #a0aec0;
  font-size: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-content {
    width: 95vw;
    max-height: 90vh;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .batch-actions {
    flex-direction: column;
  }
  
  .conflict-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .conflict-actions {
    justify-content: stretch;
  }
  
  .resolve-btn {
    flex: 1;
  }
}

/* 滚动条样式 */
.changes-section::-webkit-scrollbar {
  width: 6px;
}

.changes-section::-webkit-scrollbar-track {
  background: #2d3748;
}

.changes-section::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.changes-section::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}
</style>
