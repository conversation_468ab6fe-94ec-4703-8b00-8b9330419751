/**
 * NodeSelector.test.ts
 * NodeSelector服务的单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NodeSelector } from '../../services/NodeSelector';
import { TestUtils, createTestTemplate } from '../setup';

describe('NodeSelector', () => {
  let nodeSelector: NodeSelector;

  beforeEach(() => {
    // 每个测试前重置NodeSelector实例
    (NodeSelector as any).instance = null;
    nodeSelector = NodeSelector.getInstance();
    TestUtils.cleanup();
  });

  afterEach(() => {
    TestUtils.cleanup();
  });

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = NodeSelector.getInstance();
      const instance2 = NodeSelector.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('节点选择', () => {
    let mockNode: any;

    beforeEach(() => {
      mockNode = {
        id: 'test-node-1',
        shape: 'rect',
        position: { x: 100, y: 200 },
        size: { width: 120, height: 80 },
        attrs: {
          body: {
            fill: '#ffffff',
            stroke: '#000000'
          },
          text: {
            text: '测试节点',
            fontSize: 14
          }
        },
        data: {
          nodeType: 'action-highlight',
          displayName: '高亮动作',
          color: [1, 0, 0],
          duration: 1000
        },
        getProp: vi.fn((key: string) => {
          const props: any = {
            position: mockNode.position,
            size: mockNode.size,
            attrs: mockNode.attrs,
            data: mockNode.data
          };
          return props[key];
        }),
        getPosition: vi.fn(() => mockNode.position),
        getSize: vi.fn(() => mockNode.size),
        getAttrs: vi.fn(() => mockNode.attrs),
        getData: vi.fn(() => mockNode.data)
      };
    });

    it('应该成功选择节点', () => {
      const result = nodeSelector.selectNode(mockNode);

      expect(result.success).toBe(true);
      expect(result.selectedNode).toBe(mockNode);
      expect(nodeSelector.getSelectedNode()).toBe(mockNode);
    });

    it('应该拒绝无效节点', () => {
      const result = nodeSelector.selectNode(null);

      expect(result.success).toBe(false);
      expect(result.error).toBe('节点不能为空');
      expect(nodeSelector.getSelectedNode()).toBeNull();
    });

    it('应该正确清除选择', () => {
      nodeSelector.selectNode(mockNode);
      expect(nodeSelector.getSelectedNode()).toBe(mockNode);

      nodeSelector.clearSelection();
      expect(nodeSelector.getSelectedNode()).toBeNull();
    });

    it('应该支持多选模式', () => {
      const node2 = { ...mockNode, id: 'test-node-2' };
      
      nodeSelector.selectNode(mockNode);
      nodeSelector.selectNode(node2, { addToSelection: true });

      const selectedNodes = nodeSelector.getSelectedNodes();
      expect(selectedNodes).toHaveLength(2);
      expect(selectedNodes).toContain(mockNode);
      expect(selectedNodes).toContain(node2);
    });

    it('应该正确处理选择变更事件', () => {
      const onSelectionChange = vi.fn();
      nodeSelector.onSelectionChange(onSelectionChange);

      nodeSelector.selectNode(mockNode);

      expect(onSelectionChange).toHaveBeenCalledWith([mockNode]);
    });
  });

  describe('节点分析', () => {
    let mockNode: any;

    beforeEach(() => {
      mockNode = {
        id: 'test-node-1',
        data: {
          nodeType: 'action-highlight',
          displayName: '高亮动作',
          color: [1, 0, 0],
          duration: 1000,
          intensity: 1.5
        },
        attrs: {
          body: {
            fill: '#ffffff',
            stroke: '#000000',
            strokeWidth: 2
          },
          text: {
            text: '测试节点',
            fontSize: 14,
            fill: '#333333'
          }
        },
        position: { x: 100, y: 200 },
        size: { width: 120, height: 80 },
        getData: vi.fn(() => mockNode.data),
        getAttrs: vi.fn(() => mockNode.attrs),
        getPosition: vi.fn(() => mockNode.position),
        getSize: vi.fn(() => mockNode.size)
      };
    });

    it('应该正确分析节点属性', () => {
      const analysis = nodeSelector.analyzeNode(mockNode);

      expect(analysis.success).toBe(true);
      expect(analysis.nodeInfo).toBeDefined();
      expect(analysis.nodeInfo!.id).toBe('test-node-1');
      expect(analysis.nodeInfo!.type).toBe('action-highlight');
      expect(analysis.nodeInfo!.displayName).toBe('高亮动作');
    });

    it('应该正确提取参数', () => {
      const analysis = nodeSelector.analyzeNode(mockNode);

      expect(analysis.parameters).toBeDefined();
      expect(analysis.parameters!.color).toEqual([1, 0, 0]);
      expect(analysis.parameters!.duration).toBe(1000);
      expect(analysis.parameters!.intensity).toBe(1.5);
    });

    it('应该正确分析样式属性', () => {
      const analysis = nodeSelector.analyzeNode(mockNode);

      expect(analysis.styleInfo).toBeDefined();
      expect(analysis.styleInfo!.fill).toBe('#ffffff');
      expect(analysis.styleInfo!.stroke).toBe('#000000');
      expect(analysis.styleInfo!.strokeWidth).toBe(2);
    });

    it('应该正确分析位置和尺寸', () => {
      const analysis = nodeSelector.analyzeNode(mockNode);

      expect(analysis.positionInfo).toBeDefined();
      expect(analysis.positionInfo!.x).toBe(100);
      expect(analysis.positionInfo!.y).toBe(200);
      expect(analysis.positionInfo!.width).toBe(120);
      expect(analysis.positionInfo!.height).toBe(80);
    });

    it('应该处理缺少数据的节点', () => {
      const incompleteNode = {
        id: 'incomplete-node',
        getData: vi.fn(() => ({})),
        getAttrs: vi.fn(() => ({})),
        getPosition: vi.fn(() => ({ x: 0, y: 0 })),
        getSize: vi.fn(() => ({ width: 0, height: 0 }))
      };

      const analysis = nodeSelector.analyzeNode(incompleteNode);

      expect(analysis.success).toBe(true);
      expect(analysis.nodeInfo!.type).toBe('unknown');
      expect(analysis.parameters).toEqual({});
    });
  });

  describe('模板建议', () => {
    let mockNode: any;

    beforeEach(() => {
      mockNode = {
        id: 'test-node-1',
        data: {
          nodeType: 'action-highlight',
          displayName: '高亮动作',
          color: [1, 0, 0],
          duration: 1000
        },
        getData: vi.fn(() => mockNode.data),
        getAttrs: vi.fn(() => ({})),
        getPosition: vi.fn(() => ({ x: 0, y: 0 })),
        getSize: vi.fn(() => ({ width: 100, height: 50 }))
      };
    });

    it('应该生成合适的模板建议', () => {
      const suggestions = nodeSelector.generateTemplateSuggestions(mockNode);

      expect(suggestions.length).toBeGreaterThan(0);
      
      const actionSuggestion = suggestions.find(s => s.type === 'ACTION');
      expect(actionSuggestion).toBeDefined();
      expect(actionSuggestion!.name).toContain('高亮');
      expect(actionSuggestion!.confidence).toBeGreaterThan(0.5);
    });

    it('应该根据节点类型生成不同建议', () => {
      // 测试样式节点
      const styleNode = {
        ...mockNode,
        data: {
          nodeType: 'style',
          backgroundColor: '#ff0000',
          borderColor: '#000000'
        }
      };
      styleNode.getData = vi.fn(() => styleNode.data);

      const styleSuggestions = nodeSelector.generateTemplateSuggestions(styleNode);
      const styleSuggestion = styleSuggestions.find(s => s.type === 'STYLE');
      expect(styleSuggestion).toBeDefined();
    });

    it('应该按置信度排序建议', () => {
      const suggestions = nodeSelector.generateTemplateSuggestions(mockNode);

      for (let i = 1; i < suggestions.length; i++) {
        expect(suggestions[i].confidence).toBeLessThanOrEqual(suggestions[i - 1].confidence);
      }
    });

    it('应该过滤低置信度建议', () => {
      const suggestions = nodeSelector.generateTemplateSuggestions(mockNode, { minConfidence: 0.8 });

      suggestions.forEach(suggestion => {
        expect(suggestion.confidence).toBeGreaterThanOrEqual(0.8);
      });
    });
  });

  describe('批量操作', () => {
    let mockNodes: any[];

    beforeEach(() => {
      mockNodes = [
        {
          id: 'node-1',
          data: { nodeType: 'action-highlight', color: [1, 0, 0] },
          getData: vi.fn(function() { return this.data; }),
          getAttrs: vi.fn(() => ({})),
          getPosition: vi.fn(() => ({ x: 0, y: 0 })),
          getSize: vi.fn(() => ({ width: 100, height: 50 }))
        },
        {
          id: 'node-2',
          data: { nodeType: 'action-highlight', color: [0, 1, 0] },
          getData: vi.fn(function() { return this.data; }),
          getAttrs: vi.fn(() => ({})),
          getPosition: vi.fn(() => ({ x: 100, y: 0 })),
          getSize: vi.fn(() => ({ width: 100, height: 50 }))
        },
        {
          id: 'node-3',
          data: { nodeType: 'style', backgroundColor: '#ffffff' },
          getData: vi.fn(function() { return this.data; }),
          getAttrs: vi.fn(() => ({})),
          getPosition: vi.fn(() => ({ x: 200, y: 0 })),
          getSize: vi.fn(() => ({ width: 100, height: 50 }))
        }
      ];
    });

    it('应该支持批量选择', () => {
      const result = nodeSelector.selectMultipleNodes(mockNodes);

      expect(result.success).toBe(true);
      expect(result.selectedCount).toBe(3);
      expect(nodeSelector.getSelectedNodes()).toHaveLength(3);
    });

    it('应该支持批量分析', () => {
      const analyses = nodeSelector.analyzeMultipleNodes(mockNodes);

      expect(analyses).toHaveLength(3);
      analyses.forEach(analysis => {
        expect(analysis.success).toBe(true);
        expect(analysis.nodeInfo).toBeDefined();
      });
    });

    it('应该支持按类型分组', () => {
      const grouped = nodeSelector.groupNodesByType(mockNodes);

      expect(grouped['action-highlight']).toHaveLength(2);
      expect(grouped['style']).toHaveLength(1);
    });

    it('应该支持相似节点查找', () => {
      const referenceNode = mockNodes[0];
      const similarNodes = nodeSelector.findSimilarNodes(referenceNode, mockNodes);

      expect(similarNodes).toHaveLength(1); // 应该找到另一个 action-highlight 节点
      expect(similarNodes[0].id).toBe('node-2');
    });
  });

  describe('过滤和搜索', () => {
    let mockNodes: any[];

    beforeEach(() => {
      mockNodes = [
        {
          id: 'highlight-node',
          data: { nodeType: 'action-highlight', displayName: '高亮动作' },
          getData: vi.fn(function() { return this.data; })
        },
        {
          id: 'callback-node',
          data: { nodeType: 'action-callback', displayName: '回调动作' },
          getData: vi.fn(function() { return this.data; })
        },
        {
          id: 'style-node',
          data: { nodeType: 'style', displayName: '样式节点' },
          getData: vi.fn(function() { return this.data; })
        }
      ];
    });

    it('应该支持按类型过滤', () => {
      const actionNodes = nodeSelector.filterNodesByType(mockNodes, 'action');

      expect(actionNodes).toHaveLength(2);
      actionNodes.forEach(node => {
        expect(node.data.nodeType).toMatch(/^action-/);
      });
    });

    it('应该支持按名称搜索', () => {
      const highlightNodes = nodeSelector.searchNodesByName(mockNodes, '高亮');

      expect(highlightNodes).toHaveLength(1);
      expect(highlightNodes[0].id).toBe('highlight-node');
    });

    it('应该支持复合条件过滤', () => {
      const filtered = nodeSelector.filterNodes(mockNodes, {
        type: 'action',
        nameContains: '动作'
      });

      expect(filtered).toHaveLength(2);
    });

    it('应该支持自定义过滤函数', () => {
      const customFiltered = nodeSelector.filterNodes(mockNodes, {
        custom: (node: any) => node.id.includes('callback')
      });

      expect(customFiltered).toHaveLength(1);
      expect(customFiltered[0].id).toBe('callback-node');
    });
  });

  describe('事件处理', () => {
    let mockNode: any;
    let eventHandler: any;

    beforeEach(() => {
      mockNode = {
        id: 'test-node',
        data: { nodeType: 'action-highlight' },
        getData: vi.fn(() => mockNode.data)
      };
      eventHandler = vi.fn();
    });

    it('应该正确注册和触发选择事件', () => {
      nodeSelector.onSelectionChange(eventHandler);
      nodeSelector.selectNode(mockNode);

      expect(eventHandler).toHaveBeenCalledWith([mockNode]);
    });

    it('应该正确注册和触发分析事件', () => {
      nodeSelector.onNodeAnalyzed(eventHandler);
      nodeSelector.analyzeNode(mockNode);

      expect(eventHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          nodeInfo: expect.any(Object)
        })
      );
    });

    it('应该支持取消事件监听', () => {
      const unsubscribe = nodeSelector.onSelectionChange(eventHandler);
      unsubscribe();

      nodeSelector.selectNode(mockNode);
      expect(eventHandler).not.toHaveBeenCalled();
    });

    it('应该支持多个事件监听器', () => {
      const handler1 = vi.fn();
      const handler2 = vi.fn();

      nodeSelector.onSelectionChange(handler1);
      nodeSelector.onSelectionChange(handler2);
      nodeSelector.selectNode(mockNode);

      expect(handler1).toHaveBeenCalled();
      expect(handler2).toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    it('应该正确处理无效节点', () => {
      const result = nodeSelector.analyzeNode(null);

      expect(result.success).toBe(false);
      expect(result.error).toBe('节点不能为空');
    });

    it('应该正确处理节点方法调用失败', () => {
      const faultyNode = {
        id: 'faulty-node',
        getData: vi.fn(() => {
          throw new Error('获取数据失败');
        })
      };

      const result = nodeSelector.analyzeNode(faultyNode);

      expect(result.success).toBe(false);
      expect(result.error).toContain('获取数据失败');
    });

    it('应该正确处理批量操作中的部分失败', () => {
      const mixedNodes = [
        {
          id: 'good-node',
          getData: vi.fn(() => ({ nodeType: 'action' }))
        },
        {
          id: 'bad-node',
          getData: vi.fn(() => {
            throw new Error('数据错误');
          })
        }
      ];

      const analyses = nodeSelector.analyzeMultipleNodes(mixedNodes);

      expect(analyses).toHaveLength(2);
      expect(analyses[0].success).toBe(true);
      expect(analyses[1].success).toBe(false);
    });
  });
});
