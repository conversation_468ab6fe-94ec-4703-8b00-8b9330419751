/**
 * 模板系统调试工具
 * 在浏览器控制台中使用
 */

// 检查模板系统状态
function checkTemplateSystemStatus() {
  console.log('🔍 检查模板系统状态...');
  
  // 检查是否有模板管理器
  if (typeof window.debugTemplateSystem === 'function') {
    console.log('✅ 调试函数可用');
    return window.debugTemplateSystem();
  } else {
    console.log('❌ 调试函数不可用');
    
    // 尝试直接访问Vue应用
    const app = document.querySelector('#app');
    if (app && app.__vue__) {
      console.log('✅ 找到Vue应用实例');
      return app.__vue__;
    } else {
      console.log('❌ 无法找到Vue应用实例');
      return null;
    }
  }
}

// 检查DOM元素
function checkDOMElements() {
  console.log('🔍 检查DOM元素...');
  
  const elements = {
    'app容器': document.querySelector('#app'),
    '模板面板容器': document.querySelector('.template-panel-container'),
    '模板面板': document.querySelector('.template-panel'),
    '模板库': document.querySelector('.template-library'),
    '模板分类': document.querySelectorAll('.category-section'),
    '模板项目': document.querySelectorAll('.template-item'),
    '属性面板': document.querySelector('.properties-panel'),
    '模板选择器': document.querySelectorAll('.simple-template-selector'),
    '工具栏按钮': document.querySelectorAll('.toolbar-btn')
  };
  
  Object.entries(elements).forEach(([name, element]) => {
    if (element) {
      if (element.length !== undefined) {
        console.log(`✅ ${name}: 找到 ${element.length} 个元素`);
      } else {
        console.log(`✅ ${name}: 存在`);
      }
    } else {
      console.log(`❌ ${name}: 不存在`);
    }
  });
  
  return elements;
}

// 模拟点击测试按钮
function clickTestButton() {
  console.log('🖱️ 尝试点击测试按钮...');
  
  const testBtn = document.querySelector('.test-btn');
  if (testBtn) {
    console.log('✅ 找到测试按钮，点击中...');
    testBtn.click();
    return true;
  } else {
    console.log('❌ 没有找到测试按钮');
    return false;
  }
}

// 模拟点击模板面板切换按钮
function toggleTemplatePanel() {
  console.log('🖱️ 尝试切换模板面板...');
  
  const templateBtn = document.querySelector('.template-btn');
  if (templateBtn) {
    console.log('✅ 找到模板面板按钮，点击中...');
    templateBtn.click();
    return true;
  } else {
    console.log('❌ 没有找到模板面板按钮');
    return false;
  }
}

// 检查控制台错误
function checkConsoleErrors() {
  console.log('🔍 检查控制台错误...');
  console.log('💡 请查看浏览器控制台中是否有红色的错误信息');
  console.log('💡 常见错误类型:');
  console.log('  - 模块导入错误');
  console.log('  - TypeScript类型错误');
  console.log('  - Vue组件渲染错误');
  console.log('  - 网络请求错误');
}

// 综合诊断
function diagnoseTemplateSystem() {
  console.log('🏥 开始模板系统综合诊断...');
  console.log('=====================================');
  
  // 1. 检查DOM元素
  console.log('\n1️⃣ DOM元素检查:');
  const elements = checkDOMElements();
  
  // 2. 检查模板系统状态
  console.log('\n2️⃣ 模板系统状态检查:');
  const templateSystem = checkTemplateSystemStatus();
  
  // 3. 尝试点击测试按钮
  console.log('\n3️⃣ 测试按钮检查:');
  const testBtnClicked = clickTestButton();
  
  // 4. 检查模板面板显示
  console.log('\n4️⃣ 模板面板显示检查:');
  const templatePanelContainer = elements['模板面板容器'];
  if (templatePanelContainer) {
    const isVisible = templatePanelContainer.style.display !== 'none' && 
                     templatePanelContainer.offsetHeight > 0;
    console.log(`模板面板可见性: ${isVisible ? '✅ 可见' : '❌ 隐藏'}`);
    
    if (!isVisible) {
      console.log('💡 尝试显示模板面板...');
      toggleTemplatePanel();
    }
  }
  
  // 5. 检查控制台错误
  console.log('\n5️⃣ 控制台错误检查:');
  checkConsoleErrors();
  
  // 6. 生成诊断报告
  console.log('\n📋 诊断报告:');
  const report = {
    domElements: Object.keys(elements).filter(key => elements[key]).length,
    templateSystem: !!templateSystem,
    testButton: testBtnClicked,
    templatePanel: !!templatePanelContainer,
    timestamp: new Date().toISOString()
  };
  
  console.log(report);
  console.log('\n🎯 建议的下一步操作:');
  
  if (!templateSystem) {
    console.log('❗ 模板系统未正确初始化，检查App.vue中的初始化逻辑');
  }
  
  if (!templatePanelContainer) {
    console.log('❗ 模板面板容器不存在，检查App.vue中的模板');
  }
  
  if (!testBtnClicked) {
    console.log('❗ 测试按钮不可用，检查工具栏的实现');
  }
  
  console.log('=====================================');
  console.log('🏥 诊断完成！');
  
  return report;
}

// 快速修复尝试
function quickFix() {
  console.log('🔧 尝试快速修复...');
  
  // 1. 尝试显示模板面板
  toggleTemplatePanel();
  
  // 2. 等待一下再点击测试按钮
  setTimeout(() => {
    clickTestButton();
  }, 1000);
  
  // 3. 再等待一下检查结果
  setTimeout(() => {
    console.log('🔍 快速修复后的状态检查:');
    checkDOMElements();
  }, 2000);
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.checkTemplateSystemStatus = checkTemplateSystemStatus;
  window.checkDOMElements = checkDOMElements;
  window.clickTestButton = clickTestButton;
  window.toggleTemplatePanel = toggleTemplatePanel;
  window.checkConsoleErrors = checkConsoleErrors;
  window.diagnoseTemplateSystem = diagnoseTemplateSystem;
  window.quickFix = quickFix;
  
  console.log('🛠️ 模板系统调试工具已加载！');
  console.log('可用函数:');
  console.log('- checkTemplateSystemStatus() - 检查模板系统状态');
  console.log('- checkDOMElements() - 检查DOM元素');
  console.log('- clickTestButton() - 点击测试按钮');
  console.log('- toggleTemplatePanel() - 切换模板面板');
  console.log('- diagnoseTemplateSystem() - 综合诊断');
  console.log('- quickFix() - 快速修复尝试');
  console.log('');
  console.log('💡 建议先运行: diagnoseTemplateSystem()');
}

// 自动运行初始检查
setTimeout(() => {
  console.log('🚀 自动运行初始诊断...');
  diagnoseTemplateSystem();
}, 2000);
