/**
 * TemplateManager.test.ts
 * TemplateManager服务的单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TemplateManager } from '../../services/TemplateManager';
import { TemplateType } from '../../types/TemplateTypes';
import { TestUtils, TestAssertions, createTestTemplate, createTestInstance } from '../setup';

describe('TemplateManager', () => {
  let templateManager: TemplateManager;

  beforeEach(() => {
    // 每个测试前重置TemplateManager实例
    (TemplateManager as any).instance = null;
    templateManager = TemplateManager.getInstance();
    TestUtils.cleanup();
  });

  afterEach(() => {
    TestUtils.cleanup();
  });

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = TemplateManager.getInstance();
      const instance2 = TemplateManager.getInstance();
      
      expect(instance1).toBe(instance2);
    });

    it('应该正确初始化所有服务', () => {
      expect(templateManager.configImporter).toBeDefined();
      expect(templateManager.configExporter).toBeDefined();
      expect(templateManager.versionManager).toBeDefined();
      expect(templateManager.instanceTracker).toBeDefined();
      expect(templateManager.parameterSyncManager).toBeDefined();
      expect(templateManager.conflictResolver).toBeDefined();
      expect(templateManager.batchUpdateManager).toBeDefined();
    });
  });

  describe('模板创建', () => {
    it('应该成功创建有效模板', () => {
      const template = createTestTemplate({
        name: '测试网格模板',
        type: TemplateType.MESH
      });

      const result = templateManager.createTemplate(template);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.id).toBe(template.id);
      expect(result.data!.name).toBe(template.name);
    });

    it('应该拒绝无效模板', () => {
      const invalidTemplate = {
        name: '无效模板',
        // 缺少必需字段
      } as any;

      const result = templateManager.createTemplate(invalidTemplate);

      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
    });

    it('应该拒绝重复ID的模板', () => {
      const template1 = createTestTemplate({ id: 'duplicate-id' });
      const template2 = createTestTemplate({ id: 'duplicate-id' });

      const result1 = templateManager.createTemplate(template1);
      const result2 = templateManager.createTemplate(template2);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(false);
      expect(result2.errors).toContain('模板ID已存在');
    });

    it('应该自动生成ID如果未提供', () => {
      const template = createTestTemplate();
      delete (template as any).id;

      const result = templateManager.createTemplate(template);

      expect(result.success).toBe(true);
      expect(result.data!.id).toBeDefined();
      expect(result.data!.id).toMatch(/^template_\d+_[a-z0-9]+$/);
    });

    it('应该正确设置创建时间和更新时间', () => {
      const template = createTestTemplate();
      const beforeCreate = new Date();

      const result = templateManager.createTemplate(template);

      const afterCreate = new Date();
      expect(result.success).toBe(true);
      expect(result.data!.metadata.createdAt.getTime()).toBeGreaterThanOrEqual(beforeCreate.getTime());
      expect(result.data!.metadata.createdAt.getTime()).toBeLessThanOrEqual(afterCreate.getTime());
      expect(result.data!.metadata.updatedAt.getTime()).toBeGreaterThanOrEqual(beforeCreate.getTime());
      expect(result.data!.metadata.updatedAt.getTime()).toBeLessThanOrEqual(afterCreate.getTime());
    });
  });

  describe('模板查询', () => {
    beforeEach(() => {
      // 创建测试数据
      const templates = TestUtils.createTestTemplates(5);
      templates.forEach(template => {
        templateManager.createTemplate(template);
      });
    });

    it('应该返回所有模板', () => {
      const templates = templateManager.getAllTemplates();
      expect(templates.length).toBe(5);
    });

    it('应该根据ID查找模板', () => {
      const templates = templateManager.getAllTemplates();
      const firstTemplate = templates[0];

      const found = templateManager.getTemplate(firstTemplate.id);
      expect(found).toBeDefined();
      expect(found!.id).toBe(firstTemplate.id);
    });

    it('应该返回undefined对于不存在的模板', () => {
      const found = templateManager.getTemplate('non-existent-id');
      expect(found).toBeUndefined();
    });

    it('应该根据类型过滤模板', () => {
      const meshTemplates = templateManager.getTemplatesByType(TemplateType.MESH);
      const styleTemplates = templateManager.getTemplatesByType(TemplateType.STYLE);

      expect(meshTemplates.length).toBeGreaterThan(0);
      expect(styleTemplates.length).toBeGreaterThan(0);
      
      meshTemplates.forEach(template => {
        expect(template.type).toBe(TemplateType.MESH);
      });
      
      styleTemplates.forEach(template => {
        expect(template.type).toBe(TemplateType.STYLE);
      });
    });

    it('应该根据分类过滤模板', () => {
      const categories = ['网格', '样式', '动作'];
      
      categories.forEach(category => {
        const templates = templateManager.getTemplatesByCategory(category);
        templates.forEach(template => {
          expect(template.metadata.category).toBe(category);
        });
      });
    });

    it('应该支持搜索功能', () => {
      // 创建特定名称的模板
      const searchTemplate = createTestTemplate({
        name: '特殊搜索模板',
        metadata: {
          ...createTestTemplate().metadata,
          tags: ['search', 'special']
        }
      });
      templateManager.createTemplate(searchTemplate);

      const results = templateManager.searchTemplates('特殊搜索');
      expect(results.length).toBeGreaterThan(0);
      expect(results.some(t => t.name.includes('特殊搜索'))).toBe(true);
    });
  });

  describe('模板更新', () => {
    let testTemplate: any;

    beforeEach(() => {
      testTemplate = createTestTemplate();
      templateManager.createTemplate(testTemplate);
    });

    it('应该成功更新模板', () => {
      const updates = {
        name: '更新后的模板名称',
        description: '更新后的描述'
      };

      const result = templateManager.updateTemplate(testTemplate.id, updates);

      expect(result.success).toBe(true);
      expect(result.data!.name).toBe(updates.name);
      expect(result.data!.description).toBe(updates.description);
      expect(result.data!.metadata.updatedAt.getTime()).toBeGreaterThan(testTemplate.metadata.updatedAt.getTime());
    });

    it('应该拒绝更新不存在的模板', () => {
      const result = templateManager.updateTemplate('non-existent-id', { name: '新名称' });

      expect(result.success).toBe(false);
      expect(result.errors).toContain('模板不存在');
    });

    it('应该验证更新数据的有效性', () => {
      const invalidUpdates = {
        type: 'invalid-type' as any
      };

      const result = templateManager.updateTemplate(testTemplate.id, invalidUpdates);

      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
    });

    it('应该正确处理版本更新', () => {
      const updates = {
        data: ['new-mesh-1', 'new-mesh-2']
      };

      const result = templateManager.updateTemplate(testTemplate.id, updates);

      expect(result.success).toBe(true);
      expect(result.data!.version).not.toBe(testTemplate.version);
      expect(result.data!.data).toEqual(updates.data);
    });
  });

  describe('模板删除', () => {
    let testTemplate: any;

    beforeEach(() => {
      testTemplate = createTestTemplate();
      templateManager.createTemplate(testTemplate);
    });

    it('应该成功删除模板', () => {
      const result = templateManager.deleteTemplate(testTemplate.id);

      expect(result.success).toBe(true);
      expect(templateManager.getTemplate(testTemplate.id)).toBeUndefined();
    });

    it('应该拒绝删除不存在的模板', () => {
      const result = templateManager.deleteTemplate('non-existent-id');

      expect(result.success).toBe(false);
      expect(result.errors).toContain('模板不存在');
    });

    it('应该拒绝删除有活跃实例的模板', () => {
      // 创建实例
      const instance = createTestInstance(testTemplate.id);
      templateManager.instanceTracker.trackInstance(instance);

      const result = templateManager.deleteTemplate(testTemplate.id);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('模板有活跃实例，无法删除');
    });

    it('应该支持强制删除', () => {
      // 创建实例
      const instance = createTestInstance(testTemplate.id);
      templateManager.instanceTracker.trackInstance(instance);

      const result = templateManager.deleteTemplate(testTemplate.id, { force: true });

      expect(result.success).toBe(true);
      expect(templateManager.getTemplate(testTemplate.id)).toBeUndefined();
    });
  });

  describe('模板实例化', () => {
    let testTemplate: any;

    beforeEach(() => {
      testTemplate = createTestTemplate();
      templateManager.createTemplate(testTemplate);
    });

    it('应该成功实例化模板', async () => {
      const parameters = {
        color: '#ff0000',
        size: 2.0
      };

      const result = await templateManager.instantiateTemplate(
        testTemplate.id,
        'test-scene',
        'test-node',
        parameters
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.templateId).toBe(testTemplate.id);
      expect(result.data!.parameters).toEqual(parameters);
    });

    it('应该拒绝实例化不存在的模板', async () => {
      const result = await templateManager.instantiateTemplate(
        'non-existent-id',
        'test-scene',
        'test-node',
        {}
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('模板不存在');
    });

    it('应该验证必需参数', async () => {
      const parameters = {
        color: '#ff0000'
        // 缺少必需的 size 参数
      };

      const result = await templateManager.instantiateTemplate(
        testTemplate.id,
        'test-scene',
        'test-node',
        parameters
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('缺少必需参数: size');
    });

    it('应该应用默认参数值', async () => {
      const parameters = {
        size: 2.0
        // 不提供 color 参数，应该使用默认值
      };

      const result = await templateManager.instantiateTemplate(
        testTemplate.id,
        'test-scene',
        'test-node',
        parameters
      );

      expect(result.success).toBe(true);
      expect(result.data!.parameters.color).toBe('#ffffff'); // 默认值
      expect(result.data!.parameters.size).toBe(2.0);
    });
  });

  describe('统计信息', () => {
    beforeEach(() => {
      // 创建测试数据
      const templates = TestUtils.createTestTemplates(3);
      templates.forEach(template => {
        templateManager.createTemplate(template);
        
        // 为每个模板创建一些实例
        const instances = TestUtils.createTestInstances(template.id, 2);
        instances.forEach(instance => {
          templateManager.instanceTracker.trackInstance(instance);
        });
      });
    });

    it('应该返回正确的使用统计', () => {
      const stats = templateManager.getUsageStats();

      expect(stats.totalTemplates).toBe(3);
      expect(stats.totalInstances).toBe(6);
      expect(stats.templatesByType).toBeDefined();
      expect(stats.templatesByCategory).toBeDefined();
    });

    it('应该返回正确的实例统计', () => {
      const stats = templateManager.getInstanceStats();

      expect(stats.total).toBe(6);
      expect(stats.active).toBeDefined();
      expect(stats.inactive).toBeDefined();
      expect(stats.byTemplate).toBeDefined();
    });

    it('应该返回性能指标', () => {
      const metrics = templateManager.getPerformanceMetrics();

      expect(metrics.templateCount).toBe(3);
      expect(metrics.instanceCount).toBe(6);
      expect(metrics.memoryUsage).toBeDefined();
      expect(metrics.averageInstantiationTime).toBeDefined();
    });
  });

  describe('错误处理', () => {
    it('应该正确处理无效输入', () => {
      expect(() => {
        templateManager.createTemplate(null as any);
      }).not.toThrow();

      const result = templateManager.createTemplate(null as any);
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
    });

    it('应该正确处理异步操作错误', async () => {
      // 模拟异步错误
      vi.spyOn(templateManager.instanceTracker, 'trackInstance').mockRejectedValue(new Error('异步错误'));

      const template = createTestTemplate();
      templateManager.createTemplate(template);

      const result = await templateManager.instantiateTemplate(
        template.id,
        'test-scene',
        'test-node',
        { size: 1.0 }
      );

      expect(result.success).toBe(false);
      expect(result.errors).toContain('实例化失败');
    });

    it('应该正确处理存储错误', () => {
      // 模拟存储错误
      const originalSetItem = Storage.prototype.setItem;
      Storage.prototype.setItem = vi.fn(() => {
        throw new Error('存储空间不足');
      });

      const template = createTestTemplate();
      const result = templateManager.createTemplate(template);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('存储失败');

      // 恢复原始方法
      Storage.prototype.setItem = originalSetItem;
    });
  });

  describe('并发处理', () => {
    it('应该正确处理并发创建', async () => {
      const templates = TestUtils.createTestTemplates(10);

      const promises = templates.map(template =>
        Promise.resolve(templateManager.createTemplate(template))
      );

      const results = await Promise.all(promises);

      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBe(10);
    });

    it('应该正确处理并发实例化', async () => {
      const template = createTestTemplate();
      templateManager.createTemplate(template);

      const promises = Array.from({ length: 5 }, (_, index) =>
        templateManager.instantiateTemplate(
          template.id,
          'test-scene',
          `test-node-${index}`,
          { size: 1.0 }
        )
      );

      const results = await Promise.all(promises);

      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBe(5);
    });
  });

  describe('清理和重置', () => {
    it('应该正确清理所有数据', () => {
      // 创建一些测试数据
      const templates = TestUtils.createTestTemplates(3);
      templates.forEach(template => {
        templateManager.createTemplate(template);
      });

      expect(templateManager.getAllTemplates().length).toBe(3);

      // 清理数据
      templateManager.clear();

      expect(templateManager.getAllTemplates().length).toBe(0);
    });

    it('应该正确重置统计信息', () => {
      // 创建一些测试数据
      const templates = TestUtils.createTestTemplates(2);
      templates.forEach(template => {
        templateManager.createTemplate(template);
      });

      const statsBefore = templateManager.getUsageStats();
      expect(statsBefore.totalTemplates).toBe(2);

      // 重置
      templateManager.reset();

      const statsAfter = templateManager.getUsageStats();
      expect(statsAfter.totalTemplates).toBe(0);
    });
  });
});
