# meshNames 设计方案

## 问题分析

在配置文件中，`meshNames` 是一个关键属性，用于指定3D场景中的目标网格。它在不同的配置上下文中有不同的格式和用途：

### 使用场景

1. **Actions配置中** - 指定事件的目标网格
2. **StaticLabels配置中** - 指定标签要显示在哪些网格上
3. **模板扩展中** - 覆盖模板的默认网格列表

### 格式类型

#### 1. 直接数组格式
```javascript
meshNames: ["电气楼五层", "NA_LSZSC_QITA04", "NA_LSZSC_QITA03"]
```

#### 2. 模板引用格式
```javascript
meshNames: {
  $ref: "templates.1.meshes.buildingLevels"
}
```

#### 3. 模板定义
```javascript
templates: {
  1: {
    meshes: {
      buildingLevels: ["电气楼五层", "NA_LSZSC_QITA04", "NA_LSZSC_QITA03"],
      gisDoors: ["DOOR_NA_LSZ_GIS_MOD105", "DOOR_NA_LSZ_GIS_MOD108"]
    }
  }
}
```

## 设计方案

### 方案选择：集成到现有节点 ✅

经过评估，我们选择将`meshNames`配置集成到现有节点中，而不是创建独立的MeshSelector节点。

#### 优点
- **语义完整性**：保持配置的逻辑完整性，meshNames与其使用上下文紧密相关
- **简化节点图**：避免增加额外的节点和连接，保持图的简洁性
- **用户友好**：符合用户的心理模型，meshNames是事件/标签的属性而不是独立实体

#### 缺点
- **节点复杂度**：单个节点需要处理更多的配置选项
- **重复配置**：相同的meshNames可能在多个节点中重复配置

## 实现方案

### 1. 数据结构扩展

#### EventNodeData接口扩展
```typescript
export interface EventNodeData extends BaseNodeData {
  nodeType: "event";
  actionType: string;
  meshNames: string[] | { $ref: string };
  description?: string;
  // 新增字段
  meshNamesIsRef?: boolean; // 是否为引用格式
  meshNamesRef?: string;    // 引用路径
}
```

### 2. UI设计

#### 事件节点属性面板
```
┌─────────────────────────────────────┐
│ 事件设置                            │
├─────────────────────────────────────┤
│ 事件类型: [双击 ▼]                  │
│                                     │
│ 目标网格 (meshNames):               │
│ ○ 直接指定  ● 模板引用              │
│                                     │
│ 引用路径:                           │
│ [templates.1.meshes.buildingLevels] │
│ 输入模板引用路径                    │
│                                     │
│ 描述: [事件描述]                    │
└─────────────────────────────────────┘
```

#### 直接指定模式
```
┌─────────────────────────────────────┐
│ 目标网格 (meshNames):               │
│ ● 直接指定  ○ 模板引用              │
│                                     │
│ 网格列表:                           │
│ ┌─────────────────────────────────┐ │
│ │ ["mesh1", "mesh2", "mesh3"]     │ │
│ │                                 │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│ 输入JSON格式的网格名称数组          │
└─────────────────────────────────────┘
```

### 3. 配置解析逻辑

#### ConfigParser扩展
```typescript
// 判断meshNames是否为引用格式
private isMeshNamesReference(meshNames: any): boolean {
  return meshNames && typeof meshNames === 'object' && meshNames.$ref;
}

// 提取meshNames的引用路径
private extractMeshNamesRef(meshNames: any): string {
  if (this.isMeshNamesReference(meshNames)) {
    return meshNames.$ref;
  }
  return "";
}
```

#### 事件节点创建逻辑
```typescript
const eventNode: EventNodeData = {
  id: this.generateNodeId("event"),
  nodeType: "event",
  displayName: this.getEventDisplayName(action.actionType),
  actionType: action.actionType,
  meshNames: action.meshNames || { $ref: "templates.1.meshes.buildingLevels" },
  description: action.config?.description,
  // 添加meshNames的处理逻辑
  meshNamesIsRef: this.isMeshNamesReference(action.meshNames),
  meshNamesRef: this.extractMeshNamesRef(action.meshNames),
};
```

### 4. UI交互逻辑

#### PropertiesPanel扩展
```typescript
// 更新事件节点的网格名称
function updateEventMeshNames() {
  if (!nodeData.value) return;
  
  try {
    const parsed = JSON.parse(eventMeshNamesText.value || "[]");
    nodeData.value.meshNames = parsed;
    nodeData.value.meshNamesIsRef = false;
    updateNodeData();
  } catch (error) {
    console.error("事件网格名称JSON格式错误:", error);
  }
}
```

#### 初始化逻辑
```typescript
// 如果是事件节点，初始化meshNames相关字段
if (nodeData.value?.nodeType === "event") {
  // 判断meshNames是引用还是直接数组
  if (nodeData.value.meshNames && typeof nodeData.value.meshNames === 'object' && nodeData.value.meshNames.$ref) {
    nodeData.value.meshNamesIsRef = true;
    nodeData.value.meshNamesRef = nodeData.value.meshNames.$ref;
    eventMeshNamesText.value = "";
  } else {
    nodeData.value.meshNamesIsRef = false;
    nodeData.value.meshNamesRef = "";
    eventMeshNamesText.value = JSON.stringify(nodeData.value.meshNames || [], null, 2);
  }
}
```

## 扩展计划

### 1. 静态标签节点
- 已实现基础的meshNames支持
- 可以进一步扩展支持引用/直接模式切换

### 2. 模板管理器
- 创建专门的模板管理界面
- 支持templates.meshes的可视化编辑
- 提供meshNames的自动完成功能

### 3. 网格选择器组件
- 创建可重用的网格选择器组件
- 支持多选、搜索、分组等功能
- 集成到各种需要meshNames配置的地方

### 4. 验证和提示
- 实时验证meshNames的有效性
- 提供网格名称的智能提示
- 检测未使用的网格和缺失的网格

## 测试策略

### 1. 单元测试
- 测试不同格式meshNames的解析
- 测试UI交互的数据绑定
- 测试配置的序列化和反序列化

### 2. 集成测试
- 使用真实配置文件测试完整流程
- 测试节点图的生成和编辑
- 测试配置的导入导出

### 3. 用户体验测试
- 测试UI的易用性和直观性
- 测试错误处理和用户提示
- 测试性能和响应速度

## 总结

通过将meshNames配置集成到现有节点中，我们实现了：

1. **完整的配置支持** - 支持直接数组和模板引用两种格式
2. **直观的UI设计** - 提供清晰的模式切换和编辑界面
3. **智能的解析逻辑** - 自动识别和处理不同格式的meshNames
4. **良好的扩展性** - 为未来的功能扩展奠定了基础

这个设计方案在保持系统简洁性的同时，提供了强大的meshNames配置能力，满足了复杂3D场景配置的需求。
