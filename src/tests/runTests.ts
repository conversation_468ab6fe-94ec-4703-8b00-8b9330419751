/**
 * runTests.ts
 * 测试运行器 - 提供编程式测试执行和报告
 */

import { TestUtils, TestAssertions } from './setup';

/**
 * 测试套件接口
 */
interface TestSuite {
  name: string;
  tests: Test[];
  setup?: () => void | Promise<void>;
  teardown?: () => void | Promise<void>;
}

/**
 * 测试用例接口
 */
interface Test {
  name: string;
  fn: () => void | Promise<void>;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}

/**
 * 测试结果接口
 */
interface TestResult {
  name: string;
  passed: boolean;
  error?: Error;
  duration: number;
}

/**
 * 测试套件结果接口
 */
interface SuiteResult {
  name: string;
  tests: TestResult[];
  passed: boolean;
  duration: number;
  passedCount: number;
  failedCount: number;
  skippedCount: number;
}

/**
 * 测试运行器
 */
export class TestRunner {
  private suites: TestSuite[] = [];
  private results: SuiteResult[] = [];
  private verbose = true;

  /**
   * 添加测试套件
   */
  addSuite(suite: TestSuite): void {
    this.suites.push(suite);
  }

  /**
   * 运行所有测试
   */
  async runAll(): Promise<{ passed: boolean; results: SuiteResult[] }> {
    console.log('🧪 开始运行模板系统测试...\n');
    
    this.results = [];
    let totalPassed = 0;
    let totalFailed = 0;
    let totalSkipped = 0;

    for (const suite of this.suites) {
      const result = await this.runSuite(suite);
      this.results.push(result);
      
      totalPassed += result.passedCount;
      totalFailed += result.failedCount;
      totalSkipped += result.skippedCount;
    }

    // 打印总结
    console.log('\n📊 测试总结:');
    console.log(`✅ 通过: ${totalPassed}`);
    console.log(`❌ 失败: ${totalFailed}`);
    console.log(`⏭️  跳过: ${totalSkipped}`);
    console.log(`📈 总计: ${totalPassed + totalFailed + totalSkipped}`);

    const overallPassed = totalFailed === 0;
    console.log(`\n🎯 整体结果: ${overallPassed ? '✅ 通过' : '❌ 失败'}`);

    return {
      passed: overallPassed,
      results: this.results
    };
  }

  /**
   * 运行单个测试套件
   */
  private async runSuite(suite: TestSuite): Promise<SuiteResult> {
    const startTime = Date.now();
    
    if (this.verbose) {
      console.log(`📦 运行测试套件: ${suite.name}`);
    }

    const results: TestResult[] = [];
    let passedCount = 0;
    let failedCount = 0;
    let skippedCount = 0;

    // 运行setup
    try {
      if (suite.setup) {
        await suite.setup();
      }
    } catch (error) {
      console.error(`❌ 套件setup失败: ${suite.name}`, error);
      return {
        name: suite.name,
        tests: [],
        passed: false,
        duration: Date.now() - startTime,
        passedCount: 0,
        failedCount: 1,
        skippedCount: 0
      };
    }

    // 运行测试
    for (const test of suite.tests) {
      if (test.skip) {
        results.push({
          name: test.name,
          passed: true,
          duration: 0
        });
        skippedCount++;
        if (this.verbose) {
          console.log(`  ⏭️  ${test.name} (跳过)`);
        }
        continue;
      }

      const testResult = await this.runTest(test);
      results.push(testResult);

      if (testResult.passed) {
        passedCount++;
        if (this.verbose) {
          console.log(`  ✅ ${test.name} (${testResult.duration}ms)`);
        }
      } else {
        failedCount++;
        if (this.verbose) {
          console.log(`  ❌ ${test.name} (${testResult.duration}ms)`);
          if (testResult.error) {
            console.log(`     错误: ${testResult.error.message}`);
          }
        }
      }
    }

    // 运行teardown
    try {
      if (suite.teardown) {
        await suite.teardown();
      }
    } catch (error) {
      console.error(`⚠️  套件teardown失败: ${suite.name}`, error);
    }

    const duration = Date.now() - startTime;
    const passed = failedCount === 0;

    if (this.verbose) {
      console.log(`  📊 ${suite.name}: ${passedCount}通过, ${failedCount}失败, ${skippedCount}跳过 (${duration}ms)\n`);
    }

    return {
      name: suite.name,
      tests: results,
      passed,
      duration,
      passedCount,
      failedCount,
      skippedCount
    };
  }

  /**
   * 运行单个测试
   */
  private async runTest(test: Test): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const timeout = test.timeout || 5000;
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`测试超时 (${timeout}ms)`)), timeout);
      });

      await Promise.race([
        Promise.resolve(test.fn()),
        timeoutPromise
      ]);

      return {
        name: test.name,
        passed: true,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        name: test.name,
        passed: false,
        error: error as Error,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * 生成HTML报告
   */
  generateHtmlReport(): string {
    const totalTests = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passedCount, 0);
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failedCount, 0);
    const totalSkipped = this.results.reduce((sum, suite) => sum + suite.skippedCount, 0);

    return `
<!DOCTYPE html>
<html>
<head>
    <title>模板系统测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .suite { margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .suite-header { background: #e9e9e9; padding: 10px; font-weight: bold; }
        .test { padding: 8px 15px; border-bottom: 1px solid #eee; }
        .test:last-child { border-bottom: none; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #6c757d; }
        .error { background: #f8d7da; padding: 10px; margin-top: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>模板系统测试报告</h1>
    
    <div class="summary">
        <h2>测试总结</h2>
        <p>总计: ${totalTests} | 通过: <span class="passed">${totalPassed}</span> | 失败: <span class="failed">${totalFailed}</span> | 跳过: <span class="skipped">${totalSkipped}</span></p>
        <p>成功率: ${totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0}%</p>
    </div>

    ${this.results.map(suite => `
        <div class="suite">
            <div class="suite-header">
                ${suite.name} (${suite.duration}ms)
                - 通过: ${suite.passedCount}, 失败: ${suite.failedCount}, 跳过: ${suite.skippedCount}
            </div>
            ${suite.tests.map(test => `
                <div class="test">
                    <span class="${test.passed ? 'passed' : 'failed'}">
                        ${test.passed ? '✅' : '❌'} ${test.name} (${test.duration}ms)
                    </span>
                    ${test.error ? `<div class="error">错误: ${test.error.message}</div>` : ''}
                </div>
            `).join('')}
        </div>
    `).join('')}

    <p><small>生成时间: ${new Date().toLocaleString()}</small></p>
</body>
</html>
    `.trim();
  }

  /**
   * 设置详细输出
   */
  setVerbose(verbose: boolean): void {
    this.verbose = verbose;
  }
}

/**
 * 创建简单的测试套件
 */
export function createTestSuite(name: string): {
  suite: TestSuite;
  test: (name: string, fn: () => void | Promise<void>, options?: { timeout?: number; skip?: boolean; only?: boolean }) => void;
  beforeAll: (fn: () => void | Promise<void>) => void;
  afterAll: (fn: () => void | Promise<void>) => void;
} {
  const suite: TestSuite = {
    name,
    tests: []
  };

  return {
    suite,
    test: (name: string, fn: () => void | Promise<void>, options = {}) => {
      suite.tests.push({
        name,
        fn,
        timeout: options.timeout,
        skip: options.skip,
        only: options.only
      });
    },
    beforeAll: (fn: () => void | Promise<void>) => {
      suite.setup = fn;
    },
    afterAll: (fn: () => void | Promise<void>) => {
      suite.teardown = fn;
    }
  };
}

/**
 * 快速测试函数
 */
export async function quickTest(): Promise<void> {
  console.log('🚀 运行快速测试...\n');

  const runner = new TestRunner();

  // 基础功能测试
  const { suite: basicSuite, test: basicTest } = createTestSuite('基础功能测试');
  
  basicTest('TestUtils.createTestTemplate', () => {
    const template = TestUtils.createTestTemplate();
    TestAssertions.assertTrue(TestUtils.validateTemplate(template));
    TestAssertions.assertEqual(template.type, 'MESH');
  });

  basicTest('TestUtils.createTestInstance', () => {
    const template = TestUtils.createTestTemplate();
    const instance = TestUtils.createTestInstance(template.id);
    TestAssertions.assertTrue(TestUtils.validateInstance(instance));
    TestAssertions.assertEqual(instance.templateId, template.id);
  });

  basicTest('TestUtils.deepEqual', () => {
    const obj1 = { a: 1, b: { c: 2 } };
    const obj2 = { a: 1, b: { c: 2 } };
    const obj3 = { a: 1, b: { c: 3 } };
    
    TestAssertions.assertTrue(TestUtils.deepEqual(obj1, obj2));
    TestAssertions.assertFalse(TestUtils.deepEqual(obj1, obj3));
  });

  runner.addSuite(basicSuite);

  // 运行测试
  const result = await runner.runAll();
  
  if (result.passed) {
    console.log('🎉 快速测试通过！');
  } else {
    console.log('❌ 快速测试失败！');
  }
}

// 如果直接运行此文件，执行快速测试
if (typeof window === 'undefined' && require.main === module) {
  quickTest().catch(console.error);
}

// 导出到全局对象（用于浏览器控制台）
if (typeof window !== 'undefined') {
  (window as any).TestRunner = TestRunner;
  (window as any).createTestSuite = createTestSuite;
  (window as any).quickTest = quickTest;
  (window as any).TestUtils = TestUtils;
  (window as any).TestAssertions = TestAssertions;
  
  console.log('🧪 测试工具已添加到全局对象');
  console.log('💡 运行 quickTest() 开始快速测试');
}
