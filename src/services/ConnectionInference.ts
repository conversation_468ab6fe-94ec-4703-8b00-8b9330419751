/**
 * 连接推断算法 - 根据配置中的逻辑关系自动推断节点间的连接关系
 */

import type { 
  ParsedSceneData, 
  BaseNodeData, 
  ConnectionData,
  EventNodeData,
  ActionNodeData,
  LifecycleNodeData,
  StaticLabelNodeData,
  ReferenceNodeData,
  DataNodeData
} from "./ConfigParser";

/**
 * 连接推断规则接口
 */
export interface ConnectionRule {
  // 事件 → 动作连接
  eventToAction(eventNode: EventNodeData, actionNodes: ActionNodeData[]): ConnectionData[];
  
  // 生命周期 → 回调连接
  lifecycleToCallback(lifecycleNode: LifecycleNodeData, actionNodes: ActionNodeData[]): ConnectionData[];
  
  // 数据流连接
  dataFlow(dataNodes: DataNodeData[]): ConnectionData[];
  
  // 静态标签 → 事件连接
  staticLabelToEvent(labelNode: StaticLabelNodeData, eventNodes: EventNodeData[]): ConnectionData[];
}

/**
 * 连接推断器类
 */
export class ConnectionInference implements ConnectionRule {
  private connectionIdCounter = 0;

  /**
   * 生成唯一连接ID
   */
  private generateConnectionId(): string {
    return `connection-${++this.connectionIdCounter}`;
  }

  /**
   * 推断所有节点间的连接关系
   */
  inferAllConnections(parsedData: ParsedSceneData): ConnectionData[] {
    const connections: ConnectionData[] = [];

    // 1. 事件节点到动作节点的连接
    parsedData.eventNodes.forEach(eventNode => {
      const eventConnections = this.eventToAction(eventNode, parsedData.actionNodes);
      connections.push(...eventConnections);
    });

    // 2. 生命周期节点到回调节点的连接
    parsedData.lifecycleNodes.forEach(lifecycleNode => {
      const lifecycleConnections = this.lifecycleToCallback(lifecycleNode, parsedData.actionNodes);
      connections.push(...lifecycleConnections);
    });

    // 3. 数据流连接
    const dataFlowConnections = this.dataFlow(parsedData.dataNodes);
    connections.push(...dataFlowConnections);

    // 4. 静态标签到事件的连接
    parsedData.staticLabelNodes.forEach(labelNode => {
      const labelConnections = this.staticLabelToEvent(labelNode, parsedData.eventNodes);
      connections.push(...labelConnections);
    });

    // 5. 场景配置到生命周期的连接
    if (parsedData.sceneConfig && parsedData.lifecycleNodes.length > 0) {
      const sceneToLifecycleConnections = this.sceneConfigToLifecycle(
        parsedData.sceneConfig, 
        parsedData.lifecycleNodes
      );
      connections.push(...sceneToLifecycleConnections);
    }

    return connections;
  }

  /**
   * 事件节点到动作节点的连接推断
   */
  eventToAction(eventNode: EventNodeData, actionNodes: ActionNodeData[]): ConnectionData[] {
    const connections: ConnectionData[] = [];
    
    // 查找与此事件相关的动作节点
    const relatedActions = actionNodes.filter(actionNode => {
      // 基于Y坐标相近程度判断是否相关（同一行的节点）
      const yDiff = Math.abs((actionNode.y || 0) - (eventNode.y || 0));
      return yDiff < 50; // 50像素内认为是同一行
    });

    // 按X坐标排序，确保连接顺序正确
    relatedActions.sort((a, b) => (a.x || 0) - (b.x || 0));

    // 创建串行连接
    if (relatedActions.length > 0) {
      // 事件节点连接到第一个动作节点
      connections.push({
        id: this.generateConnectionId(),
        source: eventNode.id,
        target: relatedActions[0].id,
        sourcePort: "out-exec",
        targetPort: "in-exec",
      });

      // 动作节点之间的串行连接
      for (let i = 0; i < relatedActions.length - 1; i++) {
        connections.push({
          id: this.generateConnectionId(),
          source: relatedActions[i].id,
          target: relatedActions[i + 1].id,
          sourcePort: "out-exec",
          targetPort: "in-exec",
        });
      }
    }

    return connections;
  }

  /**
   * 生命周期节点到回调节点的连接推断
   */
  lifecycleToCallback(lifecycleNode: LifecycleNodeData, actionNodes: ActionNodeData[]): ConnectionData[] {
    const connections: ConnectionData[] = [];
    
    // 查找与此生命周期相关的动作节点
    const relatedActions = actionNodes.filter(actionNode => {
      // 基于Y坐标相近程度判断是否相关
      const yDiff = Math.abs((actionNode.y || 0) - (lifecycleNode.y || 0));
      return yDiff < 50;
    });

    // 按X坐标排序
    relatedActions.sort((a, b) => (a.x || 0) - (b.x || 0));

    // 创建连接
    if (relatedActions.length > 0) {
      connections.push({
        id: this.generateConnectionId(),
        source: lifecycleNode.id,
        target: relatedActions[0].id,
        sourcePort: "out-exec",
        targetPort: "in-exec",
      });

      // 动作节点之间的串行连接
      for (let i = 0; i < relatedActions.length - 1; i++) {
        connections.push({
          id: this.generateConnectionId(),
          source: relatedActions[i].id,
          target: relatedActions[i + 1].id,
          sourcePort: "out-exec",
          targetPort: "in-exec",
        });
      }
    }

    return connections;
  }

  /**
   * 数据流连接推断
   */
  dataFlow(dataNodes: DataNodeData[]): ConnectionData[] {
    const connections: ConnectionData[] = [];
    
    // 按节点类型分组
    const dataSources = dataNodes.filter(node => node.nodeType === "data-source");
    const dataTransforms = dataNodes.filter(node => node.nodeType === "data-transform");
    const dataConsumers = dataNodes.filter(node => node.nodeType === "data-consumer" || node.nodeType === "data-driven-service");

    // 数据源 → 数据转换 → 数据消费的连接
    dataSources.forEach(source => {
      // 查找相近的转换节点
      const nearbyTransforms = dataTransforms.filter(transform => {
        const yDiff = Math.abs((transform.y || 0) - (source.y || 0));
        return yDiff < 100;
      });

      if (nearbyTransforms.length > 0) {
        // 连接到最近的转换节点
        const nearestTransform = nearbyTransforms.reduce((nearest, current) => {
          const nearestDistance = Math.abs((nearest.x || 0) - (source.x || 0));
          const currentDistance = Math.abs((current.x || 0) - (source.x || 0));
          return currentDistance < nearestDistance ? current : nearest;
        });

        connections.push({
          id: this.generateConnectionId(),
          source: source.id,
          target: nearestTransform.id,
          sourcePort: "out-data",
          targetPort: "in-data",
        });
      } else {
        // 直接连接到消费节点
        const nearbyConsumers = dataConsumers.filter(consumer => {
          const yDiff = Math.abs((consumer.y || 0) - (source.y || 0));
          return yDiff < 100;
        });

        if (nearbyConsumers.length > 0) {
          connections.push({
            id: this.generateConnectionId(),
            source: source.id,
            target: nearbyConsumers[0].id,
            sourcePort: "out-data",
            targetPort: "in-data",
          });
        }
      }
    });

    // 转换节点 → 消费节点的连接
    dataTransforms.forEach(transform => {
      const nearbyConsumers = dataConsumers.filter(consumer => {
        const yDiff = Math.abs((consumer.y || 0) - (transform.y || 0));
        return yDiff < 100;
      });

      if (nearbyConsumers.length > 0) {
        connections.push({
          id: this.generateConnectionId(),
          source: transform.id,
          target: nearbyConsumers[0].id,
          sourcePort: "out-data",
          targetPort: "in-data",
        });
      }
    });

    return connections;
  }

  /**
   * 静态标签到事件的连接推断
   */
  staticLabelToEvent(labelNode: StaticLabelNodeData, eventNodes: EventNodeData[]): ConnectionData[] {
    const connections: ConnectionData[] = [];
    
    // 查找与此标签相关的点击事件
    const relatedEvents = eventNodes.filter(eventNode => {
      // 检查是否是点击事件且目标网格匹配
      if (eventNode.actionType === "click") {
        const eventMeshNames = Array.isArray(eventNode.meshNames) ? eventNode.meshNames : [];
        const labelMeshNames = labelNode.meshNames || [];
        
        // 检查是否有重叠的网格名称
        return eventMeshNames.some(meshName => labelMeshNames.includes(meshName));
      }
      return false;
    });

    // 创建连接
    relatedEvents.forEach(eventNode => {
      connections.push({
        id: this.generateConnectionId(),
        source: labelNode.id,
        target: eventNode.id,
        sourcePort: "out-click",
        targetPort: "in-trigger",
      });
    });

    return connections;
  }

  /**
   * 场景配置到生命周期的连接推断
   */
  private sceneConfigToLifecycle(sceneConfig: any, lifecycleNodes: LifecycleNodeData[]): ConnectionData[] {
    const connections: ConnectionData[] = [];
    
    // 场景配置节点连接到所有生命周期节点
    lifecycleNodes.forEach(lifecycleNode => {
      connections.push({
        id: this.generateConnectionId(),
        source: sceneConfig.id,
        target: lifecycleNode.id,
        sourcePort: "out-exec",
        targetPort: "in-trigger",
      });
    });

    return connections;
  }

  /**
   * 优化连接布局，避免交叉
   */
  optimizeConnections(connections: ConnectionData[], nodes: BaseNodeData[]): ConnectionData[] {
    // 这里可以实现连接优化算法
    // 目前返回原始连接
    return connections;
  }
}
