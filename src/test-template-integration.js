/**
 * 模板系统集成测试
 * 测试模板系统与节点编辑器的集成
 */

// 创建测试事件节点的函数
function createTestEventNode() {
  console.log('🎯 创建测试事件节点...');
  
  // 检查是否有LogicEditor实例
  const logicEditor = document.querySelector('.logic-editor');
  if (!logicEditor) {
    console.error('❌ 没有找到LogicEditor');
    return false;
  }
  
  // 尝试通过Vue实例访问图形编辑器
  try {
    // 这里需要根据实际的Vue组件结构来访问
    console.log('💡 请手动从节点库拖拽一个事件节点到画布上');
    console.log('💡 然后选中该节点，查看属性面板中的模板选择器');
    return true;
  } catch (error) {
    console.error('❌ 创建测试节点失败:', error);
    return false;
  }
}

// 测试模板选择器功能
function testTemplateSelector() {
  console.log('🎯 测试模板选择器功能...');
  
  // 查找模板选择器
  const templateSelectors = document.querySelectorAll('.simple-template-selector');
  console.log(`找到 ${templateSelectors.length} 个模板选择器`);
  
  if (templateSelectors.length === 0) {
    console.log('⚠️ 没有找到模板选择器');
    console.log('💡 请先选中一个事件节点，然后查看属性面板');
    return false;
  }
  
  // 测试第一个模板选择器
  const selector = templateSelectors[0];
  console.log('✅ 找到模板选择器:', selector);
  
  // 检查下拉按钮
  const dropdownBtn = selector.querySelector('.dropdown-btn');
  if (dropdownBtn) {
    console.log('✅ 找到下拉按钮，尝试点击...');
    dropdownBtn.click();
    
    // 等待一下检查下拉列表
    setTimeout(() => {
      const dropdownList = selector.querySelector('.dropdown-list');
      if (dropdownList) {
        console.log('✅ 下拉列表显示成功');
        const options = dropdownList.querySelectorAll('.template-option');
        console.log(`📋 找到 ${options.length} 个模板选项`);
        
        // 显示所有选项
        options.forEach((option, index) => {
          const name = option.querySelector('.template-name')?.textContent;
          const path = option.querySelector('.template-path')?.textContent;
          console.log(`  ${index + 1}. ${name} - ${path}`);
        });
        
        // 尝试选择第一个选项
        if (options.length > 0) {
          console.log('🖱️ 尝试选择第一个模板选项...');
          options[0].click();
        }
      } else {
        console.log('❌ 下拉列表没有显示');
      }
    }, 500);
    
    return true;
  } else {
    console.log('❌ 没有找到下拉按钮');
    return false;
  }
}

// 测试模板应用功能
function testTemplateApplication() {
  console.log('🎯 测试模板应用功能...');
  
  // 检查是否有选中的节点
  const propertiesPanel = document.querySelector('.properties-panel');
  if (!propertiesPanel) {
    console.log('❌ 没有找到属性面板');
    return false;
  }
  
  // 查找事件节点的配置区域
  const eventSection = propertiesPanel.querySelector('[class*="event"]');
  if (!eventSection) {
    console.log('⚠️ 没有找到事件节点配置区域');
    console.log('💡 请先选中一个事件节点');
    return false;
  }
  
  console.log('✅ 找到事件节点配置区域');
  
  // 查找meshNames相关的配置
  const meshNamesSection = propertiesPanel.querySelector('[class*="mesh-names"]');
  if (meshNamesSection) {
    console.log('✅ 找到meshNames配置区域');
    
    // 查找单选按钮
    const radioButtons = meshNamesSection.querySelectorAll('input[type="radio"]');
    console.log(`📻 找到 ${radioButtons.length} 个单选按钮`);
    
    // 查找模板选择器
    const templateSelector = meshNamesSection.querySelector('.simple-template-selector');
    if (templateSelector) {
      console.log('✅ 找到模板选择器');
      return testTemplateSelector();
    } else {
      console.log('❌ 没有找到模板选择器');
      return false;
    }
  } else {
    console.log('❌ 没有找到meshNames配置区域');
    return false;
  }
}

// 测试模板面板功能
function testTemplatePanel() {
  console.log('🎨 测试模板面板功能...');
  
  // 查找模板面板
  const templatePanel = document.querySelector('.template-panel');
  if (!templatePanel) {
    console.log('❌ 没有找到模板面板');
    console.log('💡 请点击"显示模板面板"按钮');
    return false;
  }
  
  console.log('✅ 找到模板面板');
  
  // 检查模板库
  const templateLibrary = templatePanel.querySelector('.template-library');
  if (templateLibrary) {
    console.log('✅ 找到模板库');
    
    // 检查是否有模板分类
    const categories = templateLibrary.querySelectorAll('.category-section');
    console.log(`📁 找到 ${categories.length} 个模板分类`);
    
    if (categories.length > 0) {
      // 展开第一个分类
      const firstCategory = categories[0];
      const categoryHeader = firstCategory.querySelector('.category-header');
      if (categoryHeader) {
        console.log('🖱️ 点击第一个分类展开...');
        categoryHeader.click();
        
        // 等待一下检查模板项目
        setTimeout(() => {
          const templateItems = firstCategory.querySelectorAll('.template-item');
          console.log(`📄 找到 ${templateItems.length} 个模板项目`);
          
          templateItems.forEach((item, index) => {
            const name = item.querySelector('.template-name')?.textContent;
            const description = item.querySelector('.template-description')?.textContent;
            console.log(`  ${index + 1}. ${name} - ${description}`);
          });
          
          // 尝试选择第一个模板
          if (templateItems.length > 0) {
            console.log('🖱️ 选择第一个模板...');
            templateItems[0].click();
          }
        }, 500);
      }
    } else {
      // 检查是否显示空状态
      const emptyLibrary = templateLibrary.querySelector('.empty-library');
      if (emptyLibrary) {
        console.log('📂 显示空状态，尝试创建模板...');
        const createBtn = emptyLibrary.querySelector('.create-btn');
        if (createBtn) {
          console.log('🖱️ 点击创建按钮...');
          createBtn.click();
        }
      } else {
        console.log('❌ 没有找到模板分类或空状态');
      }
    }
    
    return true;
  } else {
    console.log('❌ 没有找到模板库');
    return false;
  }
}

// 综合集成测试
function runIntegrationTest() {
  console.log('🚀 开始模板系统集成测试...');
  console.log('=====================================');
  
  const results = {
    templatePanel: false,
    templateSelector: false,
    templateApplication: false,
    nodeCreation: false
  };
  
  // 1. 测试模板面板
  console.log('\n1️⃣ 测试模板面板...');
  results.templatePanel = testTemplatePanel();
  
  // 2. 测试节点创建
  console.log('\n2️⃣ 测试节点创建...');
  results.nodeCreation = createTestEventNode();
  
  // 3. 等待用户操作后测试模板应用
  console.log('\n3️⃣ 等待用户操作...');
  console.log('💡 请按以下步骤操作:');
  console.log('   1. 从节点库拖拽一个事件节点到画布');
  console.log('   2. 选中该事件节点');
  console.log('   3. 在控制台运行: testTemplateApplication()');
  
  // 4. 生成测试报告
  setTimeout(() => {
    console.log('\n📋 集成测试报告:');
    console.log(`模板面板: ${results.templatePanel ? '✅ 通过' : '❌ 失败'}`);
    console.log(`节点创建: ${results.nodeCreation ? '✅ 通过' : '❌ 失败'}`);
    console.log(`模板选择器: 待测试 (需要用户操作)`);
    console.log(`模板应用: 待测试 (需要用户操作)`);
    
    console.log('\n💡 下一步建议:');
    if (!results.templatePanel) {
      console.log('- 检查模板面板是否正确显示');
      console.log('- 运行 debugTemplateSystem() 检查模板数据');
    }
    if (!results.nodeCreation) {
      console.log('- 检查节点库是否正常工作');
      console.log('- 手动创建事件节点进行测试');
    }
    
    console.log('=====================================');
    console.log('🏁 集成测试完成！');
  }, 1000);
  
  return results;
}

// 快速验证所有功能
function quickValidation() {
  console.log('⚡ 快速验证模板系统功能...');
  
  // 1. 检查模板管理器
  if (typeof window.debugTemplateSystem === 'function') {
    console.log('✅ 模板管理器可用');
    const templateManager = window.debugTemplateSystem();
    if (templateManager && templateManager.registry) {
      const templateCount = templateManager.registry.getAll().length;
      console.log(`✅ 模板数量: ${templateCount}`);
    }
  } else {
    console.log('❌ 模板管理器不可用');
  }
  
  // 2. 检查UI组件
  const components = {
    '模板面板': document.querySelector('.template-panel'),
    '模板选择器': document.querySelectorAll('.simple-template-selector'),
    '属性面板': document.querySelector('.properties-panel'),
    '节点库': document.querySelector('.node-library')
  };
  
  Object.entries(components).forEach(([name, element]) => {
    if (element) {
      if (element.length !== undefined) {
        console.log(`✅ ${name}: ${element.length} 个`);
      } else {
        console.log(`✅ ${name}: 存在`);
      }
    } else {
      console.log(`❌ ${name}: 不存在`);
    }
  });
  
  // 3. 提供操作建议
  console.log('\n💡 操作建议:');
  console.log('1. 运行 runIntegrationTest() 进行完整测试');
  console.log('2. 手动创建事件节点测试模板选择器');
  console.log('3. 在模板面板中创建和编辑模板');
  
  return components;
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.createTestEventNode = createTestEventNode;
  window.testTemplateSelector = testTemplateSelector;
  window.testTemplateApplication = testTemplateApplication;
  window.testTemplatePanel = testTemplatePanel;
  window.runIntegrationTest = runIntegrationTest;
  window.quickValidation = quickValidation;
  
  console.log('🧪 模板系统集成测试工具已加载！');
  console.log('可用函数:');
  console.log('- quickValidation() - 快速验证');
  console.log('- runIntegrationTest() - 完整集成测试');
  console.log('- testTemplatePanel() - 测试模板面板');
  console.log('- testTemplateApplication() - 测试模板应用');
  console.log('');
  console.log('💡 建议先运行: quickValidation()');
}

export { 
  createTestEventNode, 
  testTemplateSelector, 
  testTemplateApplication, 
  testTemplatePanel, 
  runIntegrationTest, 
  quickValidation 
};
