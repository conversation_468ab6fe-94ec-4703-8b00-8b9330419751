<template>
  <div class="template-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3>模板管理器</h3>
      <div class="header-actions">
        <button @click="togglePanel" class="toggle-btn">
          {{ isExpanded ? '收起' : '展开' }}
        </button>
        <button @click="refreshTemplates" class="refresh-btn">刷新</button>
      </div>
    </div>

    <!-- 面板内容 -->
    <div v-if="isExpanded" class="panel-content">
      <!-- 搜索栏 -->
      <div class="search-section">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索模板..."
          class="search-input"
        />
        <div class="filter-buttons">
          <button
            v-for="type in templateTypes"
            :key="type"
            @click="toggleTypeFilter(type)"
            :class="['filter-btn', { active: activeFilters.includes(type) }]"
          >
            {{ getTypeDisplayName(type) }}
          </button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 模板库 -->
        <div class="template-library">
          <h4>模板库</h4>

          <!-- 空状态显示 -->
          <div v-if="filteredCategories.length === 0" class="empty-library">
            <div class="empty-icon">📂</div>
            <p>暂无可用模板</p>
            <p class="empty-hint">模板系统可能正在初始化，或者还没有创建任何模板</p>
            <button @click="createNewTemplate" class="create-btn">
              ➕ 创建第一个模板
            </button>
            <button @click="refreshTemplates" class="refresh-btn">
              🔄 刷新模板
            </button>
          </div>

          <div v-else class="template-categories">
            <div
              v-for="category in filteredCategories"
              :key="category"
              class="category-section"
            >
              <div
                @click="toggleCategory(category)"
                class="category-header"
                :class="{ expanded: expandedCategories.includes(category) }"
              >
                <span class="category-icon">📁</span>
                <span class="category-name">{{ category }}</span>
                <span class="template-count">({{ getCategoryTemplateCount(category) }})</span>
              </div>
              
              <div
                v-if="expandedCategories.includes(category)"
                class="category-templates"
              >
                <div
                  v-for="template in getCategoryTemplates(category)"
                  :key="template.id"
                  @click="selectTemplate(template)"
                  @dblclick="applyTemplate(template)"
                  class="template-item"
                  :class="{ selected: selectedTemplate?.id === template.id }"
                >
                  <span class="template-icon">{{ getTemplateIcon(template.type) }}</span>
                  <div class="template-info">
                    <div class="template-name">{{ template.name }}</div>
                    <div class="template-description">{{ template.description }}</div>
                  </div>
                  <div class="template-actions">
                    <button @click.stop="editTemplate(template)" class="action-btn edit">
                      📝
                    </button>
                    <button @click.stop="duplicateTemplate(template)" class="action-btn duplicate">
                      📋
                    </button>
                    <button @click.stop="deleteTemplate(template)" class="action-btn delete">
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板预览/编辑器 -->
        <div class="template-editor-section">
          <div v-if="!selectedTemplate" class="no-selection">
            <p>选择一个模板来查看详情</p>
            <div class="action-buttons">
              <button @click="createNewTemplate" class="create-btn">
                ➕ 创建新模板
              </button>
              <button @click="openNodeToTemplateDialog" class="create-btn secondary">
                🔄 从节点创建
              </button>
            </div>
          </div>

          <div v-else class="template-details">
            <!-- 模板预览模式 -->
            <div v-if="!isEditing" class="template-preview">
              <div class="preview-header">
                <h4>{{ selectedTemplate.name }}</h4>
                <div class="preview-actions">
                  <button @click="startEditing" class="edit-btn">编辑</button>
                  <button @click="applyTemplate(selectedTemplate)" class="apply-btn">
                    应用到节点
                  </button>
                </div>
              </div>
              
              <div class="template-meta">
                <div class="meta-item">
                  <label>类型:</label>
                  <span>{{ getTypeDisplayName(selectedTemplate.type) }}</span>
                </div>
                <div class="meta-item">
                  <label>分类:</label>
                  <span>{{ selectedTemplate.metadata.category }}</span>
                </div>
                <div class="meta-item">
                  <label>使用次数:</label>
                  <span>{{ selectedTemplate.metadata.usage }}</span>
                </div>
                <div class="meta-item">
                  <label>路径:</label>
                  <span class="template-path">{{ selectedTemplate.path }}</span>
                </div>
              </div>

              <div class="template-data-preview">
                <label>模板数据:</label>
                <pre class="data-preview">{{ formatTemplateData(selectedTemplate.data) }}</pre>
              </div>

              <div class="template-tags">
                <label>标签:</label>
                <div class="tags">
                  <span
                    v-for="tag in selectedTemplate.metadata.tags"
                    :key="tag"
                    class="tag"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>

              <div class="reference-example">
                <label>引用示例:</label>
                <code class="reference-code">
                  { "$ref": "{{ selectedTemplate.path }}" }
                </code>
                <button @click="copyReference" class="copy-btn">复制</button>
              </div>
            </div>

            <!-- 模板编辑模式 -->
            <div v-else class="template-editor">
              <div class="editor-header">
                <h4>编辑模板</h4>
                <div class="editor-actions">
                  <button @click="saveTemplate" class="save-btn">保存</button>
                  <button @click="cancelEditing" class="cancel-btn">取消</button>
                </div>
              </div>

              <div class="editor-form">
                <div class="form-group">
                  <label>名称:</label>
                  <input v-model="editingTemplate.name" type="text" />
                </div>
                
                <div class="form-group">
                  <label>类型:</label>
                  <select v-model="editingTemplate.type">
                    <option v-for="type in templateTypes" :key="type" :value="type">
                      {{ getTypeDisplayName(type) }}
                    </option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label>分类:</label>
                  <input v-model="editingTemplate.metadata!.category" type="text" />
                </div>
                
                <div class="form-group">
                  <label>描述:</label>
                  <textarea v-model="editingTemplate.description" rows="2"></textarea>
                </div>
                
                <div class="form-group">
                  <label>模板数据:</label>
                  <textarea
                    v-model="editingTemplateDataText"
                    rows="8"
                    class="data-editor"
                    @blur="validateTemplateData"
                  ></textarea>
                  <div v-if="validationErrors.length > 0" class="validation-errors">
                    <div v-for="error in validationErrors" :key="error" class="error">
                      {{ error }}
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label>标签 (用逗号分隔):</label>
                  <input
                    v-model="editingTagsText"
                    type="text"
                    placeholder="标签1, 标签2, 标签3"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-item">
          <span class="stats-label">总模板数:</span>
          <span class="stats-value">{{ templateStats.totalTemplates }}</span>
        </div>
        <div class="stats-item">
          <span class="stats-label">已选择:</span>
          <span class="stats-value">{{ selectedTemplate?.name || '无' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 节点转模板对话框 -->
  <NodeToTemplateDialog
    :is-visible="showNodeToTemplateDialog"
    @close="() => showNodeToTemplateDialog = false"
    @created="handleTemplateCreated"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { TemplateManager, Template, TemplateType } from '../services/TemplateManager';
import NodeToTemplateDialog from './NodeToTemplateDialog.vue';

// 组件状态
const isExpanded = ref(true);
const searchQuery = ref('');
const activeFilters = ref<TemplateType[]>([]);
const expandedCategories = ref<string[]>([]);
const selectedTemplate = ref<Template | null>(null);
const isEditing = ref(false);
const editingTemplate = ref<Partial<Template>>({});
const editingTemplateDataText = ref('');
const editingTagsText = ref('');
const showNodeToTemplateDialog = ref(false);
const validationErrors = ref<string[]>([]);

// 模板管理器实例
const templateManager = TemplateManager.getInstance();

// 模板类型列表
const templateTypes = Object.values(TemplateType);

// 计算属性
const allTemplates = computed(() => templateManager.registry.getAll());

const filteredTemplates = computed(() => {
  let templates = allTemplates.value;

  // 搜索过滤
  if (searchQuery.value) {
    templates = templateManager.registry.search(searchQuery.value);
  }

  // 类型过滤
  if (activeFilters.value.length > 0) {
    templates = templates.filter(template => 
      activeFilters.value.includes(template.type)
    );
  }

  return templates;
});

const filteredCategories = computed(() => {
  const categories = new Set(filteredTemplates.value.map(t => t.metadata.category));
  return Array.from(categories).sort();
});

const templateStats = computed(() => templateManager.getUsageStats());

// 方法
const togglePanel = () => {
  isExpanded.value = !isExpanded.value;
};

const refreshTemplates = () => {
  // 重新加载模板
  console.log('刷新模板...');
  console.log('当前模板数量:', allTemplates.value.length);
  console.log('模板列表:', allTemplates.value.map(t => t.name));

  // 如果没有模板，尝试重新初始化
  if (allTemplates.value.length === 0) {
    console.log('没有模板，尝试重新初始化...');
    // 触发父组件的模板系统初始化
    if (typeof window !== 'undefined' && (window as any).debugTemplateSystem) {
      (window as any).debugTemplateSystem();
    }
  }

  // 强制更新视图
  selectedTemplate.value = null;
  isEditing.value = false;
};

const toggleTypeFilter = (type: TemplateType) => {
  const index = activeFilters.value.indexOf(type);
  if (index > -1) {
    activeFilters.value.splice(index, 1);
  } else {
    activeFilters.value.push(type);
  }
};

const toggleCategory = (category: string) => {
  const index = expandedCategories.value.indexOf(category);
  if (index > -1) {
    expandedCategories.value.splice(index, 1);
  } else {
    expandedCategories.value.push(category);
  }
};

const getCategoryTemplateCount = (category: string): number => {
  return filteredTemplates.value.filter(t => t.metadata.category === category).length;
};

const getCategoryTemplates = (category: string): Template[] => {
  return filteredTemplates.value.filter(t => t.metadata.category === category);
};

const selectTemplate = (template: Template) => {
  selectedTemplate.value = template;
  isEditing.value = false;
};

const applyTemplate = (template: Template) => {
  // 发送事件给父组件
  console.log('应用模板:', template);
  // TODO: 实现模板应用逻辑
};

const editTemplate = (template: Template) => {
  selectedTemplate.value = template;
  startEditing();
};

const duplicateTemplate = (template: Template) => {
  const newTemplate = {
    ...template,
    id: `${template.id}-copy`,
    name: `${template.name} (副本)`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  templateManager.createTemplate(newTemplate);
};

const deleteTemplate = (template: Template) => {
  if (confirm(`确定要删除模板 "${template.name}" 吗？`)) {
    templateManager.deleteTemplate(template.id);
    if (selectedTemplate.value?.id === template.id) {
      selectedTemplate.value = null;
    }
  }
};

const createNewTemplate = () => {
  console.log('创建新模板...');

  // 根据当前过滤的类型设置默认类型
  let defaultType = TemplateType.MESH;
  if (activeFilters.value.length === 1) {
    defaultType = activeFilters.value[0];
  }

  // 根据类型设置默认数据
  let defaultData: any = [];
  let defaultDataText = '[]';

  switch (defaultType) {
    case TemplateType.MESH:
      defaultData = ['mesh1', 'mesh2'];
      defaultDataText = '["mesh1", "mesh2"]';
      break;
    case TemplateType.STYLE:
      defaultData = { backgroundColor: '#ffffff', color: '#000000' };
      defaultDataText = '{\n  "backgroundColor": "#ffffff",\n  "color": "#000000"\n}';
      break;
    case TemplateType.ACTION:
      defaultData = { type: 'click', highlight: { color: [1, 0, 0], duration: 500 } };
      defaultDataText = '{\n  "type": "click",\n  "highlight": {\n    "color": [1, 0, 0],\n    "duration": 500\n  }\n}';
      break;
    case TemplateType.POSITION:
      defaultData = [0, 0, 0];
      defaultDataText = '[0, 0, 0]';
      break;
  }

  editingTemplate.value = {
    name: `新${getTypeDisplayName(defaultType)}模板`,
    type: defaultType,
    description: `自定义的${getTypeDisplayName(defaultType)}模板`,
    data: defaultData,
    metadata: {
      tags: ['custom', defaultType],
      author: 'user',
      usage: 0,
      dependencies: [],
      category: '自定义',
      sourceType: 'user-created' as const,
    },
  };
  editingTemplateDataText.value = defaultDataText;
  editingTagsText.value = `custom, ${defaultType}`;
  selectedTemplate.value = null;
  isEditing.value = true;

  console.log('新模板模板已准备:', editingTemplate.value);
};

const openNodeToTemplateDialog = () => {
  console.log('打开节点转模板对话框');
  showNodeToTemplateDialog.value = true;
};

const handleTemplateCreated = async (templateData: any) => {
  console.log('处理模板创建:', templateData);

  try {
    // 使用模板管理器创建模板
    const result = templateManager.createTemplateFromNode(
      templateData.node,
      templateData.name,
      templateData.description,
      templateData.category
    );

    if (result.success) {
      console.log('✅ 模板创建成功:', result.data);

      // 刷新模板列表
      refreshTemplates();

      // 选中新创建的模板
      if (result.data) {
        selectedTemplate.value = result.data;
      }

      // 显示成功消息
      showSuccessMessage(`模板 "${templateData.name}" 创建成功！`);

      // 记录创建统计
      console.log(`📊 模板统计更新: 总计 ${templateManager.getUsageStats().totalTemplates} 个模板`);
    } else {
      console.error('❌ 模板创建失败:', result.errors);
      showErrorMessage(`模板创建失败: ${result.errors?.join(', ')}`);
    }
  } catch (error) {
    console.error('模板创建过程中发生错误:', error);
    showErrorMessage('模板创建过程中发生错误，请重试');
  }
};

// 显示成功消息
const showSuccessMessage = (message: string) => {
  // 简单的成功提示，可以后续替换为更好的UI组件
  alert(`✅ ${message}`);
};

// 显示错误消息
const showErrorMessage = (message: string) => {
  // 简单的错误提示，可以后续替换为更好的UI组件
  alert(`❌ ${message}`);
};

const startEditing = () => {
  if (!selectedTemplate.value) return;
  
  editingTemplate.value = JSON.parse(JSON.stringify(selectedTemplate.value));
  editingTemplateDataText.value = JSON.stringify(selectedTemplate.value.data, null, 2);
  editingTagsText.value = selectedTemplate.value.metadata.tags.join(', ');
  isEditing.value = true;
};

const saveTemplate = () => {
  try {
    // 解析模板数据
    editingTemplate.value.data = JSON.parse(editingTemplateDataText.value);
    
    // 解析标签
    editingTemplate.value.metadata!.tags = editingTagsText.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    let result;
    if (selectedTemplate.value) {
      // 更新现有模板
      result = templateManager.updateTemplate(selectedTemplate.value.id, editingTemplate.value);
    } else {
      // 创建新模板
      result = templateManager.createTemplate(editingTemplate.value);
    }

    if (result.success) {
      selectedTemplate.value = result.template!;
      isEditing.value = false;
      validationErrors.value = [];
    } else {
      validationErrors.value = result.errors || [];
    }
  } catch (error) {
    validationErrors.value = ['模板数据格式错误'];
  }
};

const cancelEditing = () => {
  isEditing.value = false;
  validationErrors.value = [];
};

const validateTemplateData = () => {
  try {
    JSON.parse(editingTemplateDataText.value);
    validationErrors.value = [];
  } catch (error) {
    validationErrors.value = ['JSON格式错误'];
  }
};

const copyReference = () => {
  if (selectedTemplate.value) {
    const reference = `{ "$ref": "${selectedTemplate.value.path}" }`;
    navigator.clipboard.writeText(reference);
    console.log('引用已复制到剪贴板');
  }
};

const getTypeDisplayName = (type: TemplateType): string => {
  const names: Record<TemplateType, string> = {
    [TemplateType.MESH]: '网格',
    [TemplateType.STYLE]: '样式',
    [TemplateType.CAMERA]: '相机',
    [TemplateType.ACTION]: '动作',
    [TemplateType.LABEL]: '标签',
    [TemplateType.POSITION]: '位置',
    [TemplateType.INTERACTION]: '交互',
    [TemplateType.ENVIRONMENT]: '环境',
    [TemplateType.CALLBACK]: '回调',
  };
  return names[type] || type;
};

const getTemplateIcon = (type: TemplateType): string => {
  const icons: Record<TemplateType, string> = {
    [TemplateType.MESH]: '🏗️',
    [TemplateType.STYLE]: '🎨',
    [TemplateType.CAMERA]: '📷',
    [TemplateType.ACTION]: '⚡',
    [TemplateType.LABEL]: '🏷️',
    [TemplateType.POSITION]: '📍',
    [TemplateType.INTERACTION]: '🎭',
    [TemplateType.ENVIRONMENT]: '🌍',
    [TemplateType.CALLBACK]: '🔄',
  };
  return icons[type] || '📄';
};

const formatTemplateData = (data: any): string => {
  return JSON.stringify(data, null, 2);
};

// 生命周期
onMounted(() => {
  // 确保模板管理器已初始化
  console.log('TemplatePanel mounted, 当前模板数量:', allTemplates.value.length);
  console.log('模板管理器实例:', templateManager);
  console.log('所有模板:', allTemplates.value);
  console.log('分类列表:', filteredCategories.value);

  // 默认展开第一个分类
  if (filteredCategories.value.length > 0) {
    expandedCategories.value.push(filteredCategories.value[0]);
    console.log('展开分类:', filteredCategories.value[0]);
  } else {
    console.log('没有分类，等待模板加载...');
    // 如果没有分类，等待一下再试
    setTimeout(() => {
      console.log('延迟检查，当前模板数量:', allTemplates.value.length);
      console.log('延迟检查，分类列表:', filteredCategories.value);
      if (filteredCategories.value.length > 0) {
        expandedCategories.value.push(filteredCategories.value[0]);
        console.log('延迟展开分类:', filteredCategories.value[0]);
      }
    }, 2000);
  }
});

// 监听器
watch(filteredCategories, (newCategories) => {
  // 确保至少有一个分类是展开的
  if (newCategories.length > 0 && expandedCategories.value.length === 0) {
    expandedCategories.value.push(newCategories[0]);
  }
});
</script>

<style scoped>
.template-panel {
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 8px;
  overflow: hidden;
  color: #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 10px; /* 为拖拽手柄留出空间 */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #1a202c;
  border-bottom: 1px solid #4a5568;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #f7fafc;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.toggle-btn, .refresh-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #4a5568;
  border-radius: 6px;
  background: #2d3748;
  color: #e2e8f0;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.toggle-btn:hover, .refresh-btn:hover {
  background: #4a5568;
  border-color: #718096;
  transform: translateY(-1px);
}

.panel-content {
  padding: 1.5rem;
  background: #2d3748;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #4a5568;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  background: #1a202c;
  color: #e2e8f0;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #63b3ed;
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.search-input::placeholder {
  color: #a0aec0;
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #4a5568;
  border-radius: 6px;
  background: #1a202c;
  color: #e2e8f0;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #2d3748;
  border-color: #718096;
}

.filter-btn.active {
  background: #3182ce;
  color: white;
  border-color: #3182ce;
  box-shadow: 0 2px 4px rgba(49, 130, 206, 0.3);
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  min-height: 300px;
  max-height: calc(100% - 120px); /* 减去头部和搜索区域的高度 */
  overflow: hidden;
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .panel-content {
    padding: 1rem;
  }

  .filter-buttons {
    gap: 0.25rem;
  }

  .filter-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.7rem;
  }
}

.template-library {
  border: 1px solid #4a5568;
  border-radius: 8px;
  padding: 1rem;
  background: #1a202c;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 100%;
}

.template-library h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: #f7fafc;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.template-library h4::before {
  content: "📚";
  font-size: 1.2rem;
}

.empty-library {
  text-align: center;
  padding: 3rem 1rem;
  color: #a0aec0;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.empty-library p {
  margin: 0.75rem 0;
  font-size: 0.95rem;
}

.empty-hint {
  font-size: 0.875rem;
  color: #718096;
  margin-bottom: 2rem !important;
}

.empty-library .create-btn,
.empty-library .refresh-btn {
  margin: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid #38a169;
  border-radius: 6px;
  background: #38a169;
  color: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  font-weight: 500;
}

.empty-library .refresh-btn {
  background: #4a5568;
  border-color: #4a5568;
}

.empty-library .create-btn:hover {
  background: #2f855a;
  border-color: #2f855a;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(56, 161, 105, 0.3);
}

.empty-library .refresh-btn:hover {
  background: #2d3748;
  border-color: #2d3748;
  transform: translateY(-1px);
}

.category-section {
  margin-bottom: 1rem;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  background: #2d3748;
  border: 1px solid #4a5568;
  margin-bottom: 0.5rem;
}

.category-header:hover {
  background: #3a4a63;
  border-color: #63b3ed;
}

.category-header.expanded {
  background: #3a4a63;
  border-color: #63b3ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-icon {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.category-name {
  flex: 1;
  font-weight: 600;
  color: #f7fafc;
}

.template-count {
  font-size: 0.875rem;
  color: #a0aec0;
  background: #1a202c;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.category-templates {
  margin-left: 1.5rem;
  border-left: 2px solid #4a5568;
  padding-left: 0.75rem;
  margin-bottom: 1.5rem;
}

.template-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #1a202c;
  border: 1px solid #2d3748;
}

.template-item:hover {
  background: #2d3748;
  border-color: #4a5568;
  transform: translateX(2px);
}

.template-item.selected {
  background: #2c5282;
  border: 1px solid #3182ce;
  box-shadow: 0 2px 4px rgba(49, 130, 206, 0.2);
}

.template-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  font-weight: 600;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #e2e8f0;
}

.template-description {
  font-size: 0.75rem;
  color: #a0aec0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 0.25rem;
}

.template-actions {
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.template-item:hover .template-actions {
  opacity: 1;
}

.action-btn {
  padding: 0.375rem;
  border: none;
  background: #2d3748;
  cursor: pointer;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #e2e8f0;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #4a5568;
  transform: translateY(-1px);
}

.template-editor-section {
  border: 1px solid #4a5568;
  border-radius: 8px;
  padding: 1.25rem;
  background: #1a202c;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 100%;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #a0aec0;
  padding: 3rem 1rem;
}

.no-selection::before {
  content: "👈";
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.create-btn {
  margin-top: 1.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid #38a169;
  border-radius: 6px;
  background: #38a169;
  color: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(56, 161, 105, 0.2);
}

.create-btn:hover {
  background: #2f855a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(56, 161, 105, 0.3);
}

.create-btn.secondary {
  background: #3182ce;
  border-color: #3182ce;
}

.create-btn.secondary:hover {
  background: #2b6cb0;
  border-color: #2b6cb0;
  box-shadow: 0 4px 8px rgba(49, 130, 206, 0.3);
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.template-details {
  height: 100%;
}

.preview-header, .editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #4a5568;
}

.preview-header h4, .editor-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #f7fafc;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-header h4::before {
  content: "👁️";
}

.editor-header h4::before {
  content: "✏️";
}

.preview-actions, .editor-actions {
  display: flex;
  gap: 0.75rem;
}

.edit-btn, .apply-btn, .save-btn, .cancel-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #4a5568;
  border-radius: 6px;
  background: #2d3748;
  color: #e2e8f0;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.edit-btn:hover, .cancel-btn:hover {
  background: #4a5568;
  transform: translateY(-1px);
}

.apply-btn, .save-btn {
  background: #3182ce;
  color: white;
  border-color: #3182ce;
  font-weight: 500;
}

.apply-btn:hover, .save-btn:hover {
  background: #2b6cb0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(49, 130, 206, 0.3);
}

.template-meta {
  margin-bottom: 1.5rem;
}

.meta-item {
  display: flex;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  align-items: center;
}

.meta-item label {
  font-weight: 600;
  width: 100px;
  color: #a0aec0;
  flex-shrink: 0;
}

.template-path {
  font-family: monospace;
  background: #2d3748;
  color: #e2e8f0;
  padding: 0.375rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  border: 1px solid #4a5568;
  flex: 1;
}

.template-data-preview {
  margin-bottom: 1.5rem;
}

.template-data-preview label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #f7fafc;
  font-size: 0.95rem;
}

.data-preview {
  background: #1a202c;
  border: 1px solid #4a5568;
  border-radius: 6px;
  padding: 1rem;
  font-size: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  color: #e2e8f0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  line-height: 1.4;
}

.template-tags {
  margin-bottom: 1.5rem;
}

.template-tags label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #f7fafc;
  font-size: 0.95rem;
}

.tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: #2d3748;
  padding: 0.375rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  color: #e2e8f0;
  border: 1px solid #4a5568;
  transition: all 0.2s ease;
}

.tag:hover {
  background: #3a4a63;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reference-example {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 1rem;
  background: #1a202c;
  border: 1px solid #4a5568;
  border-radius: 6px;
}

.reference-example label {
  font-weight: 600;
  color: #a0aec0;
}

.reference-code {
  flex: 1;
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 4px;
  padding: 0.75rem;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  color: #63b3ed;
  font-size: 0.875rem;
  font-size: 0.75rem;
}

.copy-btn {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 0.75rem;
}

.copy-btn:hover {
  background: #f8f9fa;
}

.editor-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.875rem;
}

.data-editor {
  font-family: monospace;
  font-size: 0.75rem;
}

.validation-errors {
  margin-top: 0.5rem;
}

.error {
  color: #dc3545;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.stats-section {
  display: flex;
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #4a5568;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  background: #1a202c;
  border-radius: 6px;
  border: 1px solid #4a5568;
}

.stats-label {
  color: #a0aec0;
  font-weight: 500;
}

.stats-value {
  font-weight: 600;
  color: #63b3ed;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.template-library::-webkit-scrollbar,
.template-editor-section::-webkit-scrollbar,
.data-preview::-webkit-scrollbar,
.category-templates::-webkit-scrollbar {
  width: 8px;
}

.panel-content::-webkit-scrollbar-track,
.template-library::-webkit-scrollbar-track,
.template-editor-section::-webkit-scrollbar-track,
.data-preview::-webkit-scrollbar-track,
.category-templates::-webkit-scrollbar-track {
  background: #1a202c;
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb,
.template-library::-webkit-scrollbar-thumb,
.template-editor-section::-webkit-scrollbar-thumb,
.data-preview::-webkit-scrollbar-thumb,
.category-templates::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
  border: 1px solid #2d3748;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.template-library::-webkit-scrollbar-thumb:hover,
.template-editor-section::-webkit-scrollbar-thumb:hover,
.data-preview::-webkit-scrollbar-thumb:hover,
.category-templates::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-item,
.category-section {
  animation: fadeIn 0.3s ease-out;
}

.template-item:nth-child(even) {
  animation-delay: 0.1s;
}

.template-item:nth-child(odd) {
  animation-delay: 0.05s;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .panel-content {
    padding: 1rem;
  }
}

@media (max-width: 640px) {
  .panel-header {
    padding: 0.75rem;
  }

  .panel-header h3 {
    font-size: 1rem;
  }

  .header-actions {
    gap: 0.25rem;
  }

  .toggle-btn, .refresh-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }

  .filter-buttons {
    gap: 0.25rem;
  }

  .filter-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.7rem;
  }
}
</style>
