# 模板系统架构设计方案

## 🎯 设计目标

将模板系统与可视化节点编辑器深度集成，提供直观的模板使用和管理界面，同时保持与现有系统的兼容性。

## 📊 现状分析

### 当前模板类型
基于config.js分析，发现以下模板类型：

1. **meshes** - 网格集合模板
   - `buildingLevels`: 建筑层级网格
   - `gisDoors`: GIS门网格集合

2. **styles** - 样式模板
   - `orangeGradient`: 橙色渐变样式
   - `blueGradient`: 蓝色渐变样式
   - `transparent`: 透明样式

3. **cameras** - 相机配置模板
   - `building`: 建筑视角
   - `1-3`, `3-1`, `3-2`: 特定场景视角

4. **actions** - 动作模板
   - `hoverHighlight`: 悬停高亮
   - `clickRedHighlight`: 点击红色高亮
   - `doubleClickFocus`: 双击聚焦

5. **labels** - 标签模板
   - `floorLabel`: 楼层标签模板
   - `roomLabel`: 房间标签模板
   - `deviceLabel`: 设备标签模板

6. **positions** - 位置模板
   - 各种预定义的3D位置坐标

## 🏗️ 架构设计

### 1. 核心组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Template System                          │
├─────────────────────────────────────────────────────────────┤
│  TemplateManager (核心管理器)                               │
│  ├── TemplateRegistry (模板注册表)                          │
│  ├── TemplateResolver (模板解析器)                          │
│  ├── TemplateValidator (模板验证器)                         │
│  └── TemplateCache (模板缓存)                               │
├─────────────────────────────────────────────────────────────┤
│  UI Components (用户界面组件)                               │
│  ├── TemplatePanel (模板面板)                               │
│  ├── TemplateSelector (模板选择器)                          │
│  ├── TemplateEditor (模板编辑器)                            │
│  ├── TemplatePreview (模板预览器)                           │
│  └── TemplateLibrary (模板库)                               │
├─────────────────────────────────────────────────────────────┤
│  Integration Layer (集成层)                                │
│  ├── NodeTemplateIntegration (节点模板集成)                │
│  ├── ConfigParserIntegration (配置解析器集成)              │
│  └── GraphBuilderIntegration (图构建器集成)                │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据模型设计

#### 模板数据结构
```typescript
interface Template {
  id: string;
  name: string;
  type: TemplateType;
  category: string;
  description: string;
  data: any;
  metadata: TemplateMetadata;
  version: string;
  createdAt: Date;
  updatedAt: Date;
}

enum TemplateType {
  MESH = 'mesh',
  STYLE = 'style', 
  CAMERA = 'camera',
  ACTION = 'action',
  LABEL = 'label',
  POSITION = 'position',
  INTERACTION = 'interaction'
}

interface TemplateMetadata {
  tags: string[];
  author: string;
  usage: number;
  dependencies: string[];
  preview?: string;
}
```

#### 模板引用数据结构
```typescript
interface TemplateReference {
  $ref: string;
  $extend?: Record<string, any>;
  $override?: Record<string, any>;
}
```

## 🎨 UI/UX 设计方案

### 1. 整体布局设计

```
┌─────────────────────────────────────────────────────────────┐
│                    ddd-flow 主界面                          │
├─────────────┬─────────────────────────┬─────────────────────┤
│             │                         │                     │
│ NodeLibrary │     LogicEditor         │  PropertiesPanel   │
│             │                         │                     │
│ [新增]      │                         │  [增强]             │
│ Template    │                         │  Template           │
│ Section     │                         │  Integration        │
│             │                         │                     │
├─────────────┴─────────────────────────┴─────────────────────┤
│                 [新增] TemplatePanel                        │
│  ┌─────────────┬─────────────────┬─────────────────────────┐ │
│  │ Template    │ Template        │ Template                │ │
│  │ Library     │ Editor          │ Preview                 │ │
│  │             │                 │                         │ │
│  └─────────────┴─────────────────┴─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. TemplatePanel 设计

#### 2.1 模板库 (TemplateLibrary)
```
┌─────────────────────────────────────┐
│ 模板库                              │
├─────────────────────────────────────┤
│ 🔍 [搜索模板...]                    │
├─────────────────────────────────────┤
│ 📁 网格模板 (Meshes)                │
│   📄 buildingLevels                 │
│   📄 gisDoors                       │
│                                     │
│ 🎨 样式模板 (Styles)                │
│   📄 orangeGradient                 │
│   📄 blueGradient                   │
│   📄 transparent                    │
│                                     │
│ 📷 相机模板 (Cameras)               │
│   📄 building                       │
│   📄 scene-1-3                      │
│                                     │
│ ⚡ 动作模板 (Actions)               │
│   📄 hoverHighlight                 │
│   📄 doubleClickFocus               │
│                                     │
│ 🏷️ 标签模板 (Labels)                │
│   📄 floorLabel                     │
│   📄 deviceLabel                    │
└─────────────────────────────────────┘
```

#### 2.2 模板编辑器 (TemplateEditor)
```
┌─────────────────────────────────────┐
│ 模板编辑器                          │
├─────────────────────────────────────┤
│ 名称: [buildingLevels]              │
│ 类型: [网格模板 ▼]                  │
│ 分类: [建筑结构 ▼]                  │
│ 描述: [建筑层级网格集合]            │
├─────────────────────────────────────┤
│ 模板数据:                           │
│ ┌─────────────────────────────────┐ │
│ │ [                               │ │
│ │   "电气楼五层",                 │ │
│ │   "NA_LSZSC_QITA04",            │ │
│ │   "NA_LSZSC_QITA03",            │ │
│ │   "二层外墙",                   │ │
│ │   "NA_LSZ_YC_MOD10"             │ │
│ │ ]                               │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 标签: [建筑] [层级] [网格]          │
│ 依赖: 无                            │
├─────────────────────────────────────┤
│ [保存] [重置] [删除] [导出]         │
└─────────────────────────────────────┘
```

#### 2.3 模板预览器 (TemplatePreview)
```
┌─────────────────────────────────────┐
│ 模板预览                            │
├─────────────────────────────────────┤
│ buildingLevels                      │
│ 网格模板 | 使用次数: 12             │
├─────────────────────────────────────┤
│ 📊 数据预览:                        │
│ • 包含 5 个网格对象                 │
│ • 总大小: 156 字符                  │
│ • 最后更新: 2024-01-15              │
├─────────────────────────────────────┤
│ 🔗 使用位置:                        │
│ • scene-1 (3个引用)                 │
│ • scene-2 (2个引用)                 │
│ • scene-3 (7个引用)                 │
├─────────────────────────────────────┤
│ 📝 引用示例:                        │
│ {                                   │
│   "$ref": "templates.1.meshes.     │
│            buildingLevels"          │
│ }                                   │
├─────────────────────────────────────┤
│ [应用到节点] [复制引用] [编辑]      │
└─────────────────────────────────────┘
```

### 3. 节点集成设计

#### 3.1 增强的属性面板
```
┌─────────────────────────────────────┐
│ 事件节点属性                        │
├─────────────────────────────────────┤
│ 目标网格 (meshNames):               │
│ ○ 直接指定  ● 模板引用              │
│                                     │
│ 模板选择:                           │
│ [buildingLevels ▼] [🔍] [📝]        │
│                                     │
│ 扩展参数:                           │
│ ┌─────────────────────────────────┐ │
│ │ {                               │ │
│ │   "exclude": ["电气楼五层"]     │ │
│ │ }                               │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 预览: 4个网格 (排除1个)             │
│ [预览网格] [应用模板]               │
└─────────────────────────────────────┘
```

#### 3.2 NodeLibrary 模板集成
```
┌─────────────────────────────────────┐
│ 节点库                              │
├─────────────────────────────────────┤
│ 📁 基础节点                         │
│   🎯 事件节点                       │
│   ⚡ 动作节点                       │
│                                     │
│ 📁 模板节点 [新增]                  │
│   🏗️ 网格模板节点                   │
│   🎨 样式模板节点                   │
│   📷 相机模板节点                   │
│   🏷️ 标签模板节点                   │
│                                     │
│ 📁 复合模板 [新增]                  │
│   🎭 交互模板                       │
│   🎬 动画序列模板                   │
└─────────────────────────────────────┘
```

## 🔄 数据流设计

### 1. 模板加载流程
```
配置文件 → TemplateManager.loadTemplates() 
         → TemplateRegistry.register() 
         → TemplateCache.cache()
         → UI组件更新
```

### 2. 模板应用流程
```
用户选择模板 → TemplateSelector.selectTemplate()
            → TemplateResolver.resolve()
            → 节点数据更新
            → GraphBuilder.updateNode()
            → 视图刷新
```

### 3. 模板编辑流程
```
用户编辑模板 → TemplateEditor.updateTemplate()
            → TemplateValidator.validate()
            → TemplateManager.saveTemplate()
            → 依赖节点更新通知
            → 相关视图刷新
```

## 🚀 实现优先级和开发计划

### Phase 1: 核心基础设施 (1-2周)
- [ ] **P0** TemplateManager 核心管理器
- [ ] **P0** TemplateRegistry 模板注册表
- [ ] **P0** TemplateResolver 模板解析器
- [ ] **P1** TemplateValidator 模板验证器
- [ ] **P1** TemplateCache 模板缓存

### Phase 2: 基础UI组件 (1-2周)
- [ ] **P0** TemplatePanel 基础面板
- [ ] **P0** TemplateLibrary 模板库组件
- [ ] **P1** TemplateSelector 模板选择器
- [ ] **P1** TemplatePreview 模板预览器
- [ ] **P2** TemplateEditor 模板编辑器

### Phase 3: 节点集成 (1周)
- [ ] **P0** PropertiesPanel 模板集成
- [ ] **P0** NodeLibrary 模板节点支持
- [ ] **P1** 模板节点类型实现
- [ ] **P1** 节点模板应用逻辑

### Phase 4: 高级功能 (1-2周)
- [ ] **P1** 模板依赖管理
- [ ] **P1** 模板版本控制
- [ ] **P2** 模板导入导出
- [ ] **P2** 模板搜索和过滤
- [ ] **P2** 模板使用统计

### Phase 5: 优化和完善 (1周)
- [ ] **P1** 性能优化
- [ ] **P1** 错误处理完善
- [ ] **P2** 用户体验优化
- [ ] **P2** 文档和测试完善

## 📋 技术实现要点

### 1. 向后兼容性保证
- 保持现有$ref语法完全兼容
- ConfigParser自动识别和处理模板引用
- 渐进式迁移策略

### 2. 性能考虑
- 模板懒加载和缓存机制
- 虚拟滚动支持大量模板
- 模板预览的异步渲染

### 3. 类型安全
- 完整的TypeScript类型定义
- 模板数据的运行时验证
- 编译时的引用检查

这个架构设计将模板系统与可视化编辑器深度集成，提供了直观的用户体验，同时保持了系统的可扩展性和向后兼容性。
