<template>
  <div class="system-monitor-panel">
    <!-- 面板遮罩 -->
    <div class="panel-overlay" @click="$emit('close')"></div>
    
    <!-- 面板内容 -->
    <div class="panel-content">
      <!-- 头部 -->
      <div class="panel-header">
        <h3>📊 系统监控面板</h3>
        <div class="header-actions">
          <button @click="refreshData" class="refresh-btn" :disabled="isRefreshing">
            {{ isRefreshing ? '刷新中...' : '🔄 刷新' }}
          </button>
          <button @click="$emit('close')" class="close-btn">✕</button>
        </div>
      </div>

      <!-- 监控内容 -->
      <div class="monitor-content">
        <!-- 系统概览 -->
        <div class="overview-section">
          <h4>系统概览</h4>
          <div class="overview-grid">
            <div class="overview-card">
              <div class="card-icon">📦</div>
              <div class="card-info">
                <div class="card-value">{{ systemStats.templateCount }}</div>
                <div class="card-label">模板总数</div>
              </div>
            </div>
            
            <div class="overview-card">
              <div class="card-icon">🔗</div>
              <div class="card-info">
                <div class="card-value">{{ systemStats.instanceCount }}</div>
                <div class="card-label">实例总数</div>
              </div>
            </div>
            
            <div class="overview-card">
              <div class="card-icon">⚡</div>
              <div class="card-info">
                <div class="card-value">{{ formatTime(systemStats.averageOperationTime) }}</div>
                <div class="card-label">平均响应时间</div>
              </div>
            </div>
            
            <div class="overview-card">
              <div class="card-icon">💾</div>
              <div class="card-info">
                <div class="card-value">{{ formatMemory(systemStats.memoryUsage) }}</div>
                <div class="card-label">内存使用</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 性能指标 -->
        <div class="performance-section">
          <h4>性能指标</h4>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">操作时间</span>
                <span class="metric-trend" :class="getPerformanceTrend('time')">
                  {{ getPerformanceTrend('time') === 'up' ? '📈' : '📉' }}
                </span>
              </div>
              <div class="metric-values">
                <div class="metric-item">
                  <span class="metric-label">模板创建</span>
                  <span class="metric-value">{{ formatTime(performanceMetrics.templateCreationTime) }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">实例化</span>
                  <span class="metric-value">{{ formatTime(performanceMetrics.templateInstantiationTime) }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">参数同步</span>
                  <span class="metric-value">{{ formatTime(performanceMetrics.parameterSyncTime) }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">批量操作</span>
                  <span class="metric-value">{{ formatTime(performanceMetrics.batchOperationTime) }}</span>
                </div>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">缓存效率</span>
                <span class="metric-trend" :class="getCacheTrend()">
                  {{ getCacheTrend() === 'up' ? '📈' : '📉' }}
                </span>
              </div>
              <div class="metric-values">
                <div class="metric-item">
                  <span class="metric-label">命中率</span>
                  <span class="metric-value">{{ formatPercentage(performanceMetrics.cacheMetrics.hitRate) }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">缓存大小</span>
                  <span class="metric-value">{{ performanceMetrics.cacheMetrics.size }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">驱逐次数</span>
                  <span class="metric-value">{{ performanceMetrics.cacheMetrics.evictionCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 错误监控 -->
        <div class="error-section">
          <h4>错误监控</h4>
          <div class="error-overview">
            <div class="error-stats">
              <div class="error-stat">
                <span class="error-count">{{ errorStats.total }}</span>
                <span class="error-label">总错误数</span>
              </div>
              <div class="error-stat">
                <span class="error-count recent">{{ errorStats.recentErrors }}</span>
                <span class="error-label">最近1小时</span>
              </div>
              <div class="error-stat">
                <span class="error-count recovered">{{ errorStats.recoveredErrors }}</span>
                <span class="error-label">已恢复</span>
              </div>
            </div>
          </div>
          
          <div v-if="recentErrors.length > 0" class="recent-errors">
            <h5>最近错误</h5>
            <div class="error-list">
              <div
                v-for="error in recentErrors.slice(0, 5)"
                :key="error.id"
                class="error-item"
                :class="{ [`severity-${error.severity}`]: true }"
              >
                <div class="error-info">
                  <div class="error-type">{{ getErrorTypeText(error.type) }}</div>
                  <div class="error-message">{{ error.message }}</div>
                  <div class="error-time">{{ formatRelativeTime(error.timestamp) }}</div>
                </div>
                <div class="error-actions">
                  <button
                    v-if="error.recoverable"
                    @click="retryError(error)"
                    class="retry-btn"
                    :disabled="isRetrying"
                  >
                    重试
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统健康状态 -->
        <div class="health-section">
          <h4>系统健康状态</h4>
          <div class="health-indicators">
            <div class="health-indicator" :class="getHealthStatus('overall')">
              <div class="indicator-icon">{{ getHealthIcon('overall') }}</div>
              <div class="indicator-info">
                <div class="indicator-title">整体状态</div>
                <div class="indicator-status">{{ getHealthStatusText('overall') }}</div>
              </div>
            </div>
            
            <div class="health-indicator" :class="getHealthStatus('performance')">
              <div class="indicator-icon">{{ getHealthIcon('performance') }}</div>
              <div class="indicator-info">
                <div class="indicator-title">性能状态</div>
                <div class="indicator-status">{{ getHealthStatusText('performance') }}</div>
              </div>
            </div>
            
            <div class="health-indicator" :class="getHealthStatus('errors')">
              <div class="indicator-icon">{{ getHealthIcon('errors') }}</div>
              <div class="indicator-info">
                <div class="indicator-title">错误状态</div>
                <div class="indicator-status">{{ getHealthStatusText('errors') }}</div>
              </div>
            </div>
            
            <div class="health-indicator" :class="getHealthStatus('memory')">
              <div class="indicator-icon">{{ getHealthIcon('memory') }}</div>
              <div class="indicator-info">
                <div class="indicator-title">内存状态</div>
                <div class="indicator-status">{{ getHealthStatusText('memory') }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作建议 -->
        <div v-if="recommendations.length > 0" class="recommendations-section">
          <h4>优化建议</h4>
          <div class="recommendations-list">
            <div
              v-for="recommendation in recommendations"
              :key="recommendation.id"
              class="recommendation-item"
              :class="{ [`priority-${recommendation.priority}`]: true }"
            >
              <div class="recommendation-icon">{{ recommendation.icon }}</div>
              <div class="recommendation-content">
                <div class="recommendation-title">{{ recommendation.title }}</div>
                <div class="recommendation-description">{{ recommendation.description }}</div>
              </div>
              <div class="recommendation-actions">
                <button
                  v-if="recommendation.action"
                  @click="executeRecommendation(recommendation)"
                  class="execute-btn"
                  :disabled="isExecuting"
                >
                  执行
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="panel-footer">
        <div class="footer-actions">
          <button @click="optimizeSystem" class="optimize-btn" :disabled="isOptimizing">
            {{ isOptimizing ? '优化中...' : '⚡ 系统优化' }}
          </button>
          <button @click="clearErrorHistory" class="clear-btn">
            🧹 清除错误历史
          </button>
          <button @click="exportReport" class="export-btn">
            📊 导出报告
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { TemplateManager } from '../services/TemplateManager';
import { performanceOptimizer } from '../services/PerformanceOptimizer';
import { errorHandler } from '../services/ErrorHandler';

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 状态
const isRefreshing = ref(false);
const isRetrying = ref(false);
const isOptimizing = ref(false);
const isExecuting = ref(false);

// 数据
const systemStats = reactive({
  templateCount: 0,
  instanceCount: 0,
  averageOperationTime: 0,
  memoryUsage: 0
});

const performanceMetrics = reactive({
  templateCreationTime: 0,
  templateInstantiationTime: 0,
  parameterSyncTime: 0,
  batchOperationTime: 0,
  cacheMetrics: {
    hitRate: 0,
    size: 0,
    evictionCount: 0
  }
});

const errorStats = reactive({
  total: 0,
  recentErrors: 0,
  recoveredErrors: 0
});

const recentErrors = ref<any[]>([]);
const recommendations = ref<any[]>([]);

// 服务
const templateManager = TemplateManager.getInstance();

// 定时器
let refreshInterval: number | null = null;

// 方法
const refreshData = async () => {
  if (isRefreshing.value) return;
  
  isRefreshing.value = true;
  try {
    // 获取系统统计
    const stats = templateManager.getUsageStats();
    systemStats.templateCount = stats.totalTemplates;
    systemStats.instanceCount = stats.totalInstances;
    
    // 获取性能指标
    const metrics = performanceOptimizer.getMetrics();
    Object.assign(performanceMetrics, metrics);
    
    systemStats.averageOperationTime = (
      metrics.templateCreationTime +
      metrics.templateInstantiationTime +
      metrics.parameterSyncTime +
      metrics.batchOperationTime
    ) / 4;
    
    systemStats.memoryUsage = metrics.memoryUsage.total;
    
    // 获取错误统计
    const errors = errorHandler.getErrorStats();
    Object.assign(errorStats, errors);
    
    // 获取最近错误
    recentErrors.value = errorHandler.getErrorHistory(undefined, undefined, 10);
    
    // 生成优化建议
    generateRecommendations();
    
  } catch (error) {
    console.error('刷新监控数据失败:', error);
  } finally {
    isRefreshing.value = false;
  }
};

const generateRecommendations = () => {
  const newRecommendations = [];
  
  // 性能建议
  if (systemStats.averageOperationTime > 1000) {
    newRecommendations.push({
      id: 'performance-slow',
      title: '系统响应较慢',
      description: '平均操作时间超过1秒，建议进行性能优化',
      icon: '⚡',
      priority: 'high',
      action: () => templateManager.optimizePerformance()
    });
  }
  
  // 内存建议
  if (systemStats.memoryUsage > 50 * 1024 * 1024) { // 50MB
    newRecommendations.push({
      id: 'memory-high',
      title: '内存使用较高',
      description: '内存使用超过50MB，建议清理缓存',
      icon: '💾',
      priority: 'medium',
      action: () => performanceOptimizer.optimizeMemory()
    });
  }
  
  // 错误建议
  if (errorStats.recentErrors > 5) {
    newRecommendations.push({
      id: 'errors-frequent',
      title: '错误频率较高',
      description: '最近1小时内发生多个错误，建议检查系统状态',
      icon: '🛡️',
      priority: 'high',
      action: () => errorHandler.clearErrorHistory(new Date(Date.now() - 60 * 60 * 1000))
    });
  }
  
  // 缓存建议
  if (performanceMetrics.cacheMetrics.hitRate < 0.5) {
    newRecommendations.push({
      id: 'cache-low-hit',
      title: '缓存命中率较低',
      description: '缓存命中率低于50%，可能需要调整缓存策略',
      icon: '🎯',
      priority: 'medium'
    });
  }
  
  recommendations.value = newRecommendations;
};

const formatTime = (ms: number): string => {
  if (ms < 1000) return `${ms.toFixed(0)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
};

const formatMemory = (bytes: number): string => {
  if (bytes < 1024) return `${bytes}B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
  return `${(bytes / 1024 / 1024).toFixed(1)}MB`;
};

const formatPercentage = (value: number): string => {
  return `${(value * 100).toFixed(1)}%`;
};

const formatRelativeTime = (timestamp: Date): string => {
  const now = new Date();
  const diff = now.getTime() - new Date(timestamp).getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}小时前`;
  
  const days = Math.floor(hours / 24);
  return `${days}天前`;
};

const getPerformanceTrend = (type: string): 'up' | 'down' => {
  // 简化的趋势判断逻辑
  return Math.random() > 0.5 ? 'up' : 'down';
};

const getCacheTrend = (): 'up' | 'down' => {
  return performanceMetrics.cacheMetrics.hitRate > 0.7 ? 'up' : 'down';
};

const getHealthStatus = (type: string): 'healthy' | 'warning' | 'critical' => {
  switch (type) {
    case 'overall':
      if (errorStats.recentErrors > 10) return 'critical';
      if (errorStats.recentErrors > 5 || systemStats.averageOperationTime > 2000) return 'warning';
      return 'healthy';
    case 'performance':
      if (systemStats.averageOperationTime > 2000) return 'critical';
      if (systemStats.averageOperationTime > 1000) return 'warning';
      return 'healthy';
    case 'errors':
      if (errorStats.recentErrors > 10) return 'critical';
      if (errorStats.recentErrors > 5) return 'warning';
      return 'healthy';
    case 'memory':
      if (systemStats.memoryUsage > 100 * 1024 * 1024) return 'critical';
      if (systemStats.memoryUsage > 50 * 1024 * 1024) return 'warning';
      return 'healthy';
    default:
      return 'healthy';
  }
};

const getHealthIcon = (type: string): string => {
  const status = getHealthStatus(type);
  switch (status) {
    case 'healthy': return '✅';
    case 'warning': return '⚠️';
    case 'critical': return '❌';
    default: return '❓';
  }
};

const getHealthStatusText = (type: string): string => {
  const status = getHealthStatus(type);
  switch (status) {
    case 'healthy': return '正常';
    case 'warning': return '警告';
    case 'critical': return '严重';
    default: return '未知';
  }
};

const getErrorTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    validation: '验证错误',
    network: '网络错误',
    storage: '存储错误',
    template: '模板错误',
    instance: '实例错误',
    sync: '同步错误',
    performance: '性能问题',
    unknown: '未知错误'
  };
  return typeMap[type] || type;
};

const retryError = async (error: any) => {
  isRetrying.value = true;
  try {
    // 尝试重新执行导致错误的操作
    console.log('重试错误:', error.id);
    await new Promise(resolve => setTimeout(resolve, 1000));
    await refreshData();
  } catch (retryError) {
    console.error('重试失败:', retryError);
  } finally {
    isRetrying.value = false;
  }
};

const executeRecommendation = async (recommendation: any) => {
  if (!recommendation.action) return;
  
  isExecuting.value = true;
  try {
    await recommendation.action();
    console.log(`✅ 执行建议: ${recommendation.title}`);
    await refreshData();
  } catch (error) {
    console.error(`❌ 执行建议失败: ${recommendation.title}`, error);
  } finally {
    isExecuting.value = false;
  }
};

const optimizeSystem = async () => {
  isOptimizing.value = true;
  try {
    // 执行系统优化
    templateManager.optimizePerformance();
    performanceOptimizer.optimizeMemory();
    
    console.log('✅ 系统优化完成');
    await refreshData();
  } catch (error) {
    console.error('❌ 系统优化失败:', error);
  } finally {
    isOptimizing.value = false;
  }
};

const clearErrorHistory = () => {
  errorHandler.clearErrorHistory();
  refreshData();
};

const exportReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    systemStats,
    performanceMetrics,
    errorStats,
    recentErrors: recentErrors.value,
    recommendations: recommendations.value
  };
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `system-report-${new Date().toISOString().split('T')[0]}.json`;
  a.click();
  URL.revokeObjectURL(url);
  
  console.log('📊 系统报告已导出');
};

// 生命周期
onMounted(() => {
  refreshData();
  
  // 设置定时刷新
  refreshInterval = window.setInterval(refreshData, 30000); // 30秒刷新一次
});

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
});
</script>

<style scoped>
.system-monitor-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.panel-content {
  position: relative;
  width: 900px;
  max-width: 95vw;
  max-height: 90vh;
  background: #1a202c;
  border-radius: 12px;
  border: 1px solid #4a5568;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.panel-header h3 {
  margin: 0;
  color: #f7fafc;
  font-size: 1.25rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.refresh-btn, .close-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #4a5568;
  border-radius: 6px;
  background: transparent;
  color: #e2e8f0;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled), .close-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 监控内容样式 */
.monitor-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.monitor-content h4 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
}

.monitor-content > div {
  margin-bottom: 2rem;
}

/* 概览卡片样式 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.card-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.card-value {
  color: #f7fafc;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.card-label {
  color: #a0aec0;
  font-size: 0.875rem;
}

/* 性能指标样式 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.metric-card {
  padding: 1.5rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metric-title {
  color: #f7fafc;
  font-weight: 600;
}

.metric-trend {
  font-size: 1.25rem;
}

.metric-trend.up {
  color: #38a169;
}

.metric-trend.down {
  color: #e53e3e;
}

.metric-values {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  color: #a0aec0;
  font-size: 0.875rem;
}

.metric-value {
  color: #e2e8f0;
  font-weight: 500;
}

/* 错误监控样式 */
.error-overview {
  margin-bottom: 1.5rem;
}

.error-stats {
  display: flex;
  gap: 2rem;
}

.error-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.error-count {
  color: #f7fafc;
  font-size: 1.5rem;
  font-weight: 600;
}

.error-count.recent {
  color: #ed8936;
}

.error-count.recovered {
  color: #38a169;
}

.error-label {
  color: #a0aec0;
  font-size: 0.875rem;
}

.recent-errors h5 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 0.875rem;
  font-weight: 600;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.error-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  border-left: 3px solid #4a5568;
}

.error-item.severity-critical {
  border-left-color: #e53e3e;
}

.error-item.severity-high {
  border-left-color: #f56565;
}

.error-item.severity-medium {
  border-left-color: #ed8936;
}

.error-item.severity-low {
  border-left-color: #ecc94b;
}

.error-type {
  color: #63b3ed;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.error-message {
  color: #e2e8f0;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.error-time {
  color: #a0aec0;
  font-size: 0.75rem;
}

.retry-btn {
  padding: 0.375rem 0.75rem;
  border: 1px solid #4a5568;
  border-radius: 4px;
  background: #3182ce;
  color: white;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.retry-btn:hover:not(:disabled) {
  background: #2b6cb0;
}

.retry-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 健康状态样式 */
.health-indicators {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.health-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
}

.health-indicator.healthy {
  border-left: 3px solid #38a169;
}

.health-indicator.warning {
  border-left: 3px solid #ed8936;
}

.health-indicator.critical {
  border-left: 3px solid #e53e3e;
}

.indicator-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.indicator-title {
  color: #e2e8f0;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.indicator-status {
  color: #a0aec0;
  font-size: 0.875rem;
}

/* 建议样式 */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
}

.recommendation-item.priority-high {
  border-left: 3px solid #e53e3e;
}

.recommendation-item.priority-medium {
  border-left: 3px solid #ed8936;
}

.recommendation-item.priority-low {
  border-left: 3px solid #ecc94b;
}

.recommendation-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  color: #f7fafc;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.recommendation-description {
  color: #a0aec0;
  font-size: 0.875rem;
}

.execute-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #4a5568;
  border-radius: 4px;
  background: #38a169;
  color: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.execute-btn:hover:not(:disabled) {
  background: #2f855a;
}

.execute-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 底部操作样式 */
.panel-footer {
  padding: 1.5rem;
  background: #2d3748;
  border-top: 1px solid #4a5568;
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

.optimize-btn, .clear-btn, .export-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #4a5568;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.optimize-btn {
  background: #38a169;
  color: white;
}

.optimize-btn:hover:not(:disabled) {
  background: #2f855a;
}

.clear-btn {
  background: #e53e3e;
  color: white;
}

.clear-btn:hover {
  background: #c53030;
}

.export-btn {
  background: #3182ce;
  color: white;
}

.export-btn:hover {
  background: #2b6cb0;
}

.optimize-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-content {
    width: 95vw;
    max-height: 95vh;
  }
  
  .overview-grid, .metrics-grid, .health-indicators {
    grid-template-columns: 1fr;
  }
  
  .error-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .footer-actions {
    flex-direction: column;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* 滚动条样式 */
.monitor-content::-webkit-scrollbar {
  width: 6px;
}

.monitor-content::-webkit-scrollbar-track {
  background: #2d3748;
}

.monitor-content::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.monitor-content::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}
</style>
