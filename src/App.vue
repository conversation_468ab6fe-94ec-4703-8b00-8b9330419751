<script setup lang="ts">
import { ref, onMounted, watch, provide } from "vue";
import type { Cell } from "@antv/x6";
import LogicEditor from "./components/LogicEditor.vue";
import NodeLibrary from "./components/NodeLibrary.vue";
import PropertiesPanel from "./components/PropertiesPanel.vue";
import SceneManager from "./components/scene/SceneManager.vue";
import TemplatePanel from "./components/TemplatePanel.vue";
import { sceneService } from "./services/SceneService";
import { compileMultipleGraphs } from "./compiler/graph-compiler";
import { TemplateManager, TemplateType } from "./services/TemplateManager";

// 当前选中的节点
const selectedNode = ref<Cell.Properties | null>(null);

// 当前场景ID
const currentSceneId = ref<string | null>(null);

// 配置代码
const generatedConfig = ref("");

// 面板是否折叠
const isLibraryCollapsed = ref(false);
const isPropertiesCollapsed = ref(false);
const isTemplatePanelVisible = ref(true); // 默认显示模板面板

// 逻辑编辑器实例
const logicEditor = ref<InstanceType<typeof LogicEditor> | null>(null);
const sceneManagerRef = ref<any>(null);
const templatePanelContainer = ref<HTMLElement | null>(null);

// 拖拽调整相关状态
const isResizing = ref(false);
const startY = ref(0);
const startHeight = ref(0);

// 将场景ID注入到子组件
provide("currentSceneId", currentSceneId);

// 模板管理器实例
const templateManager = TemplateManager.getInstance();

// 初始化加载
onMounted(() => {
  // 设置当前场景ID
  currentSceneId.value = sceneService.getCurrentSceneId();

  // 初始化模板系统
  initializeTemplateSystem();
});

// 初始化模板系统
async function initializeTemplateSystem() {
  try {
    // 尝试从多个来源加载配置
    let config = null;

    // 1. 尝试从window.$config加载
    if (typeof window !== 'undefined' && (window as any).$config) {
      config = (window as any).$config;
      console.log('从window.$config加载模板配置');
    }
    // 2. 尝试从public/config.js加载
    else {
      try {
        const response = await fetch('/config.js');
        if (response.ok) {
          const configText = await response.text();
          // 安全地解析config.js文件
          try {
            // 创建一个临时的window对象来执行配置脚本
            const tempWindow = { $config: null };
            const configScript = configText.replace('window.$config', 'tempWindow.$config');
            // 使用Function构造器而不是eval，更安全
            const configFunction = new Function('tempWindow', configScript + '; return tempWindow.$config;');
            config = configFunction(tempWindow);
            console.log('从/config.js文件加载模板配置');
          } catch (parseError) {
            console.warn('解析config.js失败:', parseError);
          }
        }
      } catch (fetchError) {
        console.warn('无法从/config.js加载配置:', fetchError);
      }
    }

    if (config && config.templates) {
      templateManager.loadFromConfig(config);
      console.log('模板系统初始化完成，加载了', Object.keys(config.templates).length, '个版本的模板');
    } else {
      console.warn('未找到模板配置，模板系统将使用空配置');
      // 创建一些示例模板用于测试
      createSampleTemplates();
    }
  } catch (error) {
    console.error('模板系统初始化失败:', error);
    // 创建示例模板作为后备
    createSampleTemplates();
  }
}

// 创建示例模板用于测试
function createSampleTemplates() {
  console.log('创建示例模板...');

  const sampleTemplates = [
    {
      name: "建筑层级网格",
      type: TemplateType.MESH,
      data: ["电气楼五层", "NA_LSZSC_QITA04", "NA_LSZSC_QITA03", "二层外墙", "NA_LSZ_YC_MOD10"],
      metadata: {
        category: "网格",
        tags: ["building", "levels", "mesh"],
        author: "system",
        usage: 0,
        dependencies: []
      }
    },
    {
      name: "GIS门网格",
      type: TemplateType.MESH,
      data: ["DOOR_NA_LSZ_GIS_MOD105", "DOOR_NA_LSZ_GIS_MOD108", "DOOR_NA_LSZ_GIS_MOD114"],
      metadata: {
        category: "网格",
        tags: ["gis", "doors", "mesh"],
        author: "system",
        usage: 0,
        dependencies: []
      }
    },
    {
      name: "橙色渐变样式",
      type: TemplateType.STYLE,
      data: {
        backgroundColor: "linear-gradient(180deg, rgba(255, 165, 0, 0.9) 0%, rgba(255, 140, 0, 0.8) 50%, rgba(255, 100, 0, 0.9) 100%)",
        borderColor: "rgba(255, 215, 0, 0.8)",
        borderThickness: 2,
        cornerRadius: 8,
        textColor: "#FFFFFF"
      },
      metadata: {
        category: "样式",
        tags: ["orange", "gradient", "style"],
        author: "system",
        usage: 0,
        dependencies: []
      }
    },
    {
      name: "蓝色渐变样式",
      type: TemplateType.STYLE,
      data: {
        backgroundColor: "linear-gradient(180deg, rgba(0, 150, 255, 0.9) 0%, rgba(0, 120, 255, 0.8) 50%, rgba(0, 100, 255, 0.9) 100%)",
        borderColor: "rgba(100, 200, 255, 0.8)",
        borderThickness: 2,
        cornerRadius: 6,
        textColor: "#FFFFFF"
      },
      metadata: {
        category: "样式",
        tags: ["blue", "gradient", "style"],
        author: "system",
        usage: 0,
        dependencies: []
      }
    },
    {
      name: "悬停高亮动作",
      type: TemplateType.ACTION,
      data: {
        type: "hover",
        highlight: {
          color: [0, 1, 1],
          duration: 300,
          intensity: 1.2
        }
      },
      metadata: {
        category: "动作",
        tags: ["hover", "highlight", "action"],
        author: "system",
        usage: 0,
        dependencies: []
      }
    },
    {
      name: "双击聚焦动作",
      type: TemplateType.ACTION,
      data: {
        type: "doubleClick",
        callback: "CameraService.focusToDevice",
        parameters: {
          duration: 1000,
          easing: "easeInOutQuad"
        },
        highlight: {
          color: [1, 1, 0],
          duration: 1500,
          intensity: 1.5
        }
      },
      metadata: {
        category: "动作",
        tags: ["doubleClick", "focus", "action"],
        author: "system",
        usage: 0,
        dependencies: []
      }
    }
  ];

  sampleTemplates.forEach(template => {
    const result = templateManager.createTemplate(template);
    if (result.success) {
      console.log(`✅ 创建模板: ${template.name}`);
    } else {
      console.error(`❌ 创建模板失败: ${template.name}`, result.errors);
    }
  });

  console.log(`示例模板创建完成，共创建 ${sampleTemplates.length} 个模板`);

  // 添加调试函数到全局作用域
  if (typeof window !== 'undefined') {
    (window as any).debugTemplateSystem = () => {
      console.log('🔍 模板系统调试信息:');
      console.log('模板管理器:', templateManager);
      console.log('所有模板:', templateManager.registry.getAll());
      console.log('统计信息:', templateManager.getUsageStats());
      return templateManager;
    };
    console.log('💡 调试函数已添加: window.debugTemplateSystem()');
  }
}

// 当场景ID变化时，保存当前场景数据并加载新场景
watch(currentSceneId, (newId, oldId) => {
  // 如果有旧的场景ID，先保存当前编辑器的内容
  if (oldId && logicEditor.value) {
    const graphData = logicEditor.value.saveGraphData();
    sceneService.updateGraphData(oldId, graphData);
  }

  // 如果有新的场景ID，加载该场景的内容
  if (newId && logicEditor.value) {
    const scene = sceneService.getCurrentScene();
    if (scene && scene.graphData) {
      logicEditor.value.loadGraphData(scene.graphData);
    } else {
      // 如果是新场景没有图数据，清空编辑器
      logicEditor.value.clearGraph();
    }
  }
});

// 选中节点处理
function handleNodeSelected(node: Cell.Properties | null) {
  selectedNode.value = node;
}

// 节点数据更新处理
function handleNodeDataUpdated(event: { nodeId: string; data: any }) {
  // 如果逻辑编辑器实例存在，更新节点数据
  if (logicEditor.value) {
    logicEditor.value.updateNodeData(event.nodeId, event.data);
  }

  // 保存当前场景
  saveCurrentScene();
}

// 处理节点类型变更
function handleNodeTypeChanged(event: {
  nodeId: string;
  oldType: string;
  newType: string;
  newData: any;
}) {
  console.log(
    `节点类型变更: ${event.nodeId} 从 ${event.oldType} 变更为 ${event.newType}`
  );

  // 如果逻辑编辑器实例存在，直接调用其方法
  if (logicEditor.value) {
    logicEditor.value.changeNodeType(
      event.nodeId,
      event.oldType,
      event.newType,
      event.newData
    );
  }

  // 保存当前场景
  saveCurrentScene();
}

// 保存当前场景数据
function saveCurrentScene() {
  if (currentSceneId.value && logicEditor.value) {
    const graphData = logicEditor.value.saveGraphData();
    sceneService.updateGraphData(currentSceneId.value, graphData);
    sceneService.saveToLocalStorage();
  }
}

// 处理场景选择
function handleSceneSelected(sceneId: string) {
  currentSceneId.value = sceneId;
  console.log(`已选择场景: ${sceneId}`);
}

// 处理场景创建
function handleSceneCreated(scene: { id: string }) {
  currentSceneId.value = scene.id;
  console.log(`已创建场景: ${scene.id}`);

  // 在新场景创建后，需要让逻辑编辑器知道创建了新场景
  setTimeout(() => {
    if (logicEditor.value) {
      const event = new CustomEvent("scene-created", {
        detail: { sceneId: scene.id },
      });
      document.dispatchEvent(event);
    }
  }, 100);
}

// 从config.js导入场景
async function importFromConfig() {
  if (sceneManagerRef.value && sceneManagerRef.value.importFromConfig) {
    await sceneManagerRef.value.importFromConfig();
  }
}

// 处理配置生成
function handleConfigGenerated(config: any) {
  console.log('生成的配置:', config);
  saveCurrentScene();

  // 收集所有场景的图数据
  const allGraphsData: Record<string, any> = {};
  const scenes = sceneService.getAllScenes();

  scenes.forEach((scene) => {
    if (scene.graphData) {
      allGraphsData[scene.configId || scene.id] = scene.graphData;
    }
  });

  // 编译多场景配置
  const compiledConfig = compileMultipleGraphs(
    allGraphsData,
    currentSceneId.value || undefined
  );

  // 格式化为可读的JSON字符串
  generatedConfig.value = JSON.stringify(compiledConfig, null, 2);
}

// 切换节点库面板显示状态
function toggleLibraryPanel() {
  isLibraryCollapsed.value = !isLibraryCollapsed.value;
}

// 切换属性面板显示状态
function togglePropertiesPanel() {
  isPropertiesCollapsed.value = !isPropertiesCollapsed.value;
}

function toggleTemplatePanel() {
  isTemplatePanelVisible.value = !isTemplatePanelVisible.value;
}

// 开始拖拽调整
function startResize(event: MouseEvent | TouchEvent) {
  event.preventDefault();
  isResizing.value = true;

  const clientY = event instanceof MouseEvent ? event.clientY : event.touches[0].clientY;
  startY.value = clientY;

  if (templatePanelContainer.value) {
    startHeight.value = templatePanelContainer.value.offsetHeight;
  }

  // 添加全局事件监听器
  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
  document.addEventListener('touchmove', handleResize);
  document.addEventListener('touchend', stopResize);

  // 添加样式类以改变光标
  document.body.style.cursor = 'ns-resize';
  document.body.style.userSelect = 'none';
}

// 处理拖拽调整
function handleResize(event: MouseEvent | TouchEvent) {
  if (!isResizing.value || !templatePanelContainer.value) return;

  const clientY = event instanceof MouseEvent ? event.clientY : event.touches[0].clientY;
  const deltaY = startY.value - clientY; // 向上拖拽为正值
  const newHeight = startHeight.value + deltaY;

  // 限制高度范围
  const minHeight = 200;
  const maxHeight = window.innerHeight * 0.8;
  const clampedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));

  templatePanelContainer.value.style.height = `${clampedHeight}px`;
}

// 停止拖拽调整
function stopResize() {
  isResizing.value = false;

  // 移除全局事件监听器
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchmove', handleResize);
  document.removeEventListener('touchend', stopResize);

  // 恢复样式
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
}

function testTemplateSystem() {
  console.log('🧪 测试模板系统...');

  // 检查模板管理器
  console.log('模板管理器实例:', templateManager);

  // 检查模板数量
  const allTemplates = templateManager.registry.getAll();
  console.log(`📊 当前模板数量: ${allTemplates.length}`);

  // 显示所有模板
  allTemplates.forEach((template, index) => {
    console.log(`  ${index + 1}. ${template.name} (${template.type}) - ${template.description}`);
  });

  // 检查统计信息
  const stats = templateManager.getUsageStats();
  console.log('📈 统计信息:', stats);

  // 测试搜索功能
  const searchResults = templateManager.registry.search('网格');
  console.log(`🔍 搜索"网格"结果: ${searchResults.length} 个模板`);

  // 如果没有模板，尝试创建
  if (allTemplates.length === 0) {
    console.log('⚠️ 没有模板，尝试创建示例模板...');
    createSampleTemplates();
  }

  // 显示面板状态
  console.log(`📱 模板面板显示状态: ${isTemplatePanelVisible.value ? '显示' : '隐藏'}`);

  alert(`模板系统测试完成！\n当前模板数量: ${allTemplates.length}\n详细信息请查看控制台`);
}
</script>

<template>
  <div class="app-container">
    <!-- 顶部工具栏 -->
    <div class="app-toolbar">
      <h1></h1>
      <div class="toolbar-actions">
        <button @click="importFromConfig" class="toolbar-btn import-btn">
          从config.js导入场景
        </button>
        <button @click="toggleLibraryPanel" class="toolbar-btn">
          {{ isLibraryCollapsed ? "显示节点库" : "隐藏节点库" }}
        </button>
        <button @click="togglePropertiesPanel" class="toolbar-btn">
          {{ isPropertiesCollapsed ? "显示属性面板" : "隐藏属性面板" }}
        </button>
        <button @click="toggleTemplatePanel" class="toolbar-btn template-btn">
          {{ isTemplatePanelVisible ? "隐藏模板面板" : "显示模板面板" }}
        </button>
        <button @click="testTemplateSystem" class="toolbar-btn test-btn">
          测试模板系统
        </button>
      </div>
    </div>

    <div class="app-content">
      <!-- 左侧面板 - 节点库 -->
      <div class="left-panel" :class="{ collapsed: isLibraryCollapsed }">
        <button class="collapse-btn" @click="toggleLibraryPanel">
          {{ isLibraryCollapsed ? ">" : "<" }}
        </button>

        <div v-if="!isLibraryCollapsed" class="panel-content">
          <!-- 场景管理器组件 -->
          <SceneManager
            ref="sceneManagerRef"
            :currentSceneId="currentSceneId"
            @scene-selected="handleSceneSelected"
            @scene-created="handleSceneCreated"
            @scene-deleted="saveCurrentScene"
            @scene-renamed="saveCurrentScene"
          />

          <!-- 节点库组件 -->
          <NodeLibrary />
        </div>
      </div>

      <!-- 中间编辑区 -->
      <div class="main-content">
        <!-- 逻辑编辑器组件 -->
        <LogicEditor
          ref="logicEditor"
          @node-selected="handleNodeSelected"
          @config-generated="handleConfigGenerated"
          @node-type-changed="handleNodeTypeChanged"
        />

        <!-- 编译结果显示区域 -->
        <div v-if="generatedConfig" class="config-output">
          <div class="config-header">
            <h3>生成的配置</h3>
            <button @click="generatedConfig = ''" class="close-btn">
              关闭
            </button>
          </div>
          <pre>{{ generatedConfig }}</pre>
        </div>
      </div>

      <!-- 右侧面板 - 属性编辑器 -->
      <div class="right-panel" :class="{ collapsed: isPropertiesCollapsed }">
        <button class="collapse-btn" @click="togglePropertiesPanel">
          {{ isPropertiesCollapsed ? "<" : ">" }}
        </button>

        <div v-if="!isPropertiesCollapsed" class="panel-content">
          <!-- 属性面板组件 -->
          <PropertiesPanel
            :selectedNode="selectedNode"
            @update-node="handleNodeDataUpdated"
            @change-node-type="handleNodeTypeChanged"
          />

          <!-- 小地图区域 -->
          <div class="minimap-wrapper">
            <h3>小地图</h3>
            <div id="minimap-container"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部模板面板 -->
    <div v-if="isTemplatePanelVisible" class="template-panel-container" ref="templatePanelContainer">
      <!-- 拖拽调整手柄 -->
      <div
        class="resize-handle"
        @mousedown="startResize"
        @touchstart="startResize"
      >
        <div class="resize-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <TemplatePanel />
    </div>
  </div>
</template>

<style>
.app-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  color: #e0e0e0;
}

.app-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #252525;
  border-bottom: 1px solid #333;
}

.app-toolbar h1 {
  margin: 0;
  font-size: 20px;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

.toolbar-btn {
  padding: 8px 12px;
  background-color: #333;
  border: none;
  border-radius: 4px;
  color: #e0e0e0;
  cursor: pointer;
}

.import-btn {
  background-color: #5a8a6a;
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: relative;
  width: 300px;
  background-color: #252525;
  transition: width 0.3s;
  overflow: hidden;
}

.left-panel.collapsed,
.right-panel.collapsed {
  width: 30px;
}

.panel-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.collapse-btn {
  position: absolute;
  top: 50%;
  right: 0;
  width: 20px;
  height: 60px;
  background-color: #333;
  border: none;
  color: #e0e0e0;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px 0 0 3px;
}

.right-panel .collapse-btn {
  left: 0;
  right: auto;
  border-radius: 0 3px 3px 0;
}

.main-content {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.config-output {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 300px;
  background-color: #252525;
  border-top: 1px solid #333;
  overflow: auto;
  z-index: 100;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #333;
}

.config-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #e0e0e0;
  cursor: pointer;
}

.config-output pre {
  margin: 0;
  padding: 10px;
  white-space: pre-wrap;
  font-family: monospace;
}

.minimap-wrapper {
  padding: 10px;
  border-top: 1px solid #444;
}

.minimap-wrapper h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

#minimap-container {
  width: 100%;
  height: 150px;
  background-color: #1e1e1e;
  border: 1px solid #444;
}

.template-btn {
  background-color: #6f42c1;
  color: white;
}

.template-btn:hover {
  background-color: #5a32a3;
}

.test-btn {
  background-color: #28a745;
  color: white;
}

.test-btn:hover {
  background-color: #218838;
}

.template-panel-container {
  height: 50vh; /* 使用视口高度的50%作为初始高度 */
  min-height: 300px; /* 最小高度 */
  max-height: 80vh; /* 最大高度 */
  border-top: 2px solid #4a5568;
  background-color: #2d3748;
  overflow: auto; /* 改为auto以支持滚动 */
  padding: 0;
  box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
  transition: height 0.3s ease;
  position: relative;
  z-index: 10;
}

/* 拖拽调整手柄 */
.resize-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  cursor: ns-resize;
  background-color: #1a202c;
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #4a5568;
}

.resize-handle:hover {
  background-color: #2c5282;
}

.resize-indicator {
  display: flex;
  gap: 3px;
  align-items: center;
}

.resize-indicator span {
  width: 30px;
  height: 3px;
  background-color: #63b3ed;
  border-radius: 2px;
  opacity: 0.7;
}

.resize-handle:hover .resize-indicator span {
  opacity: 1;
}
</style>
