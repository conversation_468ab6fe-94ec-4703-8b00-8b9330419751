/**
 * DragDropManager.ts
 * 拖拽管理器 - 处理模板拖拽创建功能
 */

import { Template } from './TemplateManager';
import { templateInstantiator, InstantiationOptions } from './TemplateInstantiator';
import { instanceTracker } from './InstanceTracker';

/**
 * 拖拽数据
 */
export interface DragData {
  type: 'template';
  template: Template;
  sourceElement: HTMLElement;
  offset: { x: number; y: number };
}

/**
 * 拖拽事件
 */
export interface DragEvent {
  type: 'dragstart' | 'dragmove' | 'dragend' | 'drop';
  data: DragData;
  position: { x: number; y: number };
  target?: HTMLElement;
}

/**
 * 拖拽选项
 */
export interface DragOptions {
  // 是否显示拖拽预览
  showPreview?: boolean;
  
  // 预览样式
  previewStyle?: Partial<CSSStyleDeclaration>;
  
  // 是否限制拖拽区域
  constrainToContainer?: boolean;
  
  // 容器选择器
  containerSelector?: string;
  
  // 拖拽阈值（像素）
  threshold?: number;
}

/**
 * 放置目标
 */
export interface DropTarget {
  element: HTMLElement;
  accept: (data: DragData) => boolean;
  onDrop: (data: DragData, position: { x: number; y: number }) => void;
  onDragOver?: (data: DragData, position: { x: number; y: number }) => void;
  onDragLeave?: () => void;
}

/**
 * 拖拽管理器
 */
export class DragDropManager {
  private static instance: DragDropManager;
  
  // 当前拖拽状态
  private isDragging = false;
  private currentDrag: DragData | null = null;
  private dragPreview: HTMLElement | null = null;
  
  // 拖拽选项
  private options: DragOptions = {
    showPreview: true,
    threshold: 5,
    constrainToContainer: true,
    containerSelector: '.app-container'
  };
  
  // 放置目标
  private dropTargets: Set<DropTarget> = new Set();
  
  // 事件监听器
  private eventListeners: Map<string, ((event: DragEvent) => void)[]> = new Map();
  
  // 拖拽起始位置
  private startPosition = { x: 0, y: 0 };
  private currentPosition = { x: 0, y: 0 };

  private constructor() {
    this.initializeEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): DragDropManager {
    if (!DragDropManager.instance) {
      DragDropManager.instance = new DragDropManager();
    }
    return DragDropManager.instance;
  }

  /**
   * 设置拖拽选项
   */
  setOptions(options: Partial<DragOptions>): void {
    this.options = { ...this.options, ...options };
    console.log('🎛️ 拖拽选项已更新:', this.options);
  }

  /**
   * 注册可拖拽元素
   */
  registerDraggable(element: HTMLElement, template: Template): void {
    console.log(`🎯 注册可拖拽元素: ${template.name}`);
    
    element.draggable = true;
    element.style.cursor = 'grab';
    
    // 添加数据属性
    element.dataset.templateId = template.id;
    element.dataset.draggable = 'true';
    
    // 绑定事件
    element.addEventListener('mousedown', (e) => this.handleMouseDown(e, element, template));
    element.addEventListener('dragstart', (e) => this.handleDragStart(e, element, template));
    
    console.log(`✅ 元素注册完成: ${template.name}`);
  }

  /**
   * 注销可拖拽元素
   */
  unregisterDraggable(element: HTMLElement): void {
    element.draggable = false;
    element.style.cursor = '';
    
    // 移除数据属性
    delete element.dataset.templateId;
    delete element.dataset.draggable;
    
    // 移除事件监听器（这里简化处理）
    element.removeEventListener('mousedown', this.handleMouseDown as any);
    element.removeEventListener('dragstart', this.handleDragStart as any);
    
    console.log('🗑️ 可拖拽元素已注销');
  }

  /**
   * 注册放置目标
   */
  registerDropTarget(target: DropTarget): void {
    this.dropTargets.add(target);
    
    // 添加放置目标样式
    target.element.style.position = 'relative';
    
    console.log('🎯 放置目标已注册');
  }

  /**
   * 注销放置目标
   */
  unregisterDropTarget(target: DropTarget): void {
    this.dropTargets.delete(target);
    console.log('🗑️ 放置目标已注销');
  }

  /**
   * 开始拖拽
   */
  startDrag(template: Template, sourceElement: HTMLElement, startPos: { x: number; y: number }): void {
    if (this.isDragging) {
      console.warn('⚠️ 已有拖拽操作在进行中');
      return;
    }

    console.log(`🚀 开始拖拽模板: ${template.name}`);
    
    this.isDragging = true;
    this.startPosition = startPos;
    this.currentPosition = startPos;
    
    // 创建拖拽数据
    this.currentDrag = {
      type: 'template',
      template,
      sourceElement,
      offset: { x: 0, y: 0 }
    };
    
    // 创建拖拽预览
    if (this.options.showPreview) {
      this.createDragPreview(template, startPos);
    }
    
    // 添加全局事件监听器
    document.addEventListener('mousemove', this.handleMouseMove);
    document.addEventListener('mouseup', this.handleMouseUp);
    
    // 触发拖拽开始事件
    this.emitEvent({
      type: 'dragstart',
      data: this.currentDrag,
      position: startPos
    });
    
    // 更新光标
    document.body.style.cursor = 'grabbing';
  }

  /**
   * 结束拖拽
   */
  endDrag(dropPosition?: { x: number; y: number }): void {
    if (!this.isDragging || !this.currentDrag) {
      return;
    }

    console.log('🏁 结束拖拽');
    
    const finalPosition = dropPosition || this.currentPosition;
    
    // 检查放置目标
    const dropTarget = this.findDropTarget(finalPosition);
    
    if (dropTarget && dropTarget.accept(this.currentDrag)) {
      console.log('✅ 找到有效的放置目标');
      
      // 执行放置操作
      this.performDrop(dropTarget, this.currentDrag, finalPosition);
      
      // 触发放置事件
      this.emitEvent({
        type: 'drop',
        data: this.currentDrag,
        position: finalPosition,
        target: dropTarget.element
      });
    } else {
      console.log('❌ 未找到有效的放置目标');
    }
    
    // 清理拖拽状态
    this.cleanup();
    
    // 触发拖拽结束事件
    this.emitEvent({
      type: 'dragend',
      data: this.currentDrag,
      position: finalPosition
    });
  }

  /**
   * 执行放置操作
   */
  private performDrop(target: DropTarget, dragData: DragData, position: { x: number; y: number }): void {
    console.log(`📍 在位置 (${position.x}, ${position.y}) 放置模板: ${dragData.template.name}`);
    
    try {
      // 调用放置目标的处理函数
      target.onDrop(dragData, position);
      
      // 创建模板实例
      this.createTemplateInstance(dragData.template, position);
      
    } catch (error) {
      console.error('❌ 放置操作失败:', error);
    }
  }

  /**
   * 创建模板实例
   */
  private createTemplateInstance(template: Template, position: { x: number; y: number }): void {
    console.log(`🏗️ 创建模板实例: ${template.name}`);
    
    const options: InstantiationOptions = {
      position,
      sceneId: 'default', // 这里可以根据实际情况设置
      applyDefaultStyles: true,
      resolveDependencies: true
    };
    
    const result = templateInstantiator.instantiateTemplate(template, options);
    
    if (result.success && result.instance) {
      // 注册到实例追踪器
      instanceTracker.registerInstance(result.instance);
      
      console.log(`✅ 模板实例创建成功: ${result.instance.id}`);
      
      // 这里可以添加将节点添加到画布的逻辑
      this.addNodeToCanvas(result.node!, position);
      
    } else {
      console.error('❌ 模板实例创建失败:', result.errors);
    }
  }

  /**
   * 添加节点到画布（模拟实现）
   */
  private addNodeToCanvas(node: any, position: { x: number; y: number }): void {
    console.log(`🎨 添加节点到画布: ${node.id} at (${position.x}, ${position.y})`);
    
    // 在实际应用中，这里会调用X6图形库的API来添加节点
    // 现在我们只是模拟这个过程
    
    // 可以触发一个自定义事件来通知其他组件
    const event = new CustomEvent('nodeAdded', {
      detail: { node, position }
    });
    document.dispatchEvent(event);
  }

  /**
   * 处理鼠标按下事件
   */
  private handleMouseDown = (e: MouseEvent, element: HTMLElement, template: Template): void => {
    if (e.button !== 0) return; // 只处理左键
    
    const startPos = { x: e.clientX, y: e.clientY };
    
    // 设置拖拽准备状态
    let dragStarted = false;
    
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const distance = Math.sqrt(
        Math.pow(moveEvent.clientX - startPos.x, 2) + 
        Math.pow(moveEvent.clientY - startPos.y, 2)
      );
      
      if (!dragStarted && distance > (this.options.threshold || 5)) {
        dragStarted = true;
        this.startDrag(template, element, startPos);
      }
    };
    
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  /**
   * 处理拖拽开始事件
   */
  private handleDragStart = (e: DragEvent, element: HTMLElement, template: Template): void => {
    // 设置拖拽数据
    if (e.dataTransfer) {
      e.dataTransfer.setData('application/json', JSON.stringify({
        type: 'template',
        templateId: template.id
      }));
      e.dataTransfer.effectAllowed = 'copy';
    }
  };

  /**
   * 处理鼠标移动事件
   */
  private handleMouseMove = (e: MouseEvent): void => {
    if (!this.isDragging) return;
    
    this.currentPosition = { x: e.clientX, y: e.clientY };
    
    // 更新拖拽预览位置
    if (this.dragPreview) {
      this.updateDragPreview(this.currentPosition);
    }
    
    // 检查放置目标
    this.updateDropTargets(this.currentPosition);
    
    // 触发拖拽移动事件
    if (this.currentDrag) {
      this.emitEvent({
        type: 'dragmove',
        data: this.currentDrag,
        position: this.currentPosition
      });
    }
  };

  /**
   * 处理鼠标释放事件
   */
  private handleMouseUp = (e: MouseEvent): void => {
    if (!this.isDragging) return;
    
    const dropPosition = { x: e.clientX, y: e.clientY };
    this.endDrag(dropPosition);
  };

  /**
   * 创建拖拽预览
   */
  private createDragPreview(template: Template, position: { x: number; y: number }): void {
    this.dragPreview = document.createElement('div');
    this.dragPreview.className = 'drag-preview';
    this.dragPreview.innerHTML = `
      <div class="preview-content">
        <span class="preview-icon">${this.getTemplateIcon(template.type)}</span>
        <span class="preview-name">${template.name}</span>
      </div>
    `;
    
    // 应用样式
    Object.assign(this.dragPreview.style, {
      position: 'fixed',
      left: `${position.x + 10}px`,
      top: `${position.y + 10}px`,
      background: '#2d3748',
      color: '#f7fafc',
      padding: '8px 12px',
      borderRadius: '6px',
      border: '1px solid #4a5568',
      fontSize: '14px',
      pointerEvents: 'none',
      zIndex: '9999',
      boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
      transform: 'translate(-50%, -50%)',
      ...this.options.previewStyle
    });
    
    document.body.appendChild(this.dragPreview);
  }

  /**
   * 更新拖拽预览位置
   */
  private updateDragPreview(position: { x: number; y: number }): void {
    if (this.dragPreview) {
      this.dragPreview.style.left = `${position.x + 10}px`;
      this.dragPreview.style.top = `${position.y + 10}px`;
    }
  }

  /**
   * 查找放置目标
   */
  private findDropTarget(position: { x: number; y: number }): DropTarget | null {
    const element = document.elementFromPoint(position.x, position.y);
    if (!element) return null;
    
    for (const target of this.dropTargets) {
      if (target.element.contains(element as Node)) {
        return target;
      }
    }
    
    return null;
  }

  /**
   * 更新放置目标状态
   */
  private updateDropTargets(position: { x: number; y: number }): void {
    const currentTarget = this.findDropTarget(position);
    
    this.dropTargets.forEach(target => {
      if (target === currentTarget) {
        // 当前目标
        if (this.currentDrag && target.accept(this.currentDrag)) {
          target.element.classList.add('drag-over');
          target.onDragOver?.(this.currentDrag, position);
        }
      } else {
        // 其他目标
        target.element.classList.remove('drag-over');
        target.onDragLeave?.();
      }
    });
  }

  /**
   * 清理拖拽状态
   */
  private cleanup(): void {
    this.isDragging = false;
    this.currentDrag = null;
    
    // 移除拖拽预览
    if (this.dragPreview) {
      document.body.removeChild(this.dragPreview);
      this.dragPreview = null;
    }
    
    // 移除全局事件监听器
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
    
    // 清理放置目标状态
    this.dropTargets.forEach(target => {
      target.element.classList.remove('drag-over');
      target.onDragLeave?.();
    });
    
    // 恢复光标
    document.body.style.cursor = '';
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    const eventTypes = ['dragstart', 'dragmove', 'dragend', 'drop'];
    eventTypes.forEach(type => {
      this.eventListeners.set(type, []);
    });
  }

  /**
   * 触发事件
   */
  private emitEvent(event: DragEvent): void {
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('拖拽事件监听器执行失败:', error);
        }
      });
    }
  }

  /**
   * 获取模板图标
   */
  private getTemplateIcon(type: string): string {
    const icons: Record<string, string> = {
      'mesh': '🏗️',
      'style': '🎨',
      'camera': '📷',
      'action': '⚡',
      'label': '🏷️',
      'position': '📍',
      'interaction': '🎭',
      'environment': '🌍',
      'callback': '🔄'
    };
    return icons[type] || '📄';
  }

  /**
   * 添加事件监听器
   */
  addEventListener(type: string, listener: (event: DragEvent) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: string, listener: (event: DragEvent) => void): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
}

// 导出单例实例
export const dragDropManager = DragDropManager.getInstance();
