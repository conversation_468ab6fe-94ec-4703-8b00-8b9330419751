# 模板系统用户指南

## 🎯 概述

ddd-flow的模板系统允许您创建、管理和重用配置模板，大大提高3D场景配置的效率。模板系统支持多种类型的模板，包括网格、样式、动作、标签等。

## 🚀 快速开始

### 1. 打开模板面板
- 点击工具栏中的"显示模板面板"按钮
- 模板面板将在应用底部显示

### 2. 查看现有模板
- 模板按分类组织显示
- 点击分类名称展开/折叠分类
- 每个模板显示名称、类型图标和描述

### 3. 使用模板
- 在节点的属性面板中找到模板选择器
- 点击下拉按钮查看可用模板
- 选择合适的模板应用到节点

## 📋 模板类型

### 🏗️ 网格模板 (Mesh)
用于定义3D场景中的网格对象集合。

**示例用途:**
- 建筑层级网格集合
- 设备组网格集合
- 区域网格集合

**数据格式:**
```json
["网格名称1", "网格名称2", "网格名称3"]
```

### 🎨 样式模板 (Style)
用于定义UI元素的视觉样式。

**示例用途:**
- 标签样式配置
- 高亮效果样式
- 主题色彩方案

**数据格式:**
```json
{
  "backgroundColor": "#ff6b35",
  "color": "#ffffff",
  "borderRadius": "8px"
}
```

### ⚡ 动作模板 (Action)
用于定义交互行为配置。

**示例用途:**
- 悬停高亮效果
- 点击响应行为
- 双击聚焦动作

**数据格式:**
```json
{
  "type": "hover",
  "highlight": {
    "color": [1, 1, 0],
    "duration": 300
  }
}
```

### 🏷️ 标签模板 (Label)
用于定义标签显示配置。

**示例用途:**
- 楼层标签样式
- 设备标签配置
- 房间标签设置

**数据格式:**
```json
{
  "style": "default",
  "position": "top",
  "fontSize": 14,
  "color": "#ffffff"
}
```

### 📍 位置模板 (Position)
用于定义3D空间中的位置坐标。

**示例用途:**
- 相机位置预设
- 对象初始位置
- 动画关键点

**数据格式:**
```json
[x, y, z]
```

## 🛠️ 模板管理

### 创建新模板

1. **打开模板面板**
2. **点击"创建新模板"按钮**
3. **填写模板信息:**
   - 名称: 模板的显示名称
   - 类型: 选择模板类型
   - 分类: 模板的分组分类
   - 描述: 模板的用途说明
4. **编辑模板数据:**
   - 在JSON编辑器中输入模板数据
   - 系统会自动验证数据格式
5. **添加标签:**
   - 用逗号分隔的标签列表
   - 便于搜索和分类
6. **保存模板**

### 编辑现有模板

1. **在模板面板中选择模板**
2. **点击"编辑"按钮**
3. **修改模板信息和数据**
4. **保存更改**

### 删除模板

1. **选择要删除的模板**
2. **点击删除按钮 (🗑️)**
3. **确认删除操作**

### 复制模板

1. **选择要复制的模板**
2. **点击复制按钮 (📋)**
3. **系统会创建一个副本**
4. **可以编辑副本创建新模板**

## 🎯 在节点中使用模板

### 事件节点中的网格模板

1. **创建或选择事件节点**
2. **在属性面板中找到"目标网格"配置**
3. **选择"模板引用"模式**
4. **从下拉列表中选择网格模板**
5. **模板会自动应用到节点配置**

### 模板引用格式

当使用模板时，系统会生成如下格式的引用：

```json
{
  "$ref": "templates.1.meshes.buildingLevels"
}
```

### 扩展模板参数

您可以在使用模板的同时添加额外参数：

```json
{
  "$ref": "templates.1.meshes.buildingLevels",
  "$extend": ["额外网格1", "额外网格2"]
}
```

### 覆盖模板参数

您也可以覆盖模板中的特定参数：

```json
{
  "$ref": "templates.1.styles.orangeGradient",
  "$override": {
    "color": "#000000"
  }
}
```

## 🔍 搜索和过滤

### 搜索模板
- 在搜索框中输入关键词
- 系统会搜索模板名称、描述和标签
- 实时显示搜索结果

### 按类型过滤
- 点击类型过滤按钮
- 只显示选定类型的模板
- 可以选择多个类型

### 按分类浏览
- 模板按分类组织
- 点击分类名称展开/折叠
- 每个分类显示模板数量

## 🧪 测试和调试

### 浏览器控制台调试

在浏览器控制台中可以使用以下调试命令：

```javascript
// 检查模板系统状态
debugTemplateSystem()

// 快速验证功能
quickValidation()

// 运行集成测试
runIntegrationTest()

// 测试模板面板
testTemplatePanel()
```

### 测试模板功能

1. **点击"测试模板系统"按钮**
2. **查看控制台输出**
3. **验证模板数量和内容**
4. **测试模板选择和应用**

## 💡 最佳实践

### 模板命名
- 使用描述性的名称
- 包含模板的用途和类型
- 避免使用特殊字符

### 模板分类
- 按功能或用途分类
- 保持分类结构简洁
- 使用一致的命名规范

### 模板数据
- 确保JSON格式正确
- 添加必要的注释和说明
- 验证数据的有效性

### 标签使用
- 添加相关的搜索标签
- 使用一致的标签词汇
- 包含类型和用途标签

## 🔧 故障排除

### 模板面板不显示
1. 检查是否点击了"显示模板面板"按钮
2. 查看浏览器控制台是否有错误
3. 运行 `debugTemplateSystem()` 检查状态

### 模板列表为空
1. 点击"刷新模板"按钮
2. 运行 `quickValidation()` 检查系统状态
3. 尝试创建新模板

### 模板选择器不工作
1. 确保选中了正确的节点类型
2. 检查属性面板是否正确显示
3. 运行 `testTemplateApplication()` 进行测试

### 模板应用失败
1. 检查模板数据格式是否正确
2. 验证模板引用路径是否有效
3. 查看控制台错误信息

## 📞 获取帮助

如果遇到问题或需要帮助：

1. **查看浏览器控制台** - 检查错误信息
2. **运行调试命令** - 使用内置的调试工具
3. **检查文档** - 参考技术文档和API说明
4. **提交反馈** - 报告问题和建议改进

## 🎉 总结

模板系统为ddd-flow提供了强大的配置重用能力，通过合理使用模板，您可以：

- **提高效率** - 快速应用预定义配置
- **保持一致性** - 统一的样式和行为
- **简化维护** - 集中管理常用配置
- **促进协作** - 团队共享配置模板

开始使用模板系统，让您的3D场景配置更加高效和专业！
