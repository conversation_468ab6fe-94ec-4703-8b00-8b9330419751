/**
 * 测试连接推断算法
 */

import { ConfigParser } from './services/ConfigParser.js';
import { ConnectionInference } from './services/ConnectionInference.js';

// 创建测试数据
const testSceneConfig = {
  name: "连接推断测试场景",
  models: ["test.glb"],
  actions: [
    {
      actionType: "doubleClick",
      meshNames: ["mesh1"],
      config: {
        highlight: { color: [1, 0, 0], duration: 500 },
        callback: "CameraService.focusToDevice",
        parameters: { deviceId: "device1" }
      }
    }
  ],
  lifecycle: {
    onActivated: [
      {
        trigger: "immediate",
        callback: "DeviceService.setupDataDrivenPolling",
        parameters: {
          dataSource: { type: "polling", interval: 5000 },
          mappings: "test.csv"
        }
      }
    ]
  },
  staticLabels: [
    {
      meshNames: ["mesh1"],
      customNames: { "mesh1": "测试标签" },
      click: {
        enabled: true,
        callback: "UIService.showMessage",
        parameters: { message: "标签被点击" }
      }
    }
  ]
};

/**
 * 测试连接推断功能
 */
function testConnectionInference() {
  console.log("开始测试连接推断算法...");
  
  try {
    const parser = new ConfigParser();
    const inference = new ConnectionInference();
    
    // 解析场景配置
    const parsedData = parser.parseScene("test-scene", testSceneConfig);
    
    console.log("解析后的数据:");
    console.log("- 事件节点:", parsedData.eventNodes.length);
    console.log("- 动作节点:", parsedData.actionNodes.length);
    console.log("- 生命周期节点:", parsedData.lifecycleNodes.length);
    console.log("- 静态标签节点:", parsedData.staticLabelNodes.length);
    console.log("- 数据节点:", parsedData.dataNodes.length);
    console.log("- 原始连接:", parsedData.connections.length);
    
    // 测试单独的连接推断方法
    console.log("\n测试各种连接推断方法:");
    
    // 1. 测试事件到动作的连接
    if (parsedData.eventNodes.length > 0 && parsedData.actionNodes.length > 0) {
      const eventToActionConns = inference.eventToAction(
        parsedData.eventNodes[0], 
        parsedData.actionNodes
      );
      console.log("- 事件到动作连接:", eventToActionConns.length);
      eventToActionConns.forEach(conn => {
        console.log(`  ${conn.source} -> ${conn.target} (${conn.sourcePort} -> ${conn.targetPort})`);
      });
    }
    
    // 2. 测试生命周期到回调的连接
    if (parsedData.lifecycleNodes.length > 0 && parsedData.actionNodes.length > 0) {
      const lifecycleToCallbackConns = inference.lifecycleToCallback(
        parsedData.lifecycleNodes[0],
        parsedData.actionNodes
      );
      console.log("- 生命周期到回调连接:", lifecycleToCallbackConns.length);
      lifecycleToCallbackConns.forEach(conn => {
        console.log(`  ${conn.source} -> ${conn.target} (${conn.sourcePort} -> ${conn.targetPort})`);
      });
    }
    
    // 3. 测试静态标签到事件的连接
    if (parsedData.staticLabelNodes.length > 0 && parsedData.eventNodes.length > 0) {
      const labelToEventConns = inference.staticLabelToEvent(
        parsedData.staticLabelNodes[0],
        parsedData.eventNodes
      );
      console.log("- 静态标签到事件连接:", labelToEventConns.length);
      labelToEventConns.forEach(conn => {
        console.log(`  ${conn.source} -> ${conn.target} (${conn.sourcePort} -> ${conn.targetPort})`);
      });
    }
    
    // 4. 测试数据流连接
    if (parsedData.dataNodes.length > 0) {
      const dataFlowConns = inference.dataFlow(parsedData.dataNodes);
      console.log("- 数据流连接:", dataFlowConns.length);
      dataFlowConns.forEach(conn => {
        console.log(`  ${conn.source} -> ${conn.target} (${conn.sourcePort} -> ${conn.targetPort})`);
      });
    }
    
    // 5. 测试完整的连接推断
    const allInferredConns = inference.inferAllConnections(parsedData);
    console.log("\n完整连接推断结果:");
    console.log("- 推断出的连接总数:", allInferredConns.length);
    
    allInferredConns.forEach((conn, index) => {
      console.log(`  ${index + 1}. ${conn.source} -> ${conn.target} (${conn.sourcePort} -> ${conn.targetPort})`);
    });
    
    // 验证连接的有效性
    console.log("\n连接有效性验证:");
    const allNodeIds = [
      parsedData.sceneConfig.id,
      ...parsedData.eventNodes.map(n => n.id),
      ...parsedData.actionNodes.map(n => n.id),
      ...parsedData.lifecycleNodes.map(n => n.id),
      ...parsedData.staticLabelNodes.map(n => n.id),
      ...parsedData.dataNodes.map(n => n.id),
    ];
    
    let validConnections = 0;
    let invalidConnections = 0;
    
    allInferredConns.forEach(conn => {
      const sourceExists = allNodeIds.includes(conn.source);
      const targetExists = allNodeIds.includes(conn.target);
      
      if (sourceExists && targetExists) {
        validConnections++;
      } else {
        invalidConnections++;
        console.log(`  无效连接: ${conn.source} -> ${conn.target} (源存在: ${sourceExists}, 目标存在: ${targetExists})`);
      }
    });
    
    console.log(`- 有效连接: ${validConnections}`);
    console.log(`- 无效连接: ${invalidConnections}`);
    
    // 测试连接去重
    const duplicatedConns = [...allInferredConns, ...allInferredConns.slice(0, 2)]; // 添加重复连接
    const uniqueConns = parser.deduplicateConnections ? 
      parser.deduplicateConnections(duplicatedConns) : 
      duplicatedConns;
    
    console.log(`\n连接去重测试:`);
    console.log(`- 去重前: ${duplicatedConns.length}`);
    console.log(`- 去重后: ${uniqueConns.length}`);
    
    console.log("\n连接推断算法测试完成！");
    return true;
    
  } catch (error) {
    console.error("连接推断算法测试失败:", error);
    return false;
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testConnectionInference = testConnectionInference;
  console.log("测试函数已添加到window对象，可以在控制台中调用 testConnectionInference()");
}

export { testConnectionInference };
