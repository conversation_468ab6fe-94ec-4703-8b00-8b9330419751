/**
 * ParameterSyncDemo.ts
 * 参数同步功能演示
 */

import { TemplateManager } from '../services/TemplateManager';
import { parameterSyncManager } from '../services/ParameterSyncManager';
import { conflictResolver } from '../services/ConflictResolver';
import { batchUpdateManager } from '../services/BatchUpdateManager';
import { instanceTracker } from '../services/InstanceTracker';
import { nodeSelector } from '../services/NodeSelector';

/**
 * 参数同步功能演示
 */
export class ParameterSyncDemo {
  private templateManager: TemplateManager;

  constructor() {
    this.templateManager = TemplateManager.getInstance();
  }

  /**
   * 运行完整的参数同步演示
   */
  async runCompleteDemo(): Promise<void> {
    console.log('🚀 开始参数同步功能演示');
    console.log('='.repeat(60));

    try {
      // 1. 准备演示数据
      await this.prepareTestData();
      
      // 2. 演示参数变更检测
      await this.demoParameterChangeDetection();
      
      // 3. 演示冲突检测和解决
      await this.demoConflictDetectionAndResolution();
      
      // 4. 演示批量同步
      await this.demoBatchSync();
      
      // 5. 演示智能冲突解决
      await this.demoSmartConflictResolution();
      
      // 6. 演示批量更新管理
      await this.demoBatchUpdateManagement();

      console.log('='.repeat(60));
      console.log('✅ 参数同步功能演示完成！');
      
      // 显示最终统计
      this.showFinalStats();
      
    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 准备测试数据
   */
  private async prepareTestData(): Promise<void> {
    console.log('\n📦 1. 准备测试数据');
    console.log('-'.repeat(40));

    // 创建测试模板
    const buttonNode = nodeSelector.createMockNode('button');
    const templateResult = this.templateManager.createTemplateFromNode(
      buttonNode,
      '演示按钮模板',
      '用于参数同步演示的按钮模板',
      '演示'
    );

    if (templateResult.success && templateResult.data) {
      console.log(`✅ 创建测试模板: ${templateResult.data.name}`);
      
      // 创建多个实例
      for (let i = 0; i < 3; i++) {
        const instanceResult = this.templateManager.instantiateTemplate(templateResult.data.id, {
          position: { x: 100 + i * 150, y: 100 },
          sceneId: 'demo-scene',
          parameterOverrides: {
            backgroundColor: i === 0 ? '#ff6b6b' : i === 1 ? '#4ecdc4' : '#45b7d1',
            textColor: '#ffffff',
            fontSize: 14 + i * 2
          }
        });
        
        if (instanceResult.success) {
          console.log(`✅ 创建实例 ${i + 1}: ${instanceResult.data?.instance.id}`);
        }
      }
    }

    console.log('✅ 测试数据准备完成');
  }

  /**
   * 演示参数变更检测
   */
  private async demoParameterChangeDetection(): Promise<void> {
    console.log('\n🔍 2. 参数变更检测演示');
    console.log('-'.repeat(40));

    const templates = this.templateManager.getAllTemplates();
    if (templates.length === 0) {
      console.log('❌ 没有可用的模板');
      return;
    }

    const template = templates[0];
    console.log(`检测模板变更: ${template.name}`);

    // 创建模板的修改版本
    const modifiedTemplate = {
      ...template,
      version: '1.1.0',
      definition: {
        ...template.definition,
        parameters: [
          ...(template.definition?.parameters || []),
          {
            name: 'borderRadius',
            type: 'number',
            description: '边框圆角',
            defaultValue: 4,
            required: false
          }
        ]
      }
    };

    // 检测变更
    const changes = this.templateManager.detectTemplateChanges(template, modifiedTemplate);
    
    console.log(`✅ 检测到 ${changes.length} 个变更:`);
    changes.forEach(change => {
      console.log(`  ${this.getChangeIcon(change.changeType)} ${change.description}`);
    });

    // 检测冲突
    const conflicts = parameterSyncManager.detectConflicts(template.id, changes);
    console.log(`✅ 检测到 ${conflicts.length} 个潜在冲突`);
  }

  /**
   * 演示冲突检测和解决
   */
  private async demoConflictDetectionAndResolution(): Promise<void> {
    console.log('\n⚠️ 3. 冲突检测和解决演示');
    console.log('-'.repeat(40));

    // 获取所有冲突
    const allConflicts = this.templateManager.getParameterConflicts();
    console.log(`当前冲突数量: ${allConflicts.length}`);

    if (allConflicts.length === 0) {
      // 创建一个模拟冲突
      console.log('创建模拟冲突进行演示...');
      
      const instances = instanceTracker.queryInstances();
      if (instances.length > 0) {
        const instance = instances[0];
        
        // 模拟参数冲突
        const mockConflict = {
          id: `conflict_${Date.now()}`,
          instanceId: instance.id,
          templateId: instance.templateId,
          parameterName: 'backgroundColor',
          conflictType: 'custom_override' as const,
          templateValue: '#3182ce',
          instanceValue: '#ff6b6b',
          description: '模板默认值已变更，但实例有自定义覆盖值',
          severity: 'medium' as const,
          autoResolvable: true,
          suggestedResolution: '建议保留实例自定义值',
          timestamp: new Date()
        };

        console.log(`✅ 创建模拟冲突: ${mockConflict.parameterName}`);
        
        // 演示不同的解决策略
        const strategies = ['preserve_instance', 'use_template', 'smart_merge'];
        
        for (const strategyId of strategies) {
          const strategy = conflictResolver.getAvailableStrategies().find(s => s.id === strategyId);
          if (strategy) {
            console.log(`\n🔧 尝试策略: ${strategy.name}`);
            
            const result = conflictResolver.resolveConflict(mockConflict, {
              instance: instance,
              templateValue: mockConflict.templateValue,
              instanceValue: mockConflict.instanceValue
            }, strategyId);
            
            console.log(`  结果: ${result.success ? '✅ 成功' : '❌ 失败'}`);
            console.log(`  置信度: ${(result.confidence * 100).toFixed(1)}%`);
            console.log(`  说明: ${result.explanation}`);
          }
        }
      }
    }
  }

  /**
   * 演示批量同步
   */
  private async demoBatchSync(): Promise<void> {
    console.log('\n🔄 4. 批量同步演示');
    console.log('-'.repeat(40));

    const templates = this.templateManager.getAllTemplates();
    if (templates.length === 0) {
      console.log('❌ 没有可用的模板');
      return;
    }

    const templateIds = templates.map(t => t.id);
    console.log(`批量同步 ${templateIds.length} 个模板...`);

    try {
      const result = await this.templateManager.batchSyncTemplates(templateIds, {
        strategy: 'preserve_overrides',
        batchSize: 2,
        continueOnError: true
      });

      console.log('✅ 批量同步完成:');
      console.log(`  总处理: ${result.totalProcessed}`);
      console.log(`  成功: ${result.successCount}`);
      console.log(`  失败: ${result.failureCount}`);
      console.log(`  耗时: ${result.duration}ms`);

      if (result.errors.length > 0) {
        console.log('  错误:');
        result.errors.forEach(error => console.log(`    ❌ ${error}`));
      }
    } catch (error) {
      console.error('❌ 批量同步失败:', error);
    }
  }

  /**
   * 演示智能冲突解决
   */
  private async demoSmartConflictResolution(): Promise<void> {
    console.log('\n🤖 5. 智能冲突解决演示');
    console.log('-'.repeat(40));

    const templates = this.templateManager.getAllTemplates();
    if (templates.length === 0) {
      console.log('❌ 没有可用的模板');
      return;
    }

    const templateIds = templates.map(t => t.id);
    console.log(`智能解决 ${templateIds.length} 个模板的冲突...`);

    try {
      const result = await this.templateManager.batchResolveConflicts(templateIds, {
        strategy: 'smart',
        confidenceThreshold: 0.7
      });

      console.log('✅ 智能冲突解决完成:');
      console.log(`  总处理: ${result.totalProcessed}`);
      console.log(`  成功: ${result.successCount}`);
      console.log(`  失败: ${result.failureCount}`);

      if (result.details) {
        const details = result.details as any;
        console.log(`  总解决: ${details.totalResolved || 0} 个冲突`);
        console.log(`  待处理: ${details.totalPending || 0} 个冲突`);
      }
    } catch (error) {
      console.error('❌ 智能冲突解决失败:', error);
    }
  }

  /**
   * 演示批量更新管理
   */
  private async demoBatchUpdateManagement(): Promise<void> {
    console.log('\n📋 6. 批量更新管理演示');
    console.log('-'.repeat(40));

    // 获取所有批量任务
    const tasks = this.templateManager.getBatchUpdateTasks();
    console.log(`当前批量任务数量: ${tasks.length}`);

    if (tasks.length > 0) {
      console.log('\n任务列表:');
      tasks.forEach((task, index) => {
        console.log(`  ${index + 1}. ${task.name}`);
        console.log(`     状态: ${this.getTaskStatusText(task.status)}`);
        console.log(`     进度: ${task.progress}% (${task.processedItems}/${task.totalItems})`);
        
        if (task.status === 'completed' && task.duration) {
          console.log(`     耗时: ${task.duration}ms`);
        }
        
        if (task.errors.length > 0) {
          console.log(`     错误: ${task.errors.length} 个`);
        }
      });
    }

    // 演示实例清理
    console.log('\n🧹 演示实例清理:');
    try {
      const cleanupResult = await this.templateManager.cleanupOutdatedInstances({
        status: 'inactive'
      });

      console.log('✅ 实例清理完成:');
      console.log(`  总处理: ${cleanupResult.totalProcessed}`);
      console.log(`  清理: ${cleanupResult.successCount}`);
      console.log(`  失败: ${cleanupResult.failureCount}`);
    } catch (error) {
      console.error('❌ 实例清理失败:', error);
    }
  }

  /**
   * 显示最终统计
   */
  private showFinalStats(): void {
    console.log('\n📈 最终统计信息');
    console.log('-'.repeat(40));

    // 实例统计
    const instanceStats = this.templateManager.getInstanceStats();
    console.log('实例统计:');
    console.log(`  总数: ${instanceStats.total}`);
    console.log(`  活跃: ${instanceStats.byStatus.active || 0}`);
    console.log(`  过期: ${instanceStats.byStatus.outdated || 0}`);
    console.log(`  错误: ${instanceStats.byStatus.error || 0}`);

    // 冲突统计
    const conflicts = this.templateManager.getParameterConflicts();
    console.log('\n冲突统计:');
    console.log(`  总数: ${conflicts.length}`);
    
    const conflictsBySeverity = conflicts.reduce((acc, conflict) => {
      acc[conflict.severity] = (acc[conflict.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    Object.entries(conflictsBySeverity).forEach(([severity, count]) => {
      console.log(`  ${severity}: ${count}`);
    });

    // 解决策略统计
    const resolutionStats = conflictResolver.getResolutionStats();
    console.log('\n解决策略统计:');
    console.log(`  总解决: ${resolutionStats.totalResolved}`);
    console.log(`  自动解决: ${resolutionStats.autoResolved}`);
    console.log(`  用户解决: ${resolutionStats.userResolved}`);
    
    if (Object.keys(resolutionStats.byStrategy).length > 0) {
      console.log('  按策略分布:');
      Object.entries(resolutionStats.byStrategy).forEach(([strategy, count]) => {
        console.log(`    ${strategy}: ${count}`);
      });
    }

    // 变更历史统计
    const changeHistory = this.templateManager.getParameterChangeHistory(undefined, 100);
    console.log('\n变更历史统计:');
    console.log(`  总变更: ${changeHistory.length}`);
    
    const changesByType = changeHistory.reduce((acc, change) => {
      acc[change.changeType] = (acc[change.changeType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    Object.entries(changesByType).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
  }

  /**
   * 获取变更图标
   */
  private getChangeIcon(changeType: string): string {
    const icons: Record<string, string> = {
      added: '➕',
      removed: '➖',
      modified: '✏️',
      renamed: '🔄',
      type_changed: '🔀'
    };
    return icons[changeType] || '📝';
  }

  /**
   * 获取任务状态文本
   */
  private getTaskStatusText(status: string): string {
    const texts: Record<string, string> = {
      pending: '⏳ 等待中',
      running: '🔄 运行中',
      completed: '✅ 已完成',
      failed: '❌ 失败',
      cancelled: '🚫 已取消'
    };
    return texts[status] || status;
  }

  /**
   * 清理演示数据
   */
  clearDemoData(): void {
    console.log('🧹 清理参数同步演示数据...');
    
    // 清理事件历史
    instanceTracker.cleanupEvents();
    
    // 清理任务历史
    batchUpdateManager.cleanupTaskHistory();
    
    console.log('✅ 演示数据清理完成');
  }

  /**
   * 获取功能清单
   */
  getFeatureList(): string[] {
    return [
      '✅ 参数变更检测',
      '✅ 冲突自动识别',
      '✅ 智能解决策略',
      '✅ 批量同步操作',
      '✅ 冲突解决对话框',
      '✅ 任务进度追踪',
      '✅ 实例状态管理',
      '✅ 变更历史记录',
      '✅ 统计信息分析',
      '✅ 自动清理功能'
    ];
  }
}

// 导出演示实例
export const parameterSyncDemo = new ParameterSyncDemo();

// 如果在浏览器环境中，将演示添加到全局对象
if (typeof window !== 'undefined') {
  (window as any).parameterSyncDemo = parameterSyncDemo;
  console.log('💡 参数同步演示已添加到全局对象，可以通过 window.parameterSyncDemo 访问');
  console.log('💡 运行 window.parameterSyncDemo.runCompleteDemo() 开始演示');
  
  // 显示功能清单
  console.log('\n🎉 参数同步功能清单:');
  parameterSyncDemo.getFeatureList().forEach(feature => console.log(feature));
}
