<template>
  <div class="simple-template-selector">
    <div class="selector-input">
      <input
        v-model="selectedPath"
        type="text"
        :placeholder="placeholder"
        class="path-input"
        @input="onInputChange"
      />
      <button @click="toggleDropdown" class="dropdown-btn">
        {{ showDropdown ? '▲' : '▼' }}
      </button>
    </div>

    <div v-if="showDropdown" class="dropdown-list">
      <div v-if="availableTemplates.length === 0" class="no-templates">
        暂无可用模板
      </div>
      <div
        v-for="template in availableTemplates"
        :key="template.id"
        @click="selectTemplate(template)"
        class="template-option"
      >
        <span class="template-icon">{{ getTemplateIcon(template.type) }}</span>
        <div class="template-info">
          <div class="template-name">{{ template.name }}</div>
          <div class="template-path">{{ template.path }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { TemplateManager, TemplateType, type Template } from '../services/TemplateManager';

// Props
interface Props {
  modelValue?: string;
  placeholder?: string;
  allowedTypes?: TemplateType[];
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择模板...',
  allowedTypes: () => Object.values(TemplateType),
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'template-selected': [template: Template];
}>();

// 组件状态
const showDropdown = ref(false);
const selectedPath = ref(props.modelValue || '');
const templateManager = TemplateManager.getInstance();

// 计算属性
const availableTemplates = computed(() => {
  let templates = templateManager.registry.getAll();
  
  // 按允许的类型过滤
  if (props.allowedTypes.length > 0) {
    templates = templates.filter(t => props.allowedTypes.includes(t.type));
  }
  
  return templates;
});

// 方法
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
};

const onInputChange = () => {
  emit('update:modelValue', selectedPath.value);
};

const selectTemplate = (template: Template) => {
  console.log('选择模板:', template.name, template.path);
  selectedPath.value = template.path;
  showDropdown.value = false;

  emit('update:modelValue', template.path);
  emit('template-selected', template);

  // 显示用户反馈
  console.log(`✅ 已选择模板: ${template.name}`);
};

const getTemplateIcon = (type: TemplateType): string => {
  const icons: Record<TemplateType, string> = {
    [TemplateType.MESH]: '🏗️',
    [TemplateType.STYLE]: '🎨',
    [TemplateType.CAMERA]: '📷',
    [TemplateType.ACTION]: '⚡',
    [TemplateType.LABEL]: '🏷️',
    [TemplateType.POSITION]: '📍',
    [TemplateType.INTERACTION]: '🎭',
  };
  return icons[type] || '📄';
};

// 监听器
watch(() => props.modelValue, (newValue) => {
  selectedPath.value = newValue || '';
});

// 生命周期
onMounted(() => {
  console.log('SimpleTemplateSelector mounted, 可用模板数量:', availableTemplates.value.length);
  console.log('允许的类型:', props.allowedTypes);
  console.log('可用模板列表:', availableTemplates.value.map(t => `${t.name} (${t.type})`));
});
</script>

<style scoped>
.simple-template-selector {
  position: relative;
  width: 100%;
}

.selector-input {
  display: flex;
  border: 1px solid #4a5568;
  border-radius: 6px;
  overflow: hidden;
  background: #1a202c;
  transition: border-color 0.2s ease;
}

.selector-input:focus-within {
  border-color: #63b3ed;
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.path-input {
  flex: 1;
  padding: 0.75rem;
  border: none;
  outline: none;
  font-size: 0.875rem;
  font-family: monospace;
  background: transparent;
  color: #e2e8f0;
}

.path-input::placeholder {
  color: #a0aec0;
}

.dropdown-btn {
  padding: 0.75rem;
  border: none;
  background: #2d3748;
  cursor: pointer;
  border-left: 1px solid #4a5568;
  font-size: 0.75rem;
  color: #e2e8f0;
  transition: all 0.2s ease;
}

.dropdown-btn:hover {
  background: #4a5568;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #1a202c;
  border: 1px solid #4a5568;
  border-radius: 6px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  max-height: 250px;
  overflow-y: auto;
  margin-top: 0.25rem;
}

.no-templates {
  padding: 1.5rem;
  text-align: center;
  color: #a0aec0;
  font-style: italic;
}

.template-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  border-bottom: 1px solid #2d3748;
  transition: all 0.2s ease;
  color: #e2e8f0;
}

.template-option:hover {
  background: #2d3748;
  transform: translateX(2px);
}

.template-option:last-child {
  border-bottom: none;
}

.template-icon {
  margin-right: 1rem;
  font-size: 1.25rem;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  color: #f7fafc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-path {
  font-family: monospace;
  font-size: 0.75rem;
  color: #a0aec0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 滚动条样式 */
.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #2d3748;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #718096;
}
</style>
