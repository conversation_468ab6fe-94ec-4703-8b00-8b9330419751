<template>
  <div class="notification-container">
    <TransitionGroup name="notification" tag="div" class="notifications-list">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="[
          'notification',
          `notification-${notification.type}`,
          { 'notification-persistent': notification.persistent }
        ]"
      >
        <!-- 通知图标 -->
        <div class="notification-icon">
          {{ getNotificationIcon(notification.type) }}
        </div>
        
        <!-- 通知内容 -->
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div v-if="notification.message" class="notification-message">
            {{ notification.message }}
          </div>
          
          <!-- 通知操作 -->
          <div v-if="notification.actions && notification.actions.length > 0" class="notification-actions">
            <button
              v-for="action in notification.actions"
              :key="action.label"
              @click="handleAction(notification.id, action)"
              :class="[
                'action-btn',
                `action-${action.style || 'primary'}`
              ]"
            >
              {{ action.label }}
            </button>
          </div>
        </div>
        
        <!-- 关闭按钮 -->
        <button
          @click="removeNotification(notification.id)"
          class="notification-close"
          title="关闭通知"
        >
          ✕
        </button>
        
        <!-- 进度条（非持久通知） -->
        <div
          v-if="!notification.persistent && notification.duration"
          class="notification-progress"
          :style="{ animationDuration: `${notification.duration}ms` }"
        ></div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { notificationService, NotificationType, NotificationAction } from '../services/NotificationService';

// 计算属性
const notifications = computed(() => notificationService.getNotifications());

// 方法
const getNotificationIcon = (type: NotificationType): string => {
  const icons: Record<NotificationType, string> = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  };
  return icons[type];
};

const removeNotification = (id: string) => {
  notificationService.remove(id);
};

const handleAction = (notificationId: string, action: NotificationAction) => {
  action.action();
  removeNotification(notificationId);
};
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  pointer-events: none;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 400px;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  position: relative;
  overflow: hidden;
  min-width: 320px;
}

/* 通知类型样式 */
.notification-success {
  border-left: 4px solid #38a169;
  background: linear-gradient(135deg, #2d3748 0%, #2d4a3a 100%);
}

.notification-error {
  border-left: 4px solid #e53e3e;
  background: linear-gradient(135deg, #2d3748 0%, #4a2d2d 100%);
}

.notification-warning {
  border-left: 4px solid #ed8936;
  background: linear-gradient(135deg, #2d3748 0%, #4a3d2d 100%);
}

.notification-info {
  border-left: 4px solid #3182ce;
  background: linear-gradient(135deg, #2d3748 0%, #2d3d4a 100%);
}

.notification-persistent {
  border-style: dashed;
}

/* 通知图标 */
.notification-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

/* 通知内容 */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  color: #f7fafc;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.notification-message {
  color: #a0aec0;
  font-size: 0.8125rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  word-wrap: break-word;
}

/* 通知操作 */
.notification-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.action-btn {
  padding: 0.375rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-primary {
  background: #3182ce;
  color: white;
}

.action-primary:hover {
  background: #2b6cb0;
}

.action-secondary {
  background: #4a5568;
  color: #e2e8f0;
}

.action-secondary:hover {
  background: #718096;
}

.action-danger {
  background: #e53e3e;
  color: white;
}

.action-danger:hover {
  background: #c53030;
}

/* 关闭按钮 */
.notification-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.notification-close:hover {
  background: rgba(160, 174, 192, 0.1);
  color: #f7fafc;
}

/* 进度条 */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, #38a169, #68d391);
  animation: progress-countdown linear forwards;
}

.notification-success .notification-progress {
  background: linear-gradient(90deg, #38a169, #68d391);
}

.notification-error .notification-progress {
  background: linear-gradient(90deg, #e53e3e, #fc8181);
}

.notification-warning .notification-progress {
  background: linear-gradient(90deg, #ed8936, #f6ad55);
}

.notification-info .notification-progress {
  background: linear-gradient(90deg, #3182ce, #63b3ed);
}

@keyframes progress-countdown {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* 过渡动画 */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-container {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
  }
  
  .notifications-list {
    max-width: none;
  }
  
  .notification {
    min-width: auto;
    padding: 0.75rem;
  }
  
  .notification-title {
    font-size: 0.8125rem;
  }
  
  .notification-message {
    font-size: 0.75rem;
  }
  
  .notification-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .notification {
    border-width: 2px;
  }
  
  .notification-title {
    font-weight: 700;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .notification-enter-active,
  .notification-leave-active,
  .notification-move {
    transition: none;
  }
  
  .notification-progress {
    animation: none;
    width: 0;
  }
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
  .notification {
    background: #1a202c;
    border-color: #2d3748;
  }
  
  .notification-success {
    background: linear-gradient(135deg, #1a202c 0%, #1a2e20 100%);
  }
  
  .notification-error {
    background: linear-gradient(135deg, #1a202c 0%, #2e1a1a 100%);
  }
  
  .notification-warning {
    background: linear-gradient(135deg, #1a202c 0%, #2e251a 100%);
  }
  
  .notification-info {
    background: linear-gradient(135deg, #1a202c 0%, #1a252e 100%);
  }
}

/* 焦点管理 */
.notification:focus-within {
  outline: 2px solid #63b3ed;
  outline-offset: 2px;
}

.action-btn:focus,
.notification-close:focus {
  outline: 2px solid #63b3ed;
  outline-offset: 1px;
}

/* 打印样式 */
@media print {
  .notification-container {
    display: none;
  }
}
</style>
