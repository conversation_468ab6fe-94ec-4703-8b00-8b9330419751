/**
 * NodeSelector.ts
 * 负责从画布选择节点的服务
 */

import { Cell } from '@antv/x6';

/**
 * 节点选择器类
 */
export class NodeSelector {
  private static instance: NodeSelector;
  private selectedNode: Cell | null = null;
  private selectionCallbacks: ((node: Cell | null) => void)[] = [];

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): NodeSelector {
    if (!NodeSelector.instance) {
      NodeSelector.instance = new NodeSelector();
    }
    return NodeSelector.instance;
  }

  /**
   * 设置选中的节点
   */
  setSelectedNode(node: Cell | null): void {
    this.selectedNode = node;
    this.notifySelectionChange();
  }

  /**
   * 获取当前选中的节点
   */
  getSelectedNode(): Cell | null {
    return this.selectedNode;
  }

  /**
   * 监听节点选择变化
   */
  onSelectionChange(callback: (node: Cell | null) => void): void {
    this.selectionCallbacks.push(callback);
  }

  /**
   * 移除选择变化监听器
   */
  removeSelectionListener(callback: (node: Cell | null) => void): void {
    const index = this.selectionCallbacks.indexOf(callback);
    if (index > -1) {
      this.selectionCallbacks.splice(index, 1);
    }
  }

  /**
   * 通知选择变化
   */
  private notifySelectionChange(): void {
    this.selectionCallbacks.forEach(callback => {
      try {
        callback(this.selectedNode);
      } catch (error) {
        console.error('节点选择回调执行失败:', error);
      }
    });
  }

  /**
   * 创建模拟节点（用于演示）
   */
  createMockNode(type: 'button' | 'label' | 'shape' = 'button'): Cell {
    const mockNodes = {
      button: {
        id: `button-${Date.now()}`,
        shape: 'rect',
        data: {
          label: '示例按钮',
          backgroundColor: '#3182ce',
          borderColor: '#2b6cb0',
          textColor: '#ffffff',
          fontSize: 14,
          padding: 8,
          borderRadius: 6,
          onClick: 'handleButtonClick'
        },
        attrs: {
          body: {
            fill: '#3182ce',
            stroke: '#2b6cb0',
            strokeWidth: 2,
            rx: 6,
            ry: 6
          },
          text: {
            fill: '#ffffff',
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        size: { width: 120, height: 40 },
        position: { x: 100, y: 100 }
      },
      label: {
        id: `label-${Date.now()}`,
        shape: 'text',
        data: {
          text: '示例标签',
          fontSize: 16,
          fontWeight: 'normal',
          textColor: '#2d3748',
          backgroundColor: 'transparent',
          textAlign: 'center'
        },
        attrs: {
          text: {
            fill: '#2d3748',
            fontSize: 16,
            fontWeight: 'normal',
            textAnchor: 'middle'
          }
        },
        size: { width: 100, height: 30 },
        position: { x: 150, y: 200 }
      },
      shape: {
        id: `shape-${Date.now()}`,
        shape: 'circle',
        data: {
          fillColor: '#38a169',
          strokeColor: '#2f855a',
          strokeWidth: 3,
          opacity: 0.8
        },
        attrs: {
          body: {
            fill: '#38a169',
            stroke: '#2f855a',
            strokeWidth: 3,
            opacity: 0.8
          }
        },
        size: { width: 60, height: 60 },
        position: { x: 200, y: 150 }
      }
    };

    // 创建一个模拟的Cell对象
    const nodeData = mockNodes[type];
    
    // 这里我们创建一个简化的Cell对象用于演示
    const mockCell = {
      ...nodeData,
      getData: () => nodeData.data,
      getAttrs: () => nodeData.attrs,
      getSize: () => nodeData.size,
      getPosition: () => nodeData.position,
      setData: (data: any) => { nodeData.data = { ...nodeData.data, ...data }; },
      setAttrs: (attrs: any) => { nodeData.attrs = { ...nodeData.attrs, ...attrs }; },
      setSize: (size: any) => { nodeData.size = size; },
      setPosition: (position: any) => { nodeData.position = position; }
    } as Cell;

    return mockCell;
  }

  /**
   * 模拟从画布选择节点
   */
  async selectFromCanvas(): Promise<Cell | null> {
    return new Promise((resolve) => {
      // 模拟用户选择过程
      console.log('🎯 模拟从画布选择节点...');
      
      // 随机选择一个节点类型
      const nodeTypes: ('button' | 'label' | 'shape')[] = ['button', 'label', 'shape'];
      const randomType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];
      
      setTimeout(() => {
        const mockNode = this.createMockNode(randomType);
        this.setSelectedNode(mockNode);
        console.log(`✅ 已选择 ${randomType} 节点:`, mockNode);
        resolve(mockNode);
      }, 800); // 模拟选择延迟
    });
  }

  /**
   * 清除选择
   */
  clearSelection(): void {
    this.setSelectedNode(null);
  }

  /**
   * 获取节点的可视化信息
   */
  getNodeDisplayInfo(node: Cell): {
    name: string;
    type: string;
    icon: string;
    description: string;
  } {
    if (!node) {
      return {
        name: '未知节点',
        type: 'unknown',
        icon: '❓',
        description: '无法获取节点信息'
      };
    }

    const data = node.getData() || {};
    const shape = node.shape || 'unknown';

    // 根据节点类型和数据确定显示信息
    let name = data.label || data.text || data.name || `节点-${node.id}`;
    let type = shape;
    let icon = '📄';
    let description = '';

    // 根据形状确定图标
    if (shape.includes('rect')) {
      icon = '⬜';
      type = '矩形';
    } else if (shape.includes('circle')) {
      icon = '⭕';
      type = '圆形';
    } else if (shape.includes('ellipse')) {
      icon = '🔵';
      type = '椭圆';
    } else if (shape.includes('text')) {
      icon = '📝';
      type = '文本';
    }

    // 生成描述
    const size = node.getSize();
    const position = node.getPosition();
    description = `${type} | ${size?.width || 0}×${size?.height || 0} | (${position?.x || 0}, ${position?.y || 0})`;

    return { name, type, icon, description };
  }

  /**
   * 验证节点是否可以转换为模板
   */
  validateNodeForTemplate(node: Cell): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!node) {
      errors.push('节点不存在');
      return { valid: false, errors, warnings };
    }

    // 检查节点数据
    const data = node.getData();
    if (!data || Object.keys(data).length === 0) {
      warnings.push('节点没有数据，模板可能为空');
    }

    // 检查节点属性
    const attrs = node.getAttrs();
    if (!attrs || Object.keys(attrs).length === 0) {
      warnings.push('节点没有样式属性');
    }

    // 检查节点大小
    const size = node.getSize();
    if (!size || size.width <= 0 || size.height <= 0) {
      warnings.push('节点大小无效');
    }

    // 检查是否包含函数（不支持序列化）
    const hasFunction = this.containsFunction(data) || this.containsFunction(attrs);
    if (hasFunction) {
      warnings.push('节点包含函数属性，可能影响模板的保存和加载');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 检查对象是否包含函数
   */
  private containsFunction(obj: any): boolean {
    if (typeof obj === 'function') {
      return true;
    }

    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (this.containsFunction(obj[key])) {
          return true;
        }
      }
    }

    return false;
  }
}

// 导出单例实例
export const nodeSelector = NodeSelector.getInstance();
