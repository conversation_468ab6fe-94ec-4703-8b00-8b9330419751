/**
 * 配置解析器 - 将config.js配置转换为节点数据结构
 */

import { ConnectionInference } from "./ConnectionInference";

// 解析后的场景数据接口
export interface ParsedSceneData {
  sceneId: string;
  sceneName: string;
  sceneConfig: SceneConfigNodeData;
  eventNodes: EventNodeData[];
  actionNodes: ActionNodeData[];
  lifecycleNodes: LifecycleNodeData[];
  staticLabelNodes: StaticLabelNodeData[];
  referenceNodes: ReferenceNodeData[];
  dataNodes: DataNodeData[];
  connections: ConnectionData[];
}

// 节点数据接口
export interface BaseNodeData {
  id: string;
  nodeType: string;
  displayName: string;
  x?: number;
  y?: number;
  [key: string]: any;
}

export interface SceneConfigNodeData extends BaseNodeData {
  nodeType: "scene-config";
  sceneId: string;
  sceneName: string;
  models: string[];
  environment: string;
  scene: string;
}

export interface EventNodeData extends BaseNodeData {
  nodeType: "event";
  actionType: string;
  meshNames: string[] | { $ref: string };
  description?: string;
  meshNamesIsRef?: boolean; // 是否为引用格式
  meshNamesRef?: string; // 引用路径
}

export interface ActionNodeData extends BaseNodeData {
  nodeType: "action-highlight" | "action-callback";
  callback?: string;
  parameters?: any;
  highlight?: {
    color: number[];
    duration: number;
    intensity?: number;
  };
}

export interface LifecycleNodeData extends BaseNodeData {
  nodeType: "lifecycle";
  lifecycleType: "onActivated" | "onDeactivated" | "onModelLoaded" | "onInit";
  trigger: "immediate" | "delayed";
  delay?: number;
}

export interface StaticLabelNodeData extends BaseNodeData {
  nodeType: "static-label";
  meshNames: string[];
  customNames: Record<string, string>;
  position?: number[];
  target?: number[];
  style?: any;
  clickConfig?: any;
}

export interface ReferenceNodeData extends BaseNodeData {
  nodeType: "reference";
  refPath: string;
  refType: "camera" | "environment" | "style" | "label" | "callback";
  extendParams?: Record<string, any>;
}

export interface DataNodeData extends BaseNodeData {
  nodeType: "data-source" | "data-transform" | "data-mapping" | "data-consumer";
  dataType?: "polling" | "websocket";
  transformType?: "map" | "range";
  config?: any;
}

export interface ConnectionData {
  id: string;
  source: string;
  target: string;
  sourcePort?: string;
  targetPort?: string;
}

/**
 * 配置解析器类
 */
export class ConfigParser {
  private nodeIdCounter = 0;
  private connectionInference = new ConnectionInference();

  /**
   * 生成唯一节点ID
   */
  private generateNodeId(prefix: string): string {
    return `${prefix}-${++this.nodeIdCounter}`;
  }

  /**
   * 解析完整场景配置
   */
  parseScene(sceneId: string, sceneConfig: any): ParsedSceneData {
    const parsedData: ParsedSceneData = {
      sceneId,
      sceneName: sceneConfig.name || `场景 ${sceneId}`,
      sceneConfig: this.parseSceneConfig(sceneId, sceneConfig),
      eventNodes: [],
      actionNodes: [],
      lifecycleNodes: [],
      staticLabelNodes: [],
      referenceNodes: [],
      dataNodes: [],
      connections: [],
    };

    // 解析各种配置
    if (sceneConfig.actions) {
      const { eventNodes, actionNodes, connections } = this.parseActions(sceneConfig.actions);
      parsedData.eventNodes.push(...eventNodes);
      parsedData.actionNodes.push(...actionNodes);
      parsedData.connections.push(...connections);
    }

    if (sceneConfig.lifecycle) {
      const { lifecycleNodes, actionNodes, dataNodes, connections } = this.parseLifecycle(sceneConfig.lifecycle);
      parsedData.lifecycleNodes.push(...lifecycleNodes);
      parsedData.actionNodes.push(...actionNodes);
      parsedData.dataNodes.push(...dataNodes);
      parsedData.connections.push(...connections);
    }

    if (sceneConfig.staticLabels) {
      const { staticLabelNodes, eventNodes, actionNodes, connections } = this.parseStaticLabels(sceneConfig.staticLabels);
      parsedData.staticLabelNodes.push(...staticLabelNodes);
      parsedData.eventNodes.push(...eventNodes);
      parsedData.actionNodes.push(...actionNodes);
      parsedData.connections.push(...connections);
    }

    // 解析模板引用
    this.extractReferences(sceneConfig, parsedData);

    // 使用连接推断算法优化连接
    const inferredConnections = this.connectionInference.inferAllConnections(parsedData);

    // 合并解析出的连接和推断出的连接，去重
    const allConnections = [...parsedData.connections, ...inferredConnections];
    const uniqueConnections = this.deduplicateConnections(allConnections);
    parsedData.connections = uniqueConnections;

    return parsedData;
  }

  /**
   * 解析场景配置节点
   */
  private parseSceneConfig(sceneId: string, sceneConfig: any): SceneConfigNodeData {
    return {
      id: this.generateNodeId("scene-config"),
      nodeType: "scene-config",
      displayName: `场景: ${sceneConfig.name || sceneId}`,
      sceneId,
      sceneName: sceneConfig.name || `场景 ${sceneId}`,
      models: sceneConfig.models || [],
      environment: this.extractRefValue(sceneConfig.envTemplate) || "default",
      scene: sceneConfig.scene || "DefaultScene",
      x: 50,
      y: 50,
    };
  }

  /**
   * 解析Actions配置
   */
  parseActions(actions: any[]): {
    eventNodes: EventNodeData[];
    actionNodes: ActionNodeData[];
    connections: ConnectionData[];
  } {
    const eventNodes: EventNodeData[] = [];
    const actionNodes: ActionNodeData[] = [];
    const connections: ConnectionData[] = [];

    actions.forEach((action, index) => {
      // 创建事件节点
      const eventNode: EventNodeData = {
        id: this.generateNodeId("event"),
        nodeType: "event",
        displayName: this.getEventDisplayName(action.actionType),
        actionType: action.actionType,
        meshNames: action.meshNames || { $ref: "templates.1.meshes.buildingLevels" },
        description: action.config?.description,
        x: 200,
        y: 150 + index * 100,
        // 添加meshNames的处理逻辑
        meshNamesIsRef: this.isMeshNamesReference(action.meshNames),
        meshNamesRef: this.extractMeshNamesRef(action.meshNames),
      };
      eventNodes.push(eventNode);

      let lastNodeId = eventNode.id;
      let currentX = 400;

      // 解析动作配置
      if (action.config) {
        const { nodes, conns } = this.parseActionConfig(action.config, currentX, eventNode.y || 150);
        actionNodes.push(...nodes);
        connections.push(...conns);

        // 连接事件节点到第一个动作节点
        if (nodes.length > 0) {
          connections.push({
            id: this.generateNodeId("connection"),
            source: lastNodeId,
            target: nodes[0].id,
            sourcePort: "out-exec",
            targetPort: "in-exec",
          });
        }
      }
    });

    return { eventNodes, actionNodes, connections };
  }

  /**
   * 解析动作配置
   */
  private parseActionConfig(config: any, startX: number, y: number): {
    nodes: ActionNodeData[];
    conns: ConnectionData[];
  } {
    const nodes: ActionNodeData[] = [];
    const conns: ConnectionData[] = [];
    let currentX = startX;

    // 解析高亮配置
    if (config.highlight) {
      const highlightNode: ActionNodeData = {
        id: this.generateNodeId("action-highlight"),
        nodeType: "action-highlight",
        displayName: "高亮",
        highlight: config.highlight,
        x: currentX,
        y,
      };
      nodes.push(highlightNode);
      currentX += 200;
    }

    // 解析回调配置
    if (config.callback) {
      const callbackNode: ActionNodeData = {
        id: this.generateNodeId("action-callback"),
        nodeType: "action-callback",
        displayName: this.getCallbackDisplayName(config.callback),
        callback: config.callback,
        parameters: config.parameters,
        x: currentX,
        y,
      };
      nodes.push(callbackNode);
      currentX += 200;
    }

    // 解析回调数组
    if (config.callbacks) {
      if (Array.isArray(config.callbacks)) {
        config.callbacks.forEach((callback: any) => {
          const callbackNode: ActionNodeData = {
            id: this.generateNodeId("action-callback"),
            nodeType: "action-callback",
            displayName: this.getCallbackDisplayName(callback),
            callback: callback,
            x: currentX,
            y,
          };
          nodes.push(callbackNode);
          currentX += 200;
        });
      } else if (config.callbacks.$ref) {
        // 处理引用回调
        const refNode: ReferenceNodeData = {
          id: this.generateNodeId("reference"),
          nodeType: "reference",
          displayName: `引用: ${config.callbacks.$ref}`,
          refPath: config.callbacks.$ref,
          refType: "callback",
          x: currentX,
          y,
        };
        // 这里需要将引用节点添加到适当的位置
        currentX += 200;
      }
    }

    // 创建节点间的连接
    for (let i = 0; i < nodes.length - 1; i++) {
      conns.push({
        id: this.generateNodeId("connection"),
        source: nodes[i].id,
        target: nodes[i + 1].id,
        sourcePort: "out-exec",
        targetPort: "in-exec",
      });
    }

    return { nodes, conns };
  }

  /**
   * 获取事件显示名称
   */
  private getEventDisplayName(actionType: string): string {
    const nameMap: Record<string, string> = {
      doubleClick: "双击事件",
      click: "单击事件",
      rightDoubleClick: "右键双击事件",
      hover: "悬停事件",
      hotkey: "按键事件",
    };
    return nameMap[actionType] || `${actionType}事件`;
  }

  /**
   * 获取回调显示名称
   */
  private getCallbackDisplayName(callback: string): string {
    const nameMap: Record<string, string> = {
      "CameraService.focusToDevice": "相机聚焦",
      "CameraService.moveCamera": "相机移动",
      "AnimationService.playMeshAnimation": "播放动画",
      "UIService.showMessage": "显示消息",
      "ConfigSceneService.switchToScene": "切换场景",
    };
    return nameMap[callback] || callback;
  }

  /**
   * 提取引用值
   */
  private extractRefValue(refObj: any): string | null {
    if (!refObj) return null;
    if (typeof refObj === "string") return refObj;
    if (refObj.$ref) {
      const parts = refObj.$ref.split(".");
      return parts[parts.length - 1];
    }
    return null;
  }

  /**
   * 提取配置中的所有引用
   */
  private extractReferences(config: any, parsedData: ParsedSceneData): void {
    // 递归遍历配置对象，查找所有$ref引用
    this.traverseForReferences(config, parsedData, "");
  }

  /**
   * 递归遍历查找引用
   */
  private traverseForReferences(obj: any, parsedData: ParsedSceneData, path: string): void {
    if (!obj || typeof obj !== "object") return;

    if (obj.$ref) {
      const refNode: ReferenceNodeData = {
        id: this.generateNodeId("reference"),
        nodeType: "reference",
        displayName: `引用: ${obj.$ref}`,
        refPath: obj.$ref,
        refType: this.inferRefType(obj.$ref),
        extendParams: obj.$extend,
      };
      parsedData.referenceNodes.push(refNode);
    }

    // 递归遍历子对象
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && key !== "$ref" && key !== "$extend") {
        this.traverseForReferences(obj[key], parsedData, `${path}.${key}`);
      }
    }
  }

  /**
   * 解析生命周期配置
   */
  parseLifecycle(lifecycle: any): {
    lifecycleNodes: LifecycleNodeData[];
    actionNodes: ActionNodeData[];
    dataNodes: DataNodeData[];
    connections: ConnectionData[];
  } {
    const lifecycleNodes: LifecycleNodeData[] = [];
    const actionNodes: ActionNodeData[] = [];
    const dataNodes: DataNodeData[] = [];
    const connections: ConnectionData[] = [];

    let yOffset = 300;

    // 解析各种生命周期事件
    Object.entries(lifecycle).forEach(([eventType, events]: [string, any]) => {
      if (Array.isArray(events)) {
        events.forEach((event, index) => {
          // 创建生命周期节点
          const lifecycleNode: LifecycleNodeData = {
            id: this.generateNodeId("lifecycle"),
            nodeType: "lifecycle",
            displayName: this.getLifecycleDisplayName(eventType),
            lifecycleType: eventType as any,
            trigger: event.trigger || "immediate",
            delay: event.delay,
            x: 50,
            y: yOffset + index * 100,
          };
          lifecycleNodes.push(lifecycleNode);

          let lastNodeId = lifecycleNode.id;
          let currentX = 300;

          // 解析回调配置
          if (event.callback) {
            if (event.callback === "DeviceService.setupDataDrivenPolling") {
              // 创建数据驱动服务节点
              const dataServiceNode: DataNodeData = {
                id: this.generateNodeId("data-service"),
                nodeType: "data-consumer",
                displayName: "数据驱动服务",
                config: event.parameters,
                x: currentX,
                y: lifecycleNode.y,
              };
              dataNodes.push(dataServiceNode);

              // 连接生命周期节点到数据服务节点
              connections.push({
                id: this.generateNodeId("connection"),
                source: lastNodeId,
                target: dataServiceNode.id,
                sourcePort: "out-exec",
                targetPort: "in-exec",
              });
            } else {
              // 创建普通回调节点
              const callbackNode: ActionNodeData = {
                id: this.generateNodeId("action-callback"),
                nodeType: "action-callback",
                displayName: this.getCallbackDisplayName(event.callback),
                callback: event.callback,
                parameters: event.parameters,
                x: currentX,
                y: lifecycleNode.y,
              };
              actionNodes.push(callbackNode);

              // 连接生命周期节点到回调节点
              connections.push({
                id: this.generateNodeId("connection"),
                source: lastNodeId,
                target: callbackNode.id,
                sourcePort: "out-exec",
                targetPort: "in-exec",
              });
            }
          }
        });
      }
      yOffset += 150;
    });

    return { lifecycleNodes, actionNodes, dataNodes, connections };
  }

  /**
   * 解析静态标签配置
   */
  parseStaticLabels(staticLabels: any[]): {
    staticLabelNodes: StaticLabelNodeData[];
    eventNodes: EventNodeData[];
    actionNodes: ActionNodeData[];
    connections: ConnectionData[];
  } {
    const staticLabelNodes: StaticLabelNodeData[] = [];
    const eventNodes: EventNodeData[] = [];
    const actionNodes: ActionNodeData[] = [];
    const connections: ConnectionData[] = [];

    staticLabels.forEach((label, index) => {
      const yPos = 500 + index * 120;

      // 创建静态标签节点
      const staticLabelNode: StaticLabelNodeData = {
        id: this.generateNodeId("static-label"),
        nodeType: "static-label",
        displayName: this.getStaticLabelDisplayName(label),
        meshNames: label.meshNames || label.$extend?.meshNames || [],
        customNames: label.customNames || label.$extend?.customNames || {},
        position: label.position || label.$extend?.position,
        target: label.target || label.$extend?.target,
        style: label.style,
        clickConfig: label.click || label.$extend?.click,
        x: 50,
        y: yPos,
      };
      staticLabelNodes.push(staticLabelNode);

      // 如果有点击配置，创建点击事件和动作节点
      const clickConfig = label.click || label.$extend?.click;
      if (clickConfig && clickConfig.enabled) {
        // 创建点击事件节点
        const clickEventNode: EventNodeData = {
          id: this.generateNodeId("event"),
          nodeType: "event",
          displayName: "点击事件",
          actionType: "click",
          meshNames: staticLabelNode.meshNames,
          x: 300,
          y: yPos,
        };
        eventNodes.push(clickEventNode);

        // 连接静态标签到点击事件
        connections.push({
          id: this.generateNodeId("connection"),
          source: staticLabelNode.id,
          target: clickEventNode.id,
          sourcePort: "out-click",
          targetPort: "in-trigger",
        });

        let lastNodeId = clickEventNode.id;
        let currentX = 550;

        // 创建高亮动作节点
        if (clickConfig.highlight) {
          const highlightNode: ActionNodeData = {
            id: this.generateNodeId("action-highlight"),
            nodeType: "action-highlight",
            displayName: "高亮",
            highlight: clickConfig.highlight,
            x: currentX,
            y: yPos,
          };
          actionNodes.push(highlightNode);

          connections.push({
            id: this.generateNodeId("connection"),
            source: lastNodeId,
            target: highlightNode.id,
            sourcePort: "out-exec",
            targetPort: "in-exec",
          });

          lastNodeId = highlightNode.id;
          currentX += 200;
        }

        // 创建回调动作节点
        if (clickConfig.callback) {
          const callbackNode: ActionNodeData = {
            id: this.generateNodeId("action-callback"),
            nodeType: "action-callback",
            displayName: this.getCallbackDisplayName(clickConfig.callback),
            callback: clickConfig.callback,
            parameters: clickConfig.parameters,
            x: currentX,
            y: yPos,
          };
          actionNodes.push(callbackNode);

          connections.push({
            id: this.generateNodeId("connection"),
            source: lastNodeId,
            target: callbackNode.id,
            sourcePort: "out-exec",
            targetPort: "in-exec",
          });
        }
      }
    });

    return { staticLabelNodes, eventNodes, actionNodes, connections };
  }

  /**
   * 获取生命周期显示名称
   */
  private getLifecycleDisplayName(eventType: string): string {
    const nameMap: Record<string, string> = {
      onActivated: "场景激活",
      onDeactivated: "场景退出",
      onModelLoaded: "模型加载完成",
      onInit: "场景初始化",
    };
    return nameMap[eventType] || eventType;
  }

  /**
   * 获取静态标签显示名称
   */
  private getStaticLabelDisplayName(label: any): string {
    const meshNames = label.meshNames || label.$extend?.meshNames || [];
    const customNames = label.customNames || label.$extend?.customNames || {};

    if (meshNames.length > 0) {
      const firstMesh = meshNames[0];
      const displayName = customNames[firstMesh] || firstMesh;
      return `标签: ${displayName}`;
    }

    if (label.$ref) {
      return `标签引用: ${label.$ref}`;
    }

    return "静态标签";
  }

  /**
   * 推断引用类型
   */
  private inferRefType(refPath: string): "camera" | "environment" | "style" | "label" | "callback" {
    if (refPath.includes("cameras")) return "camera";
    if (refPath.includes("environments")) return "environment";
    if (refPath.includes("styles")) return "style";
    if (refPath.includes("labels")) return "label";
    if (refPath.includes("callbacks")) return "callback";
    return "callback"; // 默认类型
  }

  /**
   * 去重连接数据
   */
  private deduplicateConnections(connections: ConnectionData[]): ConnectionData[] {
    const seen = new Set<string>();
    return connections.filter(conn => {
      const key = `${conn.source}-${conn.target}-${conn.sourcePort}-${conn.targetPort}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 判断meshNames是否为引用格式
   */
  private isMeshNamesReference(meshNames: any): boolean {
    return meshNames && typeof meshNames === 'object' && meshNames.$ref;
  }

  /**
   * 提取meshNames的引用路径
   */
  private extractMeshNamesRef(meshNames: any): string {
    if (this.isMeshNamesReference(meshNames)) {
      return meshNames.$ref;
    }
    return "";
  }
}
