<script setup lang="ts">
// 触发添加节点事件
function addNode(type: string, subtype?: string) {
  const event = new CustomEvent("add-node-request", {
    detail: { type, subtype },
  });
  document.dispatchEvent(event);
}
</script>

<template>
  <div class="node-library">
    <h3>节点库</h3>

    <div class="library-section">
      <h4>场景配置</h4>
      <div class="node-item" @click="addNode('scene-config')">
        <div class="node-icon scene-config-icon"></div>
        <div class="node-label">场景配置</div>
      </div>
    </div>

    <div class="library-section">
      <h4>事件节点</h4>
      <div class="node-item" @click="addNode('event', 'doubleClick')">
        <div class="node-icon event-icon"></div>
        <div class="node-label">双击事件</div>
      </div>
      <div class="node-item" @click="addNode('event', 'click')">
        <div class="node-icon event-icon"></div>
        <div class="node-label">单击事件</div>
      </div>
      <div class="node-item" @click="addNode('event', 'hover')">
        <div class="node-icon event-icon"></div>
        <div class="node-label">悬停事件</div>
      </div>
      <div class="node-item" @click="addNode('event', 'rightDoubleClick')">
        <div class="node-icon event-icon"></div>
        <div class="node-label">右键双击事件</div>
      </div>
    </div>

    <div class="library-section">
      <h4>动作节点</h4>
      <div class="node-item" @click="addNode('action-highlight')">
        <div class="node-icon action-icon"></div>
        <div class="node-label">高亮</div>
      </div>
      <div
        class="node-item"
        @click="addNode('action-callback', 'CameraService.focusToDevice')"
      >
        <div class="node-icon action-icon"></div>
        <div class="node-label">相机聚焦</div>
      </div>
      <div
        class="node-item"
        @click="addNode('action-callback', 'CameraService.moveCamera')"
      >
        <div class="node-icon action-icon"></div>
        <div class="node-label">相机移动</div>
      </div>
      <div
        class="node-item"
        @click="
          addNode('action-callback', 'AnimationService.playMeshAnimation')
        "
      >
        <div class="node-icon action-icon"></div>
        <div class="node-label">播放动画</div>
      </div>
      <div
        class="node-item"
        @click="addNode('action-callback', 'UIService.showMessage')"
      >
        <div class="node-icon action-icon"></div>
        <div class="node-label">显示消息</div>
      </div>
    </div>

    <div class="library-section">
      <h4>逻辑控制</h4>
      <div class="node-item" @click="addNode('logic-branch')">
        <div class="node-icon logic-icon"></div>
        <div class="node-label">条件分支</div>
      </div>
      <div class="node-item" @click="addNode('logic-sequence')">
        <div class="node-icon logic-icon"></div>
        <div class="node-label">顺序执行</div>
      </div>
    </div>

    <div class="library-section">
      <h4>数据节点</h4>
      <div class="node-item" @click="addNode('data-source', 'polling')">
        <div class="node-icon data-icon"></div>
        <div class="node-label">轮询数据源</div>
      </div>
      <div class="node-item" @click="addNode('data-source', 'websocket')">
        <div class="node-icon data-icon"></div>
        <div class="node-label">WebSocket数据源</div>
      </div>
      <div class="node-item" @click="addNode('data-transform', 'map')">
        <div class="node-icon transform-icon"></div>
        <div class="node-label">映射转换</div>
      </div>
      <div class="node-item" @click="addNode('data-transform', 'range')">
        <div class="node-icon transform-icon"></div>
        <div class="node-label">范围转换</div>
      </div>
      <div class="node-item" @click="addNode('data-mapping')">
        <div class="node-icon mapping-icon"></div>
        <div class="node-label">CSV映射文件</div>
      </div>
      <div class="node-item" @click="addNode('data-consumer', 'toggle')">
        <div class="node-icon consumer-icon"></div>
        <div class="node-label">切换可见性</div>
      </div>
      <div class="node-item" @click="addNode('data-consumer', 'animate')">
        <div class="node-icon consumer-icon"></div>
        <div class="node-label">动画控制</div>
      </div>
      <div class="node-item" @click="addNode('data-consumer', 'property')">
        <div class="node-icon consumer-icon"></div>
        <div class="node-label">属性控制</div>
      </div>
      <div class="node-item" @click="addNode('data-driven-service')">
        <div class="node-icon data-driven-service-icon"></div>
        <div class="node-label">数据驱动服务</div>
      </div>
    </div>

    <div class="library-section">
      <h4>生命周期</h4>
      <div class="node-item" @click="addNode('lifecycle', 'onActivated')">
        <div class="node-icon lifecycle-icon"></div>
        <div class="node-label">场景激活</div>
      </div>
      <div class="node-item" @click="addNode('lifecycle', 'onDeactivated')">
        <div class="node-icon lifecycle-icon"></div>
        <div class="node-label">场景退出</div>
      </div>
      <div class="node-item" @click="addNode('lifecycle', 'onModelLoaded')">
        <div class="node-icon lifecycle-icon"></div>
        <div class="node-label">模型加载完成</div>
      </div>
      <div class="node-item" @click="addNode('lifecycle', 'onInit')">
        <div class="node-icon lifecycle-icon"></div>
        <div class="node-label">场景初始化</div>
      </div>
    </div>

    <div class="library-section">
      <h4>模板和标签</h4>
      <div class="node-item" @click="addNode('reference')">
        <div class="node-icon reference-icon"></div>
        <div class="node-label">模板引用</div>
      </div>
      <div class="node-item" @click="addNode('static-label')">
        <div class="node-icon label-icon"></div>
        <div class="node-label">静态标签</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.node-library {
  height: 100%;
  padding: 1rem;
  background-color: #1e1e1e;
  color: #e0e0e0;
  overflow-y: auto;
}

h3 {
  margin-top: 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #444;
}

.library-section {
  margin-bottom: 1.5rem;
}

.library-section h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #aaa;
  font-size: 0.9rem;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: #2a2a2a;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.node-item:hover {
  background-color: #3a3a3a;
}

.node-icon {
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  border-radius: 4px;
}

.event-icon {
  background-color: #4a6b8a;
}

.action-icon {
  background-color: #4a8a6b;
}

.logic-icon {
  background-color: #8a4a6b;
}

.scene-config-icon {
  background-color: #1a3f5c;
}

.data-icon {
  background-color: #0066cc;
}

.transform-icon {
  background-color: #13c2c2;
}

.mapping-icon {
  background-color: #722ed1;
}

.consumer-icon {
  background-color: #fa8c16;
}

.lifecycle-icon {
  background-color: #722ed1;
}

.reference-icon {
  background-color: #eb2f96;
}

.label-icon {
  background-color: #13c2c2;
}

.data-driven-service-icon {
  background-color: #d4380d;
}

.node-label {
  font-size: 0.9rem;
}
</style>
