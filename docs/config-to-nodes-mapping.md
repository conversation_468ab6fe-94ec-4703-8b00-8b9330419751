# 配置解析到节点图映射策略

## 概述

本文档定义了从 config.js 配置结构到可视化节点图的完整映射策略，确保复杂的配置能够准确转换为直观的节点图表示。

## 核心映射原则

### 1. 结构化映射
- **场景级配置** → **场景配置节点**
- **交互配置** → **事件节点 + 动作节点链**
- **生命周期配置** → **生命周期节点 + 回调链**
- **数据驱动配置** → **数据流节点链**

### 2. 引用解析
- **$ref 引用** → **引用节点**
- **$extend 扩展** → **引用节点 + 参数覆盖**

### 3. 连接推断
- **配置层次关系** → **节点连接关系**
- **执行顺序** → **连线方向**

## 详细映射规则

### 1. 场景配置映射

#### 输入配置
```javascript
"scene-id": {
  name: "场景名称",
  models: ["model1.glb", "model2.glb"],
  scene: "DefaultScene",
  envTemplate: { $ref: "templates.1.environments.techStyle" },
  camera: { $ref: "templates.1.cameras.building" }
}
```

#### 映射结果
- **1个场景配置节点** (SceneConfigNode)
  - 属性：name, models, scene
  - 连接：无输入，输出到生命周期节点

### 2. Actions配置映射

#### 输入配置
```javascript
actions: [
  {
    actionType: "doubleClick",
    meshNames: ["mesh1", "mesh2"],
    config: {
      highlight: { color: [1, 0, 0], duration: 500 },
      callback: "CameraService.focusToDevice",
      parameters: { deviceId: "device1" }
    }
  }
]
```

#### 映射结果
- **1个事件节点** (EventNode)
  - 类型：doubleClick
  - 目标：meshNames
- **1个高亮动作节点** (HighlightActionNode)
  - 属性：color, duration
- **1个回调动作节点** (CallbackActionNode)
  - 回调：CameraService.focusToDevice
  - 参数：parameters
- **连接关系**：事件节点 → 高亮节点 → 回调节点

### 3. Lifecycle配置映射

#### 输入配置
```javascript
lifecycle: {
  onActivated: [
    {
      trigger: "immediate",
      callback: "DeviceService.setupDataDrivenPolling",
      parameters: {
        dataSource: { type: "polling", interval: 5000 },
        mappings: "mapping.csv",
        transforms: [{ type: "map", mapping: { 0: false, 1: true } }]
      }
    }
  ]
}
```

#### 映射结果
- **1个生命周期节点** (LifecycleNode)
  - 类型：onActivated
  - 触发：immediate
- **1个数据驱动服务节点** (DataDrivenServiceNode)
  - 服务：DeviceService.setupDataDrivenPolling
  - 数据源配置：完整参数结构
- **连接关系**：生命周期节点 → 数据驱动服务节点

### 4. StaticLabels配置映射

#### 输入配置
```javascript
staticLabels: [
  {
    $ref: "templates.1.labels.floorLabel",
    $extend: {
      meshNames: ["floor1"],
      customNames: { floor1: "一层" },
      click: {
        enabled: true,
        callback: "CameraService.moveCamera",
        parameters: { position: [0, 0, 10] }
      }
    }
  }
]
```

#### 映射结果
- **1个静态标签节点** (StaticLabelNode)
  - 引用：templates.1.labels.floorLabel
  - 目标网格：floor1
  - 显示名称：一层
- **1个点击事件节点** (EventNode)
  - 类型：click
  - 目标：floor1
- **1个相机移动节点** (CallbackActionNode)
  - 回调：CameraService.moveCamera
- **连接关系**：静态标签节点 → 点击事件节点 → 相机移动节点

### 5. 模板引用映射

#### 输入配置
```javascript
{
  $ref: "templates.1.cameras.building"
}
```

#### 映射结果
- **1个引用节点** (ReferenceNode)
  - 引用路径：templates.1.cameras.building
  - 类型：camera

#### 带扩展的引用
```javascript
{
  $ref: "templates.1.labels.floorLabel",
  $extend: {
    meshNames: ["customMesh"],
    position: [1, 2, 3]
  }
}
```

#### 映射结果
- **1个引用节点** (ReferenceNode)
  - 引用路径：templates.1.labels.floorLabel
  - 扩展参数：meshNames, position

## 连接推断算法

### 1. 基于配置结构的连接
```typescript
interface ConnectionRule {
  // 事件 → 动作连接
  eventToAction: (event: EventConfig, actions: ActionConfig[]) => Connection[];
  
  // 生命周期 → 回调连接
  lifecycleToCallback: (lifecycle: LifecycleConfig) => Connection[];
  
  // 数据流连接
  dataFlow: (dataConfig: DataConfig) => Connection[];
}
```

### 2. 执行顺序推断
- **并行执行**：同级配置项 → 并行连接
- **顺序执行**：数组配置 → 串行连接
- **条件执行**：分支配置 → 条件连接

### 3. 数据流连接
- **数据源** → **数据转换** → **数据消费**
- **轮询数据源** → **映射转换** → **动画控制**

## 节点布局策略

### 1. 层次布局
- **第1层**：场景配置节点
- **第2层**：生命周期节点、事件节点
- **第3层**：动作节点、回调节点
- **第4层**：数据节点

### 2. 分组布局
- **事件组**：事件节点及其动作链
- **生命周期组**：生命周期节点及其回调链
- **数据流组**：数据源到消费的完整链路
- **静态配置组**：静态标签、引用节点等

### 3. 避免交叉
- **最小化连线交叉**
- **保持逻辑流向清晰**
- **相关节点就近放置**

## 实现优先级

### 高优先级
1. **Actions配置映射** - 最常用的交互配置
2. **Lifecycle配置映射** - 场景初始化的关键
3. **基础连接推断** - 确保节点图逻辑正确

### 中优先级
4. **StaticLabels配置映射** - 静态UI元素
5. **模板引用映射** - 配置复用机制
6. **布局优化** - 视觉效果提升

### 低优先级
7. **复杂数据流映射** - 高级数据驱动功能
8. **性能优化** - 大型配置处理
9. **错误恢复** - 异常配置处理
