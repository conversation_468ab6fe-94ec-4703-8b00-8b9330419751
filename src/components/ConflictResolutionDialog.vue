<template>
  <div class="dialog-overlay" @click="closeDialog">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3>🔧 解决参数冲突</h3>
        <button class="close-btn" @click="closeDialog">✕</button>
      </div>
      
      <div class="dialog-content">
        <div v-if="conflict" class="conflict-details">
          <!-- 冲突信息 -->
          <div class="conflict-info">
            <h4>{{ conflict.parameterName }}</h4>
            <p class="conflict-description">{{ conflict.description }}</p>
            <div class="conflict-meta">
              <span class="severity-badge" :class="`severity-${conflict.severity}`">
                {{ getSeverityText(conflict.severity) }}
              </span>
              <span class="type-badge">{{ getTypeText(conflict.conflictType) }}</span>
            </div>
          </div>

          <!-- 值对比 -->
          <div class="value-comparison">
            <div class="value-section">
              <h5>🎨 模板值</h5>
              <div class="value-display template-value">
                <pre>{{ formatValue(conflict.templateValue) }}</pre>
              </div>
            </div>
            
            <div class="value-section">
              <h5>🔧 实例值</h5>
              <div class="value-display instance-value">
                <pre>{{ formatValue(conflict.instanceValue) }}</pre>
              </div>
            </div>
          </div>

          <!-- 解决方案选择 -->
          <div class="resolution-options">
            <h5>选择解决方案:</h5>
            
            <div class="option-list">
              <label class="option-item">
                <input
                  type="radio"
                  v-model="selectedResolution"
                  value="use_template"
                  name="resolution"
                />
                <div class="option-content">
                  <div class="option-title">使用模板值</div>
                  <div class="option-description">
                    用模板中的新值替换实例中的自定义值
                  </div>
                </div>
              </label>

              <label class="option-item">
                <input
                  type="radio"
                  v-model="selectedResolution"
                  value="keep_instance"
                  name="resolution"
                />
                <div class="option-content">
                  <div class="option-title">保留实例值</div>
                  <div class="option-description">
                    保留实例中的自定义值，忽略模板变更
                  </div>
                </div>
              </label>

              <label class="option-item" v-if="canMerge">
                <input
                  type="radio"
                  v-model="selectedResolution"
                  value="merge"
                  name="resolution"
                />
                <div class="option-content">
                  <div class="option-title">智能合并</div>
                  <div class="option-description">
                    尝试智能合并模板值和实例值
                  </div>
                </div>
              </label>

              <label class="option-item">
                <input
                  type="radio"
                  v-model="selectedResolution"
                  value="custom"
                  name="resolution"
                />
                <div class="option-content">
                  <div class="option-title">自定义值</div>
                  <div class="option-description">
                    手动输入一个新的值
                  </div>
                </div>
              </label>
            </div>

            <!-- 自定义值输入 -->
            <div v-if="selectedResolution === 'custom'" class="custom-value-input">
              <label for="customValue">自定义值:</label>
              <textarea
                id="customValue"
                v-model="customValue"
                placeholder="输入自定义值 (JSON格式)"
                class="custom-input"
                rows="4"
              ></textarea>
              <div v-if="customValueError" class="error-message">
                {{ customValueError }}
              </div>
            </div>
          </div>

          <!-- 建议 -->
          <div v-if="conflict.suggestedResolution" class="suggestion">
            <h5>💡 建议</h5>
            <p>{{ conflict.suggestedResolution }}</p>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="cancel-btn" @click="closeDialog">取消</button>
        <button 
          class="resolve-btn" 
          @click="resolveConflict"
          :disabled="!canResolve"
        >
          解决冲突
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ParameterConflict } from '../services/ParameterSyncManager';

// Props
interface Props {
  conflict: ParameterConflict | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  resolve: [resolution: { type: 'use_template' | 'keep_instance' | 'merge' | 'custom'; value?: any }];
  cancel: [];
}>();

// 响应式数据
const selectedResolution = ref<'use_template' | 'keep_instance' | 'merge' | 'custom'>('use_template');
const customValue = ref('');
const customValueError = ref('');

// 计算属性
const canMerge = computed(() => {
  if (!props.conflict) return false;
  
  const templateValue = props.conflict.templateValue;
  const instanceValue = props.conflict.instanceValue;
  
  // 只有当两个值都是对象时才能合并
  return (
    typeof templateValue === 'object' && 
    typeof instanceValue === 'object' &&
    templateValue !== null &&
    instanceValue !== null &&
    !Array.isArray(templateValue) &&
    !Array.isArray(instanceValue)
  );
});

const canResolve = computed(() => {
  if (selectedResolution.value === 'custom') {
    return customValue.value.trim() !== '' && !customValueError.value;
  }
  return true;
});

// 监听自定义值变化，验证JSON格式
watch(customValue, (newValue) => {
  if (selectedResolution.value === 'custom' && newValue.trim()) {
    try {
      JSON.parse(newValue);
      customValueError.value = '';
    } catch (error) {
      customValueError.value = '请输入有效的JSON格式';
    }
  } else {
    customValueError.value = '';
  }
});

// 方法
const closeDialog = () => {
  emit('cancel');
  resetForm();
};

const resolveConflict = () => {
  if (!canResolve.value) return;
  
  let resolution: { type: 'use_template' | 'keep_instance' | 'merge' | 'custom'; value?: any };
  
  if (selectedResolution.value === 'custom') {
    try {
      const parsedValue = JSON.parse(customValue.value);
      resolution = {
        type: 'custom',
        value: parsedValue
      };
    } catch (error) {
      customValueError.value = '请输入有效的JSON格式';
      return;
    }
  } else {
    resolution = {
      type: selectedResolution.value
    };
  }
  
  emit('resolve', resolution);
  resetForm();
};

const resetForm = () => {
  selectedResolution.value = 'use_template';
  customValue.value = '';
  customValueError.value = '';
};

const formatValue = (value: any): string => {
  if (value === null || value === undefined) {
    return 'null';
  }
  
  if (typeof value === 'string') {
    return `"${value}"`;
  }
  
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value, null, 2);
    } catch (error) {
      return String(value);
    }
  }
  
  return String(value);
};

const getSeverityText = (severity: string): string => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  };
  return texts[severity] || severity;
};

const getTypeText = (type: string): string => {
  const texts: Record<string, string> = {
    parameter_removed: '参数删除',
    type_mismatch: '类型不匹配',
    value_incompatible: '值不兼容',
    dependency_missing: '依赖缺失',
    custom_override: '自定义覆盖'
  };
  return texts[type] || type;
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.dialog-container {
  background: #1a202c;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 700px;
  width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #4a5568;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.dialog-header h3 {
  margin: 0;
  color: #f7fafc;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #a0aec0;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.conflict-info h4 {
  margin: 0 0 0.5rem 0;
  color: #f7fafc;
  font-size: 1.1rem;
  font-weight: 600;
}

.conflict-description {
  color: #a0aec0;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.conflict-meta {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.severity-badge,
.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.severity-badge.severity-low {
  background: #2d5016;
  color: #9ae6b4;
}

.severity-badge.severity-medium {
  background: #744210;
  color: #f6ad55;
}

.severity-badge.severity-high {
  background: #742a2a;
  color: #fc8181;
}

.severity-badge.severity-critical {
  background: #63171b;
  color: #feb2b2;
}

.type-badge {
  background: #2a4365;
  color: #90cdf4;
}

.value-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.value-section h5 {
  margin: 0 0 0.75rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
}

.value-display {
  background: #2d3748;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #4a5568;
  max-height: 150px;
  overflow-y: auto;
}

.value-display pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
  word-break: break-all;
}

.template-value {
  border-left: 3px solid #3182ce;
}

.template-value pre {
  color: #90cdf4;
}

.instance-value {
  border-left: 3px solid #38a169;
}

.instance-value pre {
  color: #9ae6b4;
}

.resolution-options h5 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.option-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-item:hover {
  background: #374151;
  border-color: #63b3ed;
}

.option-item input[type="radio"] {
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-title {
  font-weight: 600;
  color: #f7fafc;
  margin-bottom: 0.25rem;
}

.option-description {
  font-size: 0.875rem;
  color: #a0aec0;
  line-height: 1.4;
}

.custom-value-input {
  margin-top: 1rem;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
}

.custom-value-input label {
  display: block;
  margin-bottom: 0.5rem;
  color: #e2e8f0;
  font-weight: 500;
}

.custom-input {
  width: 100%;
  padding: 0.75rem;
  background: #1a202c;
  border: 1px solid #4a5568;
  border-radius: 4px;
  color: #e2e8f0;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.custom-input:focus {
  outline: none;
  border-color: #63b3ed;
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.error-message {
  margin-top: 0.5rem;
  color: #fc8181;
  font-size: 0.875rem;
}

.suggestion {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #2a4365;
  border-radius: 6px;
  border-left: 3px solid #63b3ed;
}

.suggestion h5 {
  margin: 0 0 0.5rem 0;
  color: #90cdf4;
  font-size: 1rem;
  font-weight: 600;
}

.suggestion p {
  margin: 0;
  color: #e2e8f0;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  background: #2d3748;
  border-top: 1px solid #4a5568;
}

.cancel-btn,
.resolve-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #4a5568;
  color: #e2e8f0;
}

.cancel-btn:hover {
  background: #718096;
}

.resolve-btn {
  background: #38a169;
  color: white;
}

.resolve-btn:hover:not(:disabled) {
  background: #2f855a;
  transform: translateY(-1px);
}

.resolve-btn:disabled {
  background: #4a5568;
  color: #a0aec0;
  cursor: not-allowed;
}

/* 滚动条样式 */
.dialog-content::-webkit-scrollbar,
.value-display::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track,
.value-display::-webkit-scrollbar-track {
  background: #2d3748;
}

.dialog-content::-webkit-scrollbar-thumb,
.value-display::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover,
.value-display::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .value-comparison {
    grid-template-columns: 1fr;
  }
  
  .conflict-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
}
</style>
