<template>
  <div class="enhanced-template-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="header-left">
        <h2 class="panel-title">
          <span class="title-icon">🎨</span>
          模板库
        </h2>
        <div class="template-count">{{ filteredTemplates.length }} 个模板</div>
      </div>
      
      <div class="header-actions">
        <!-- 搜索框 -->
        <div class="search-container">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索模板..."
            class="search-input"
          />
          <span class="search-icon">🔍</span>
        </div>
        
        <!-- 视图切换 -->
        <div class="view-toggle">
          <button
            @click="viewMode = 'grid'"
            :class="{ active: viewMode === 'grid' }"
            class="view-btn"
            title="网格视图"
          >
            ⊞
          </button>
          <button
            @click="viewMode = 'list'"
            :class="{ active: viewMode === 'list' }"
            class="view-btn"
            title="列表视图"
          >
            ☰
          </button>
        </div>
        
        <!-- 更多操作 -->
        <div class="more-actions">
          <button @click="showCreateDialog = true" class="action-btn create-btn">
            <span class="btn-icon">➕</span>
            从节点创建
          </button>
          <button @click="refreshTemplates" class="action-btn refresh-btn">
            <span class="btn-icon">🔄</span>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 过滤器栏 -->
    <div class="filter-bar">
      <div class="filter-group">
        <label class="filter-label">类型:</label>
        <select v-model="selectedType" class="filter-select">
          <option value="">全部类型</option>
          <option v-for="type in availableTypes" :key="type" :value="type">
            {{ getTypeDisplayName(type) }}
          </option>
        </select>
      </div>
      
      <div class="filter-group">
        <label class="filter-label">分类:</label>
        <select v-model="selectedCategory" class="filter-select">
          <option value="">全部分类</option>
          <option v-for="category in availableCategories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>
      </div>
      
      <div class="filter-group">
        <label class="filter-label">排序:</label>
        <select v-model="sortBy" class="filter-select">
          <option value="name">名称</option>
          <option value="createdAt">创建时间</option>
          <option value="usageCount">使用次数</option>
          <option value="updatedAt">更新时间</option>
        </select>
      </div>
      
      <div class="filter-actions">
        <button @click="clearFilters" class="clear-filters-btn">
          清除筛选
        </button>
      </div>
    </div>

    <!-- 模板列表 -->
    <div class="template-content" :class="{ [`view-${viewMode}`]: true }">
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载模板中...</p>
      </div>
      
      <div v-else-if="filteredTemplates.length === 0" class="empty-state">
        <div class="empty-icon">📦</div>
        <h3>没有找到模板</h3>
        <p v-if="hasActiveFilters">尝试调整筛选条件或清除筛选</p>
        <p v-else>开始创建您的第一个模板</p>
        <button @click="showCreateDialog = true" class="create-first-btn">
          从节点创建模板
        </button>
      </div>
      
      <div v-else class="template-grid">
        <TemplateCard
          v-for="template in paginatedTemplates"
          :key="template.id"
          :template="template"
          :view-mode="viewMode"
          @select="selectTemplate"
          @edit="editTemplate"
          @delete="deleteTemplate"
          @duplicate="duplicateTemplate"
          @drag-start="handleDragStart"
        />
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="pagination">
      <button
        @click="currentPage = Math.max(1, currentPage - 1)"
        :disabled="currentPage === 1"
        class="page-btn"
      >
        ‹ 上一页
      </button>
      
      <div class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </div>
      
      <button
        @click="currentPage = Math.min(totalPages, currentPage + 1)"
        :disabled="currentPage === totalPages"
        class="page-btn"
      >
        下一页 ›
      </button>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="status-item">
          <span class="status-icon">📊</span>
          统计: {{ templateStats.totalTemplates }} 个模板
        </span>
        <span class="status-item">
          <span class="status-icon">🔄</span>
          实例: {{ instanceStats.total }} 个
        </span>
      </div>
      
      <div class="status-right">
        <button
          @click="showSyncPanel = true"
          :class="{ 'has-conflicts': conflicts.length > 0 }"
          class="sync-status-btn"
        >
          <span class="status-icon">⚠️</span>
          {{ conflicts.length > 0 ? `${conflicts.length} 个冲突` : '同步正常' }}
        </button>
      </div>
    </div>

    <!-- 节点转模板对话框 -->
    <NodeToTemplateDialog
      v-if="showCreateDialog"
      @close="showCreateDialog = false"
      @created="handleTemplateCreated"
    />

    <!-- 参数同步面板 -->
    <SimpleParameterSyncPanel
      v-if="showSyncPanel"
      @close="showSyncPanel = false"
    />

    <!-- 模板详情侧边栏 -->
    <TemplateDetailSidebar
      v-if="selectedTemplate"
      :template="selectedTemplate"
      @close="selectedTemplate = null"
      @updated="handleTemplateUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { TemplateManager } from '../services/TemplateManager';
import { Template, TemplateType } from '../types/TemplateTypes';
import TemplateCard from './TemplateCard.vue';
import NodeToTemplateDialog from './NodeToTemplateDialog.vue';
import SimpleParameterSyncPanel from './SimpleParameterSyncPanel.vue';
import TemplateDetailSidebar from './TemplateDetailSidebar.vue';

// 组件状态
const isLoading = ref(false);
const searchQuery = ref('');
const selectedType = ref('');
const selectedCategory = ref('');
const sortBy = ref('name');
const viewMode = ref<'grid' | 'list'>('grid');
const currentPage = ref(1);
const pageSize = ref(12);

// 对话框状态
const showCreateDialog = ref(false);
const showSyncPanel = ref(false);
const selectedTemplate = ref<Template | null>(null);

// 数据
const templateManager = TemplateManager.getInstance();
const allTemplates = ref<Template[]>([]);
const templateStats = ref<any>({});
const instanceStats = ref<any>({});
const conflicts = ref<any[]>([]);

// 计算属性
const availableTypes = computed(() => {
  const types = new Set(allTemplates.value.map(t => t.type));
  return Array.from(types).sort();
});

const availableCategories = computed(() => {
  const categories = new Set(allTemplates.value.map(t => t.metadata.category));
  return Array.from(categories).sort();
});

const hasActiveFilters = computed(() => {
  return searchQuery.value || selectedType.value || selectedCategory.value;
});

const filteredTemplates = computed(() => {
  let filtered = allTemplates.value;

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description?.toLowerCase().includes(query) ||
      template.metadata.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }

  // 类型过滤
  if (selectedType.value) {
    filtered = filtered.filter(template => template.type === selectedType.value);
  }

  // 分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter(template => template.metadata.category === selectedCategory.value);
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'createdAt':
        return new Date(b.metadata.createdAt).getTime() - new Date(a.metadata.createdAt).getTime();
      case 'usageCount':
        return (b.metadata.usageCount || 0) - (a.metadata.usageCount || 0);
      case 'updatedAt':
        return new Date(b.metadata.updatedAt).getTime() - new Date(a.metadata.updatedAt).getTime();
      default:
        return 0;
    }
  });

  return filtered;
});

const totalPages = computed(() => {
  return Math.ceil(filteredTemplates.value.length / pageSize.value);
});

const paginatedTemplates = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredTemplates.value.slice(start, end);
});

// 方法
const refreshTemplates = async () => {
  isLoading.value = true;
  try {
    allTemplates.value = templateManager.getAllTemplates();
    templateStats.value = templateManager.getUsageStats();
    instanceStats.value = templateManager.getInstanceStats();
    conflicts.value = templateManager.getParameterConflicts();
    
    console.log('✅ 模板数据刷新完成');
  } catch (error) {
    console.error('❌ 模板数据刷新失败:', error);
  } finally {
    isLoading.value = false;
  }
};

const clearFilters = () => {
  searchQuery.value = '';
  selectedType.value = '';
  selectedCategory.value = '';
  currentPage.value = 1;
};

const selectTemplate = (template: Template) => {
  selectedTemplate.value = template;
};

const editTemplate = (template: Template) => {
  selectedTemplate.value = template;
  // 可以添加编辑模式标识
};

const deleteTemplate = async (template: Template) => {
  if (confirm(`确定要删除模板 "${template.name}" 吗？`)) {
    const result = templateManager.deleteTemplate(template.id);
    if (result.success) {
      console.log(`✅ 模板已删除: ${template.name}`);
      await refreshTemplates();
    } else {
      console.error(`❌ 模板删除失败: ${result.errors?.join(', ')}`);
    }
  }
};

const duplicateTemplate = async (template: Template) => {
  const duplicatedTemplate = {
    ...template,
    name: `${template.name} (副本)`,
    id: undefined // 让系统生成新ID
  };
  
  const result = templateManager.createTemplate(duplicatedTemplate);
  if (result.success) {
    console.log(`✅ 模板已复制: ${duplicatedTemplate.name}`);
    await refreshTemplates();
  } else {
    console.error(`❌ 模板复制失败: ${result.errors?.join(', ')}`);
  }
};

const handleDragStart = (template: Template, event: DragEvent) => {
  // 设置拖拽数据
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'template',
      template: template
    }));
    event.dataTransfer.effectAllowed = 'copy';
  }
  
  console.log(`🎯 开始拖拽模板: ${template.name}`);
};

const handleTemplateCreated = async (template: Template) => {
  console.log(`✅ 新模板已创建: ${template.name}`);
  showCreateDialog.value = false;
  await refreshTemplates();
};

const handleTemplateUpdated = async (template: Template) => {
  console.log(`✅ 模板已更新: ${template.name}`);
  await refreshTemplates();
};

const getTypeDisplayName = (type: TemplateType): string => {
  const typeNames: Record<TemplateType, string> = {
    [TemplateType.MESH]: '网格',
    [TemplateType.STYLE]: '样式',
    [TemplateType.CAMERA]: '相机',
    [TemplateType.ACTION]: '动作',
    [TemplateType.LABEL]: '标签',
    [TemplateType.POSITION]: '位置',
    [TemplateType.INTERACTION]: '交互',
    [TemplateType.ENVIRONMENT]: '环境',
    [TemplateType.CALLBACK]: '回调'
  };
  return typeNames[type] || type;
};

// 监听筛选条件变化，重置分页
watch([searchQuery, selectedType, selectedCategory], () => {
  currentPage.value = 1;
});

// 生命周期
onMounted(() => {
  refreshTemplates();
  
  // 定期刷新数据
  const interval = setInterval(refreshTemplates, 30000); // 30秒刷新一次
  
  // 组件卸载时清理定时器
  return () => clearInterval(interval);
});
</script>

<style scoped>
.enhanced-template-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a202c;
  border-radius: 8px;
  border: 1px solid #4a5568;
  overflow: hidden;
}

/* 面板头部样式 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #f7fafc;
  font-size: 1.25rem;
  font-weight: 600;
}

.title-icon {
  font-size: 1.5rem;
}

.template-count {
  color: #a0aec0;
  font-size: 0.875rem;
  background: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 搜索框样式 */
.search-container {
  position: relative;
}

.search-input {
  width: 200px;
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  background: #4a5568;
  border: 1px solid #718096;
  border-radius: 6px;
  color: #f7fafc;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #63b3ed;
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  pointer-events: none;
}

/* 视图切换样式 */
.view-toggle {
  display: flex;
  background: #4a5568;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.view-btn:hover {
  background: #718096;
  color: #f7fafc;
}

.view-btn.active {
  background: #63b3ed;
  color: #1a202c;
}

/* 操作按钮样式 */
.more-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.create-btn {
  background: #38a169;
  color: white;
}

.create-btn:hover {
  background: #2f855a;
  transform: translateY(-1px);
}

.refresh-btn {
  background: #4a5568;
  color: #e2e8f0;
}

.refresh-btn:hover {
  background: #718096;
}

.btn-icon {
  font-size: 1rem;
}

/* 过滤器栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem 1.5rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label {
  color: #a0aec0;
  font-size: 0.875rem;
  font-weight: 500;
}

.filter-select {
  padding: 0.375rem 0.75rem;
  background: #4a5568;
  border: 1px solid #718096;
  border-radius: 4px;
  color: #f7fafc;
  font-size: 0.875rem;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #63b3ed;
}

.filter-actions {
  margin-left: auto;
}

.clear-filters-btn {
  padding: 0.375rem 0.75rem;
  background: transparent;
  border: 1px solid #718096;
  border-radius: 4px;
  color: #a0aec0;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

/* 模板内容样式 */
.template-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #a0aec0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #4a5568;
  border-top: 3px solid #63b3ed;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #e2e8f0;
  font-size: 1.25rem;
}

.empty-state p {
  margin: 0 0 1.5rem 0;
  color: #a0aec0;
}

.create-first-btn {
  padding: 0.75rem 1.5rem;
  background: #38a169;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.create-first-btn:hover {
  background: #2f855a;
  transform: translateY(-1px);
}

/* 模板网格样式 */
.template-grid {
  display: grid;
  gap: 1.5rem;
}

.view-grid .template-grid {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.view-list .template-grid {
  grid-template-columns: 1fr;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2d3748;
  border-top: 1px solid #4a5568;
}

.page-btn {
  padding: 0.5rem 1rem;
  background: #4a5568;
  border: 1px solid #718096;
  border-radius: 4px;
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #718096;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #a0aec0;
  font-size: 0.875rem;
}

/* 状态栏样式 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: #1a202c;
  border-top: 1px solid #4a5568;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #a0aec0;
  font-size: 0.875rem;
}

.status-icon {
  font-size: 1rem;
}

.sync-status-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #4a5568;
  border: 1px solid #718096;
  border-radius: 4px;
  color: #e2e8f0;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.sync-status-btn:hover {
  background: #718096;
}

.sync-status-btn.has-conflicts {
  background: #e53e3e;
  border-color: #c53030;
  color: white;
}

.sync-status-btn.has-conflicts:hover {
  background: #c53030;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .panel-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .search-input {
    width: 150px;
  }
}

@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .template-content {
    padding: 1rem;
  }
  
  .view-grid .template-grid {
    grid-template-columns: 1fr;
  }
  
  .status-bar {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
}

/* 滚动条样式 */
.template-content::-webkit-scrollbar {
  width: 8px;
}

.template-content::-webkit-scrollbar-track {
  background: #2d3748;
}

.template-content::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
}

.template-content::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}
</style>
