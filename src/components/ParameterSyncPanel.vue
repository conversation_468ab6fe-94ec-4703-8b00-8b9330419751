<template>
  <div class="parameter-sync-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3>参数同步管理</h3>
      <div class="header-actions">
        <button @click="refreshData" class="refresh-btn" :disabled="isLoading">
          {{ isLoading ? '刷新中...' : '刷新' }}
        </button>
        <button @click="toggleExpanded" class="toggle-btn">
          {{ isExpanded ? '收起' : '展开' }}
        </button>
      </div>
    </div>

    <!-- 面板内容 -->
    <div v-if="isExpanded" class="panel-content">
      <!-- 统计信息 -->
      <div class="stats-section">
        <h4>📊 同步统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">总实例数</span>
            <span class="stat-value">{{ instanceStats.total }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">活跃实例</span>
            <span class="stat-value active">{{ instanceStats.byStatus.active || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">过期实例</span>
            <span class="stat-value outdated">{{ instanceStats.byStatus.outdated || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">冲突数量</span>
            <span class="stat-value conflict">{{ conflicts.length }}</span>
          </div>
        </div>
      </div>

      <!-- 冲突列表 -->
      <div v-if="conflicts.length > 0" class="conflicts-section">
        <h4>⚠️ 参数冲突 ({{ conflicts.length }})</h4>
        <div class="conflicts-list">
          <div
            v-for="conflict in conflicts"
            :key="conflict.id"
            class="conflict-item"
            :class="{ [`severity-${conflict.severity}`]: true }"
          >
            <div class="conflict-header">
              <span class="conflict-icon">{{ getConflictIcon(conflict.conflictType) }}</span>
              <div class="conflict-info">
                <div class="conflict-title">{{ conflict.parameterName }}</div>
                <div class="conflict-description">{{ conflict.description }}</div>
              </div>
              <div class="conflict-actions">
                <button
                  v-if="conflict.autoResolvable"
                  @click="autoResolveConflict(conflict)"
                  class="resolve-btn auto"
                  :disabled="isResolving"
                >
                  自动解决
                </button>
                <button
                  @click="showResolveDialog(conflict)"
                  class="resolve-btn manual"
                  :disabled="isResolving"
                >
                  手动解决
                </button>
              </div>
            </div>
            
            <div class="conflict-details">
              <div class="value-comparison">
                <div class="value-item">
                  <span class="value-label">模板值:</span>
                  <span class="value-content template">{{ formatValue(conflict.templateValue) }}</span>
                </div>
                <div class="value-item">
                  <span class="value-label">实例值:</span>
                  <span class="value-content instance">{{ formatValue(conflict.instanceValue) }}</span>
                </div>
              </div>
              
              <div v-if="conflict.suggestedResolution" class="suggested-resolution">
                <span class="suggestion-label">建议:</span>
                <span class="suggestion-text">{{ conflict.suggestedResolution }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-actions">
          <button
            @click="batchResolveConflicts('smart')"
            class="batch-btn"
            :disabled="isResolving || conflicts.length === 0"
          >
            🤖 智能批量解决
          </button>
          <button
            @click="batchResolveConflicts('conservative')"
            class="batch-btn"
            :disabled="isResolving || conflicts.length === 0"
          >
            🛡️ 保守批量解决
          </button>
        </div>
      </div>

      <!-- 变更历史 -->
      <div class="history-section">
        <h4>📝 变更历史</h4>
        <div class="history-controls">
          <select v-model="selectedTemplateForHistory" class="template-select">
            <option value="">所有模板</option>
            <option v-for="template in allTemplates" :key="template.id" :value="template.id">
              {{ template.name }}
            </option>
          </select>
          <input
            v-model="historyLimit"
            type="number"
            min="10"
            max="100"
            class="limit-input"
            placeholder="显示数量"
          />
        </div>
        
        <div class="history-list">
          <div
            v-for="change in filteredChangeHistory"
            :key="change.id"
            class="history-item"
            :class="{ [`change-${change.changeType}`]: true }"
          >
            <div class="history-header">
              <span class="change-icon">{{ getChangeIcon(change.changeType) }}</span>
              <div class="change-info">
                <div class="change-title">{{ change.parameterName }}</div>
                <div class="change-description">{{ change.description }}</div>
              </div>
              <div class="change-time">{{ formatTime(change.timestamp) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 批量任务 -->
      <div class="tasks-section">
        <h4>🔄 批量任务</h4>
        <div class="tasks-list">
          <div
            v-for="task in batchTasks"
            :key="task.id"
            class="task-item"
            :class="{ [`status-${task.status}`]: true }"
          >
            <div class="task-header">
              <span class="task-icon">{{ getTaskIcon(task.status) }}</span>
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-description">{{ task.description }}</div>
              </div>
              <div class="task-actions">
                <button
                  v-if="task.status === 'running'"
                  @click="cancelTask(task.id)"
                  class="cancel-btn"
                >
                  取消
                </button>
              </div>
            </div>
            
            <div v-if="task.status === 'running'" class="task-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: `${task.progress}%` }"></div>
              </div>
              <div class="progress-text">
                {{ task.processedItems }}/{{ task.totalItems }} ({{ task.progress }}%)
              </div>
            </div>
            
            <div v-if="task.status === 'completed'" class="task-results">
              <span class="result-success">✅ {{ task.processedItems - task.failedItems }} 成功</span>
              <span v-if="task.failedItems > 0" class="result-failed">❌ {{ task.failedItems }} 失败</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 冲突解决对话框 -->
    <ConflictResolutionDialog
      v-if="showConflictDialog"
      :conflict="selectedConflict"
      @resolve="handleConflictResolution"
      @cancel="closeConflictDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { TemplateManager } from '../services/TemplateManager';
import { ParameterConflict, ParameterChange } from '../services/ParameterSyncManager';
import { BatchUpdateTask } from '../services/BatchUpdateManager';
import ConflictResolutionDialog from './ConflictResolutionDialog.vue';

// 组件状态
const isExpanded = ref(true);
const isLoading = ref(false);
const isResolving = ref(false);
const showConflictDialog = ref(false);
const selectedConflict = ref<ParameterConflict | null>(null);
const selectedTemplateForHistory = ref('');
const historyLimit = ref(20);

// 数据
const templateManager = TemplateManager.getInstance();
const instanceStats = ref<any>({});
const conflicts = ref<ParameterConflict[]>([]);
const changeHistory = ref<ParameterChange[]>([]);
const batchTasks = ref<BatchUpdateTask[]>([]);
const allTemplates = computed(() => templateManager.getAllTemplates());

// 计算属性
const filteredChangeHistory = computed(() => {
  return templateManager.getParameterChangeHistory(
    selectedTemplateForHistory.value || undefined,
    historyLimit.value
  );
});

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const refreshData = async () => {
  isLoading.value = true;
  try {
    // 刷新统计信息
    instanceStats.value = templateManager.getInstanceStats();
    
    // 刷新冲突列表
    conflicts.value = templateManager.getParameterConflicts();
    
    // 刷新变更历史
    changeHistory.value = templateManager.getParameterChangeHistory(undefined, 50);
    
    // 刷新批量任务
    batchTasks.value = templateManager.getBatchUpdateTasks();
    
    console.log('✅ 参数同步数据刷新完成');
  } catch (error) {
    console.error('❌ 数据刷新失败:', error);
  } finally {
    isLoading.value = false;
  }
};

const autoResolveConflict = async (conflict: ParameterConflict) => {
  isResolving.value = true;
  try {
    const success = await templateManager.resolveParameterConflict(
      conflict.id,
      'use_template'
    );
    
    if (success) {
      console.log(`✅ 冲突自动解决: ${conflict.parameterName}`);
      await refreshData();
    } else {
      console.error(`❌ 冲突自动解决失败: ${conflict.parameterName}`);
    }
  } catch (error) {
    console.error('冲突解决失败:', error);
  } finally {
    isResolving.value = false;
  }
};

const showResolveDialog = (conflict: ParameterConflict) => {
  selectedConflict.value = conflict;
  showConflictDialog.value = true;
};

const closeConflictDialog = () => {
  showConflictDialog.value = false;
  selectedConflict.value = null;
};

const handleConflictResolution = async (resolution: {
  type: 'use_template' | 'keep_instance' | 'merge' | 'custom';
  value?: any;
}) => {
  if (!selectedConflict.value) return;
  
  isResolving.value = true;
  try {
    const success = await templateManager.resolveParameterConflict(
      selectedConflict.value.id,
      resolution.type,
      resolution.value
    );
    
    if (success) {
      console.log(`✅ 冲突手动解决: ${selectedConflict.value.parameterName}`);
      closeConflictDialog();
      await refreshData();
    } else {
      console.error(`❌ 冲突手动解决失败: ${selectedConflict.value.parameterName}`);
    }
  } catch (error) {
    console.error('冲突解决失败:', error);
  } finally {
    isResolving.value = false;
  }
};

const batchResolveConflicts = async (strategy: 'smart' | 'conservative') => {
  isResolving.value = true;
  try {
    const templateIds = [...new Set(conflicts.value.map(c => c.templateId))];
    
    const result = await templateManager.batchResolveConflicts(templateIds, {
      strategy: strategy === 'smart' ? 'smart' : 'conservative'
    });
    
    console.log(`✅ 批量冲突解决完成: ${strategy}`, result);
    await refreshData();
  } catch (error) {
    console.error('批量冲突解决失败:', error);
  } finally {
    isResolving.value = false;
  }
};

const cancelTask = async (taskId: string) => {
  try {
    const success = templateManager.cancelBatchUpdateTask(taskId);
    if (success) {
      console.log(`✅ 任务已取消: ${taskId}`);
      await refreshData();
    }
  } catch (error) {
    console.error('任务取消失败:', error);
  }
};

// 格式化函数
const formatValue = (value: any): string => {
  if (value === null || value === undefined) return 'null';
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

const formatTime = (timestamp: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(timestamp);
};

const getConflictIcon = (type: string): string => {
  const icons: Record<string, string> = {
    parameter_removed: '🗑️',
    type_mismatch: '🔄',
    value_incompatible: '⚠️',
    dependency_missing: '🔗',
    custom_override: '✏️'
  };
  return icons[type] || '❓';
};

const getChangeIcon = (type: string): string => {
  const icons: Record<string, string> = {
    added: '➕',
    removed: '➖',
    modified: '✏️',
    renamed: '🔄',
    type_changed: '🔀'
  };
  return icons[type] || '📝';
};

const getTaskIcon = (status: string): string => {
  const icons: Record<string, string> = {
    pending: '⏳',
    running: '🔄',
    completed: '✅',
    failed: '❌',
    cancelled: '🚫'
  };
  return icons[status] || '📋';
};

// 生命周期
onMounted(() => {
  refreshData();
  
  // 定期刷新数据
  const interval = setInterval(refreshData, 30000); // 30秒刷新一次
  
  // 组件卸载时清理定时器
  return () => clearInterval(interval);
});

// 监听模板选择变化
watch(selectedTemplateForHistory, () => {
  // 变更历史会通过计算属性自动更新
});
</script>

<style scoped>
.parameter-sync-panel {
  background: #1a202c;
  border-radius: 8px;
  border: 1px solid #4a5568;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.panel-header h3 {
  margin: 0;
  color: #f7fafc;
  font-size: 1.1rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.refresh-btn,
.toggle-btn {
  padding: 0.5rem 1rem;
  background: #4a5568;
  color: #e2e8f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.refresh-btn:hover,
.toggle-btn:hover {
  background: #718096;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.panel-content {
  padding: 1rem;
  max-height: 600px;
  overflow-y: auto;
}

/* 统计信息样式 */
.stats-section {
  margin-bottom: 1.5rem;
}

.stats-section h4 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
}

.stat-label {
  font-size: 0.75rem;
  color: #a0aec0;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #e2e8f0;
}

.stat-value.active {
  color: #68d391;
}

.stat-value.outdated {
  color: #f6ad55;
}

.stat-value.conflict {
  color: #fc8181;
}

/* 冲突列表样式 */
.conflicts-section {
  margin-bottom: 1.5rem;
}

.conflicts-section h4 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
}

.conflicts-list {
  max-height: 300px;
  overflow-y: auto;
}

.conflict-item {
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
  margin-bottom: 0.75rem;
  padding: 1rem;
}

.conflict-item.severity-critical {
  border-color: #e53e3e;
}

.conflict-item.severity-high {
  border-color: #f56565;
}

.conflict-item.severity-medium {
  border-color: #ed8936;
}

.conflict-item.severity-low {
  border-color: #ecc94b;
}

.conflict-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.conflict-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.conflict-info {
  flex: 1;
}

.conflict-title {
  font-weight: 600;
  color: #f7fafc;
  margin-bottom: 0.25rem;
}

.conflict-description {
  font-size: 0.875rem;
  color: #a0aec0;
}

.conflict-actions {
  display: flex;
  gap: 0.5rem;
}

.resolve-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.resolve-btn.auto {
  background: #38a169;
  color: white;
}

.resolve-btn.auto:hover {
  background: #2f855a;
}

.resolve-btn.manual {
  background: #3182ce;
  color: white;
}

.resolve-btn.manual:hover {
  background: #2b6cb0;
}

.resolve-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.conflict-details {
  border-top: 1px solid #4a5568;
  padding-top: 0.75rem;
}

.value-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.value-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.value-label {
  font-size: 0.75rem;
  color: #a0aec0;
  font-weight: 500;
}

.value-content {
  padding: 0.5rem;
  background: #1a202c;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
  word-break: break-all;
}

.value-content.template {
  border-left: 3px solid #3182ce;
  color: #90cdf4;
}

.value-content.instance {
  border-left: 3px solid #38a169;
  color: #9ae6b4;
}

.suggested-resolution {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  padding: 0.5rem;
  background: #2a4365;
  border-radius: 4px;
  border-left: 3px solid #63b3ed;
}

.suggestion-label {
  font-size: 0.75rem;
  color: #90cdf4;
  font-weight: 500;
}

.suggestion-text {
  font-size: 0.875rem;
  color: #e2e8f0;
}

/* 批量操作样式 */
.batch-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #4a5568;
}

.batch-btn {
  padding: 0.75rem 1.5rem;
  background: #4a5568;
  color: #e2e8f0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.batch-btn:hover:not(:disabled) {
  background: #718096;
  transform: translateY(-1px);
}

.batch-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 历史记录样式 */
.history-section {
  margin-bottom: 1.5rem;
}

.history-section h4 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
}

.history-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.template-select,
.limit-input {
  padding: 0.5rem;
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 4px;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.template-select {
  flex: 1;
}

.limit-input {
  width: 120px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #2d3748;
  border-radius: 4px;
  border: 1px solid #4a5568;
  margin-bottom: 0.5rem;
}

.change-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

.change-info {
  flex: 1;
}

.change-title {
  font-weight: 500;
  color: #f7fafc;
  margin-bottom: 0.25rem;
}

.change-description {
  font-size: 0.875rem;
  color: #a0aec0;
}

.change-time {
  font-size: 0.75rem;
  color: #718096;
}

/* 任务列表样式 */
.tasks-section h4 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
}

.tasks-list {
  max-height: 200px;
  overflow-y: auto;
}

.task-item {
  background: #2d3748;
  border-radius: 6px;
  border: 1px solid #4a5568;
  margin-bottom: 0.75rem;
  padding: 1rem;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.task-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.task-info {
  flex: 1;
}

.task-name {
  font-weight: 600;
  color: #f7fafc;
  margin-bottom: 0.25rem;
}

.task-description {
  font-size: 0.875rem;
  color: #a0aec0;
}

.cancel-btn {
  padding: 0.5rem 1rem;
  background: #e53e3e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.cancel-btn:hover {
  background: #c53030;
}

.task-progress {
  margin-top: 0.75rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #4a5568;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: #38a169;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.875rem;
  color: #a0aec0;
  text-align: center;
}

.task-results {
  display: flex;
  gap: 1rem;
  margin-top: 0.75rem;
  font-size: 0.875rem;
}

.result-success {
  color: #68d391;
}

.result-failed {
  color: #fc8181;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.conflicts-list::-webkit-scrollbar,
.history-list::-webkit-scrollbar,
.tasks-list::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.conflicts-list::-webkit-scrollbar-track,
.history-list::-webkit-scrollbar-track,
.tasks-list::-webkit-scrollbar-track {
  background: #2d3748;
}

.panel-content::-webkit-scrollbar-thumb,
.conflicts-list::-webkit-scrollbar-thumb,
.history-list::-webkit-scrollbar-thumb,
.tasks-list::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.conflicts-list::-webkit-scrollbar-thumb:hover,
.history-list::-webkit-scrollbar-thumb:hover,
.tasks-list::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}
</style>
