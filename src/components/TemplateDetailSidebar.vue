<template>
  <div class="template-detail-sidebar">
    <!-- 侧边栏遮罩 -->
    <div class="sidebar-overlay" @click="$emit('close')"></div>
    
    <!-- 侧边栏内容 -->
    <div class="sidebar-content">
      <!-- 头部 -->
      <div class="sidebar-header">
        <div class="header-info">
          <div class="template-icon">
            {{ getTypeIcon(template.type) }}
          </div>
          <div class="template-title">
            <h2>{{ template.name }}</h2>
            <div class="template-meta">
              <span class="template-type">{{ getTypeDisplayName(template.type) }}</span>
              <span class="template-version">v{{ template.version }}</span>
            </div>
          </div>
        </div>
        
        <button @click="$emit('close')" class="close-btn">
          ✕
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="sidebar-body">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>名称</label>
              <div class="info-value">{{ template.name }}</div>
            </div>
            <div class="info-item">
              <label>描述</label>
              <div class="info-value">{{ template.description || '无描述' }}</div>
            </div>
            <div class="info-item">
              <label>类型</label>
              <div class="info-value">{{ getTypeDisplayName(template.type) }}</div>
            </div>
            <div class="info-item">
              <label>分类</label>
              <div class="info-value">{{ template.metadata.category }}</div>
            </div>
            <div class="info-item">
              <label>版本</label>
              <div class="info-value">{{ template.version }}</div>
            </div>
            <div class="info-item">
              <label>作者</label>
              <div class="info-value">{{ template.metadata.author || '未知' }}</div>
            </div>
          </div>
        </div>

        <!-- 标签 -->
        <div v-if="template.metadata.tags.length > 0" class="info-section">
          <h3 class="section-title">标签</h3>
          <div class="tags-container">
            <span
              v-for="tag in template.metadata.tags"
              :key="tag"
              class="tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- 使用统计 -->
        <div class="info-section">
          <h3 class="section-title">使用统计</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-value">{{ template.metadata.usageCount || 0 }}</div>
                <div class="stat-label">总使用次数</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">🔗</div>
              <div class="stat-info">
                <div class="stat-value">{{ instanceCount }}</div>
                <div class="stat-label">当前实例</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">📅</div>
              <div class="stat-info">
                <div class="stat-value">{{ formatDate(template.metadata.createdAt) }}</div>
                <div class="stat-label">创建时间</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">🔄</div>
              <div class="stat-info">
                <div class="stat-value">{{ formatDate(template.metadata.updatedAt) }}</div>
                <div class="stat-label">更新时间</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 参数定义 -->
        <div v-if="template.definition?.parameters.length" class="info-section">
          <h3 class="section-title">参数定义</h3>
          <div class="parameters-list">
            <div
              v-for="param in template.definition.parameters"
              :key="param.name"
              class="parameter-item"
            >
              <div class="param-header">
                <span class="param-name">{{ param.name }}</span>
                <span class="param-type">{{ param.type }}</span>
                <span v-if="param.required" class="param-required">必需</span>
              </div>
              <div v-if="param.description" class="param-description">
                {{ param.description }}
              </div>
              <div v-if="param.defaultValue !== undefined" class="param-default">
                默认值: <code>{{ formatValue(param.defaultValue) }}</code>
              </div>
            </div>
          </div>
        </div>

        <!-- 实例列表 -->
        <div v-if="instances.length > 0" class="info-section">
          <h3 class="section-title">
            实例列表 ({{ instances.length }})
            <button @click="refreshInstances" class="refresh-btn">🔄</button>
          </h3>
          <div class="instances-list">
            <div
              v-for="instance in instances"
              :key="instance.id"
              class="instance-item"
              :class="{ [`status-${instance.status}`]: true }"
            >
              <div class="instance-header">
                <span class="instance-id">{{ instance.id.slice(-8) }}</span>
                <span class="instance-scene">{{ instance.sceneId }}</span>
                <div class="instance-status">
                  <span class="status-dot"></span>
                  {{ getStatusText(instance.status) }}
                </div>
              </div>
              <div class="instance-meta">
                <span class="instance-sync">
                  同步: {{ getSyncStatusText(instance.syncStatus) }}
                </span>
                <span class="instance-time">
                  {{ formatDate(instance.lastSyncAt) }}
                </span>
              </div>
              <div v-if="Object.keys(instance.overrides).length > 0" class="instance-overrides">
                <details>
                  <summary>参数覆盖 ({{ Object.keys(instance.overrides).length }})</summary>
                  <div class="overrides-content">
                    <div
                      v-for="[key, value] in Object.entries(instance.overrides)"
                      :key="key"
                      class="override-item"
                    >
                      <span class="override-key">{{ key }}:</span>
                      <span class="override-value">{{ formatValue(value) }}</span>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        <!-- 冲突信息 -->
        <div v-if="conflicts.length > 0" class="info-section">
          <h3 class="section-title conflict-title">
            ⚠️ 参数冲突 ({{ conflicts.length }})
          </h3>
          <div class="conflicts-list">
            <div
              v-for="conflict in conflicts"
              :key="conflict.id"
              class="conflict-item"
              :class="{ [`severity-${conflict.severity}`]: true }"
            >
              <div class="conflict-header">
                <span class="conflict-param">{{ conflict.parameterName }}</span>
                <span class="conflict-type">{{ getConflictTypeText(conflict.conflictType) }}</span>
              </div>
              <div class="conflict-description">{{ conflict.description }}</div>
              <div class="conflict-actions">
                <button
                  @click="resolveConflict(conflict, 'use_template')"
                  class="resolve-btn template"
                >
                  使用模板值
                </button>
                <button
                  @click="resolveConflict(conflict, 'keep_instance')"
                  class="resolve-btn instance"
                >
                  保留实例值
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="sidebar-footer">
        <div class="action-group">
          <button @click="editTemplate" class="action-btn primary">
            ✏️ 编辑模板
          </button>
          <button @click="duplicateTemplate" class="action-btn secondary">
            📋 复制模板
          </button>
        </div>
        
        <div class="action-group">
          <button @click="syncInstances" class="action-btn sync" :disabled="isSyncing">
            {{ isSyncing ? '同步中...' : '🔄 同步实例' }}
          </button>
          <button @click="exportTemplate" class="action-btn export">
            📤 导出模板
          </button>
        </div>
        
        <div class="action-group danger">
          <button @click="deleteTemplate" class="action-btn danger">
            🗑️ 删除模板
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Template, TemplateType, TemplateInstance } from '../types/TemplateTypes';
import { TemplateManager } from '../services/TemplateManager';

// Props
interface Props {
  template: Template;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  updated: [template: Template];
}>();

// 状态
const isSyncing = ref(false);
const instances = ref<TemplateInstance[]>([]);
const conflicts = ref<any[]>([]);

// 服务
const templateManager = TemplateManager.getInstance();

// 计算属性
const instanceCount = computed(() => instances.value.length);

// 方法
const getTypeIcon = (type: TemplateType): string => {
  const icons: Record<TemplateType, string> = {
    [TemplateType.MESH]: '🧊',
    [TemplateType.STYLE]: '🎨',
    [TemplateType.CAMERA]: '📷',
    [TemplateType.ACTION]: '⚡',
    [TemplateType.LABEL]: '🏷️',
    [TemplateType.POSITION]: '📍',
    [TemplateType.INTERACTION]: '👆',
    [TemplateType.ENVIRONMENT]: '🌍',
    [TemplateType.CALLBACK]: '🔗'
  };
  return icons[type] || '📦';
};

const getTypeDisplayName = (type: TemplateType): string => {
  const names: Record<TemplateType, string> = {
    [TemplateType.MESH]: '网格',
    [TemplateType.STYLE]: '样式',
    [TemplateType.CAMERA]: '相机',
    [TemplateType.ACTION]: '动作',
    [TemplateType.LABEL]: '标签',
    [TemplateType.POSITION]: '位置',
    [TemplateType.INTERACTION]: '交互',
    [TemplateType.ENVIRONMENT]: '环境',
    [TemplateType.CALLBACK]: '回调'
  };
  return names[type] || type;
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
};

const formatValue = (value: any): string => {
  if (value === null || value === undefined) return 'null';
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    error: '错误',
    outdated: '过期'
  };
  return statusMap[status] || status;
};

const getSyncStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    synced: '已同步',
    pending: '待同步',
    conflict: '冲突',
    failed: '失败',
    outdated: '过期'
  };
  return statusMap[status] || status;
};

const getConflictTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    parameter_removed: '参数删除',
    type_mismatch: '类型不匹配',
    value_incompatible: '值不兼容',
    dependency_missing: '依赖缺失',
    custom_override: '自定义覆盖'
  };
  return typeMap[type] || type;
};

const refreshInstances = () => {
  instances.value = templateManager.instanceTracker.getTemplateInstances(props.template.id);
  conflicts.value = templateManager.getParameterConflicts(props.template.id);
};

const editTemplate = () => {
  // 编辑模板逻辑
  console.log('编辑模板:', props.template.name);
};

const duplicateTemplate = () => {
  // 复制模板逻辑
  const duplicated = {
    ...props.template,
    name: `${props.template.name} (副本)`,
    id: undefined
  };
  
  const result = templateManager.createTemplate(duplicated);
  if (result.success) {
    console.log(`✅ 模板已复制: ${duplicated.name}`);
    emit('updated', result.data!);
  }
};

const syncInstances = async () => {
  isSyncing.value = true;
  try {
    const result = await templateManager.syncTemplateParameters(props.template.id);
    if (result.success) {
      console.log(`✅ 实例同步完成: ${props.template.name}`);
      refreshInstances();
    } else {
      console.error(`❌ 实例同步失败: ${result.errors?.join(', ')}`);
    }
  } catch (error) {
    console.error('实例同步失败:', error);
  } finally {
    isSyncing.value = false;
  }
};

const exportTemplate = () => {
  // 导出模板逻辑
  const config = templateManager.configExporter.exportTemplate(props.template);
  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${props.template.name}.json`;
  a.click();
  URL.revokeObjectURL(url);
  
  console.log(`📤 模板已导出: ${props.template.name}`);
};

const deleteTemplate = () => {
  if (confirm(`确定要删除模板 "${props.template.name}" 吗？`)) {
    const result = templateManager.deleteTemplate(props.template.id);
    if (result.success) {
      console.log(`✅ 模板已删除: ${props.template.name}`);
      emit('close');
    } else {
      console.error(`❌ 模板删除失败: ${result.errors?.join(', ')}`);
    }
  }
};

const resolveConflict = async (conflict: any, resolution: 'use_template' | 'keep_instance') => {
  try {
    const success = await templateManager.resolveParameterConflict(conflict.id, resolution);
    if (success) {
      console.log(`✅ 冲突已解决: ${conflict.parameterName}`);
      refreshInstances();
    } else {
      console.error(`❌ 冲突解决失败: ${conflict.parameterName}`);
    }
  } catch (error) {
    console.error('冲突解决失败:', error);
  }
};

// 生命周期
onMounted(() => {
  refreshInstances();
});
</script>

<style scoped>
.template-detail-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
}

.sidebar-overlay {
  flex: 1;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.sidebar-content {
  width: 400px;
  background: #1a202c;
  border-left: 1px solid #4a5568;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 头部样式 */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.template-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4a5568;
  border-radius: 12px;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.template-title h2 {
  margin: 0 0 0.25rem 0;
  color: #f7fafc;
  font-size: 1.25rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-meta {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.template-type {
  color: #63b3ed;
  background: rgba(99, 179, 237, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.template-version {
  color: #9ae6b4;
  background: rgba(154, 230, 180, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid #4a5568;
  border-radius: 6px;
  color: #a0aec0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

/* 主体内容样式 */
.sidebar-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.info-section {
  margin-bottom: 2rem;
}

.section-title {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.conflict-title {
  color: #fc8181;
}

.refresh-btn {
  background: transparent;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  font-size: 0.875rem;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #4a5568;
  color: #f7fafc;
}

/* 信息网格样式 */
.info-grid {
  display: grid;
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  color: #a0aec0;
  font-size: 0.875rem;
  font-weight: 500;
}

.info-value {
  color: #e2e8f0;
  font-size: 0.875rem;
  word-break: break-word;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: #4a5568;
  color: #e2e8f0;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.stat-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-value {
  color: #f7fafc;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.125rem;
}

.stat-label {
  color: #a0aec0;
  font-size: 0.75rem;
}

/* 参数列表样式 */
.parameters-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.parameter-item {
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.param-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.param-name {
  color: #f7fafc;
  font-weight: 600;
  font-size: 0.875rem;
}

.param-type {
  background: #4a5568;
  color: #63b3ed;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.param-required {
  background: #e53e3e;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.param-description {
  color: #a0aec0;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.param-default {
  color: #a0aec0;
  font-size: 0.75rem;
}

.param-default code {
  background: #4a5568;
  color: #9ae6b4;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: monospace;
}

/* 实例列表样式 */
.instances-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.instance-item {
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.instance-item.status-active {
  border-left: 3px solid #38a169;
}

.instance-item.status-error {
  border-left: 3px solid #e53e3e;
}

.instance-item.status-outdated {
  border-left: 3px solid #ed8936;
}

.instance-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.instance-id {
  color: #63b3ed;
  font-family: monospace;
  font-size: 0.75rem;
  background: #2a4365;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
}

.instance-scene {
  color: #9ae6b4;
  font-size: 0.75rem;
  background: #2d5016;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
}

.instance-status {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-left: auto;
  font-size: 0.75rem;
  color: #a0aec0;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #38a169;
}

.instance-item.status-error .status-dot {
  background: #e53e3e;
}

.instance-item.status-outdated .status-dot {
  background: #ed8936;
}

.instance-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #a0aec0;
  margin-bottom: 0.5rem;
}

.instance-overrides details {
  font-size: 0.75rem;
}

.instance-overrides summary {
  color: #a0aec0;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.overrides-content {
  background: #1a202c;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #4a5568;
}

.override-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.override-key {
  color: #63b3ed;
  font-weight: 500;
}

.override-value {
  color: #e2e8f0;
  font-family: monospace;
}

/* 冲突列表样式 */
.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.conflict-item {
  padding: 1rem;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
}

.conflict-item.severity-critical {
  border-left: 3px solid #e53e3e;
}

.conflict-item.severity-high {
  border-left: 3px solid #f56565;
}

.conflict-item.severity-medium {
  border-left: 3px solid #ed8936;
}

.conflict-item.severity-low {
  border-left: 3px solid #ecc94b;
}

.conflict-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.conflict-param {
  color: #f7fafc;
  font-weight: 600;
  font-size: 0.875rem;
}

.conflict-type {
  background: #4a5568;
  color: #f6ad55;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.conflict-description {
  color: #a0aec0;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.conflict-actions {
  display: flex;
  gap: 0.5rem;
}

.resolve-btn {
  padding: 0.375rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.resolve-btn.template {
  background: #3182ce;
  color: white;
}

.resolve-btn.template:hover {
  background: #2b6cb0;
}

.resolve-btn.instance {
  background: #38a169;
  color: white;
}

.resolve-btn.instance:hover {
  background: #2f855a;
}

/* 底部操作样式 */
.sidebar-footer {
  padding: 1.5rem;
  background: #2d3748;
  border-top: 1px solid #4a5568;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-group {
  display: flex;
  gap: 0.75rem;
}

.action-group.danger {
  border-top: 1px solid #4a5568;
  padding-top: 1rem;
}

.action-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.action-btn.primary {
  background: #3182ce;
  color: white;
}

.action-btn.primary:hover {
  background: #2b6cb0;
}

.action-btn.secondary {
  background: #4a5568;
  color: #e2e8f0;
}

.action-btn.secondary:hover {
  background: #718096;
}

.action-btn.sync {
  background: #38a169;
  color: white;
}

.action-btn.sync:hover:not(:disabled) {
  background: #2f855a;
}

.action-btn.export {
  background: #805ad5;
  color: white;
}

.action-btn.export:hover {
  background: #6b46c1;
}

.action-btn.danger {
  background: #e53e3e;
  color: white;
}

.action-btn.danger:hover {
  background: #c53030;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-content {
    width: 100vw;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-group {
    flex-direction: column;
  }
}

/* 滚动条样式 */
.sidebar-body::-webkit-scrollbar {
  width: 6px;
}

.sidebar-body::-webkit-scrollbar-track {
  background: #2d3748;
}

.sidebar-body::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.sidebar-body::-webkit-scrollbar-thumb:hover {
  background: #63b3ed;
}
</style>
