<template>
  <div class="template-selector">
    <div class="selector-header">
      <div class="current-selection">
        <input
          v-model="selectedPath"
          type="text"
          :placeholder="placeholder"
          class="path-input"
          @focus="showDropdown = true"
          @blur="handleBlur"
        />
        <button @click="toggleDropdown" class="dropdown-btn">
          {{ showDropdown ? '▲' : '▼' }}
        </button>
      </div>
      <div class="action-buttons">
        <button @click="openTemplateManager" class="manage-btn" title="管理模板">
          🔧
        </button>
        <button @click="previewTemplate" class="preview-btn" title="预览模板" :disabled="!selectedTemplate">
          👁️
        </button>
      </div>
    </div>

    <!-- 下拉选择面板 -->
    <div v-if="showDropdown" class="dropdown-panel">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索模板..."
          class="search-input"
          @input="filterTemplates"
        />
      </div>

      <!-- 类型过滤 -->
      <div class="type-filters">
        <button
          v-for="type in availableTypes"
          :key="type"
          @click="toggleTypeFilter(type)"
          :class="['type-filter', { active: activeTypeFilters.includes(type) }]"
        >
          {{ getTypeDisplayName(type) }}
        </button>
      </div>

      <!-- 模板列表 -->
      <div class="template-list">
        <div
          v-for="category in filteredCategories"
          :key="category"
          class="category-group"
        >
          <div class="category-header">
            <span class="category-icon">📁</span>
            <span class="category-name">{{ category }}</span>
            <span class="template-count">({{ getCategoryCount(category) }})</span>
          </div>
          
          <div class="category-templates">
            <div
              v-for="template in getCategoryTemplates(category)"
              :key="template.id"
              @click="selectTemplate(template)"
              :class="['template-option', { selected: selectedTemplate?.id === template.id }]"
            >
              <span class="template-icon">{{ getTemplateIcon(template.type) }}</span>
              <div class="template-info">
                <div class="template-name">{{ template.name }}</div>
                <div class="template-path">{{ template.path }}</div>
                <div class="template-description">{{ template.description }}</div>
              </div>
              <div class="template-meta">
                <span class="usage-count">{{ template.metadata.usage }}次</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTemplates.length === 0" class="empty-state">
          <p>没有找到匹配的模板</p>
          <button @click="createNewTemplate" class="create-btn">
            ➕ 创建新模板
          </button>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <button @click="clearSelection" class="clear-btn">清除选择</button>
        <button @click="applyTemplate" class="apply-btn" :disabled="!selectedTemplate">
          应用模板
        </button>
      </div>
    </div>

    <!-- 模板预览弹窗 -->
    <div v-if="showPreview" class="preview-modal" @click="closePreview">
      <div class="preview-content" @click.stop>
        <div class="preview-header">
          <h3>{{ selectedTemplate?.name }}</h3>
          <button @click="closePreview" class="close-btn">✕</button>
        </div>
        
        <div class="preview-body">
          <div class="template-details">
            <div class="detail-item">
              <label>类型:</label>
              <span>{{ getTypeDisplayName(selectedTemplate?.type) }}</span>
            </div>
            <div class="detail-item">
              <label>分类:</label>
              <span>{{ selectedTemplate?.metadata.category }}</span>
            </div>
            <div class="detail-item">
              <label>路径:</label>
              <code>{{ selectedTemplate?.path }}</code>
            </div>
          </div>
          
          <div class="template-data">
            <label>模板数据:</label>
            <pre class="data-display">{{ formatTemplateData(selectedTemplate?.data) }}</pre>
          </div>
          
          <div class="template-usage">
            <label>引用示例:</label>
            <code class="usage-example">
              { "$ref": "{{ selectedTemplate?.path }}" }
            </code>
            <button @click="copyReference" class="copy-btn">复制</button>
          </div>
        </div>
        
        <div class="preview-footer">
          <button @click="applyTemplate" class="apply-btn">应用此模板</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { TemplateManager, Template, TemplateType } from '../services/TemplateManager';

// Props
interface Props {
  modelValue?: string;
  placeholder?: string;
  allowedTypes?: TemplateType[];
  category?: string;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择模板...',
  allowedTypes: () => Object.values(TemplateType),
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'template-selected': [template: Template];
  'template-applied': [template: Template];
  'open-manager': [];
}>();

// 组件状态
const showDropdown = ref(false);
const showPreview = ref(false);
const searchQuery = ref('');
const activeTypeFilters = ref<TemplateType[]>([]);
const selectedTemplate = ref<Template | null>(null);
const selectedPath = ref(props.modelValue || '');

// 模板管理器
const templateManager = TemplateManager.getInstance();

// 计算属性
const allTemplates = computed(() => {
  let templates = templateManager.registry.getAll();
  
  // 按允许的类型过滤
  if (props.allowedTypes.length > 0) {
    templates = templates.filter(t => props.allowedTypes.includes(t.type));
  }
  
  // 按分类过滤
  if (props.category) {
    templates = templates.filter(t => t.metadata.category === props.category);
  }
  
  return templates;
});

const filteredTemplates = computed(() => {
  let templates = allTemplates.value;
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    templates = templates.filter(t => 
      t.name.toLowerCase().includes(query) ||
      t.description.toLowerCase().includes(query) ||
      t.path.toLowerCase().includes(query) ||
      t.metadata.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }
  
  // 类型过滤
  if (activeTypeFilters.value.length > 0) {
    templates = templates.filter(t => activeTypeFilters.value.includes(t.type));
  }
  
  return templates;
});

const filteredCategories = computed(() => {
  const categories = new Set(filteredTemplates.value.map(t => t.metadata.category));
  return Array.from(categories).sort();
});

const availableTypes = computed(() => {
  const types = new Set(allTemplates.value.map(t => t.type));
  return Array.from(types);
});

// 方法
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
};

const handleBlur = () => {
  // 延迟关闭，允许点击下拉选项
  setTimeout(() => {
    showDropdown.value = false;
  }, 200);
};

const filterTemplates = () => {
  // 搜索逻辑已在计算属性中处理
};

const toggleTypeFilter = (type: TemplateType) => {
  const index = activeTypeFilters.value.indexOf(type);
  if (index > -1) {
    activeTypeFilters.value.splice(index, 1);
  } else {
    activeTypeFilters.value.push(type);
  }
};

const getCategoryCount = (category: string): number => {
  return filteredTemplates.value.filter(t => t.metadata.category === category).length;
};

const getCategoryTemplates = (category: string): Template[] => {
  return filteredTemplates.value.filter(t => t.metadata.category === category);
};

const selectTemplate = (template: Template) => {
  selectedTemplate.value = template;
  selectedPath.value = template.path;
  showDropdown.value = false;
  
  emit('update:modelValue', template.path);
  emit('template-selected', template);
};

const clearSelection = () => {
  selectedTemplate.value = null;
  selectedPath.value = '';
  showDropdown.value = false;
  
  emit('update:modelValue', '');
};

const applyTemplate = () => {
  if (selectedTemplate.value) {
    emit('template-applied', selectedTemplate.value);
    showDropdown.value = false;
    showPreview.value = false;
  }
};

const previewTemplate = () => {
  if (selectedTemplate.value) {
    showPreview.value = true;
  }
};

const closePreview = () => {
  showPreview.value = false;
};

const openTemplateManager = () => {
  emit('open-manager');
};

const createNewTemplate = () => {
  emit('open-manager');
};

const copyReference = () => {
  if (selectedTemplate.value) {
    const reference = `{ "$ref": "${selectedTemplate.value.path}" }`;
    navigator.clipboard.writeText(reference);
    console.log('引用已复制到剪贴板');
  }
};

const getTypeDisplayName = (type?: TemplateType): string => {
  if (!type) return '';
  const names: Record<TemplateType, string> = {
    [TemplateType.MESH]: '网格',
    [TemplateType.STYLE]: '样式',
    [TemplateType.CAMERA]: '相机',
    [TemplateType.ACTION]: '动作',
    [TemplateType.LABEL]: '标签',
    [TemplateType.POSITION]: '位置',
    [TemplateType.INTERACTION]: '交互',
  };
  return names[type] || type;
};

const getTemplateIcon = (type: TemplateType): string => {
  const icons: Record<TemplateType, string> = {
    [TemplateType.MESH]: '🏗️',
    [TemplateType.STYLE]: '🎨',
    [TemplateType.CAMERA]: '📷',
    [TemplateType.ACTION]: '⚡',
    [TemplateType.LABEL]: '🏷️',
    [TemplateType.POSITION]: '📍',
    [TemplateType.INTERACTION]: '🎭',
  };
  return icons[type] || '📄';
};

const formatTemplateData = (data: any): string => {
  if (!data) return '';
  return JSON.stringify(data, null, 2);
};

// 监听器
watch(() => props.modelValue, (newValue) => {
  selectedPath.value = newValue || '';
  
  // 根据路径查找对应的模板
  if (newValue) {
    const template = allTemplates.value.find(t => t.path === newValue);
    selectedTemplate.value = template || null;
  } else {
    selectedTemplate.value = null;
  }
});

// 生命周期
onMounted(() => {
  // 初始化选中的模板
  if (props.modelValue) {
    const template = allTemplates.value.find(t => t.path === props.modelValue);
    selectedTemplate.value = template || null;
  }
});
</script>

<style scoped>
.template-selector {
  position: relative;
  width: 100%;
}

.selector-header {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.current-selection {
  display: flex;
  flex: 1;
  border: 1px solid #ced4da;
  border-radius: 4px;
  overflow: hidden;
}

.path-input {
  flex: 1;
  padding: 0.5rem;
  border: none;
  outline: none;
  font-size: 0.875rem;
  font-family: monospace;
}

.dropdown-btn {
  padding: 0.5rem;
  border: none;
  background: #f8f9fa;
  cursor: pointer;
  border-left: 1px solid #ced4da;
  font-size: 0.75rem;
}

.dropdown-btn:hover {
  background: #e9ecef;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.manage-btn, .preview-btn {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 0.875rem;
}

.manage-btn:hover, .preview-btn:hover {
  background: #f8f9fa;
}

.preview-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdown-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-bar {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
}

.search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.875rem;
}

.type-filters {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #e9ecef;
  flex-wrap: wrap;
}

.type-filter {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s;
}

.type-filter:hover {
  background: #f8f9fa;
}

.type-filter.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.template-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.category-group {
  margin-bottom: 1rem;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
}

.category-icon {
  margin-right: 0.5rem;
}

.category-name {
  flex: 1;
}

.template-count {
  color: #6c757d;
  font-size: 0.75rem;
}

.category-templates {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.template-option {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.template-option:hover {
  background: #f8f9fa;
  border-color: #ced4da;
}

.template-option.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.template-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.template-path {
  font-family: monospace;
  font-size: 0.75rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.template-description {
  font-size: 0.75rem;
  color: #6c757d;
  line-height: 1.3;
}

.template-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.usage-count {
  font-size: 0.75rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.create-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  border: 1px solid #28a745;
  border-radius: 4px;
  background: #28a745;
  color: white;
  cursor: pointer;
}

.create-btn:hover {
  background: #218838;
}

.quick-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.clear-btn, .apply-btn {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 0.875rem;
}

.apply-btn {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.apply-btn:hover {
  background: #0056b3;
}

.apply-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.clear-btn:hover {
  background: #f8f9fa;
}

.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.preview-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.preview-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.close-btn {
  padding: 0.25rem 0.5rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1.25rem;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e9ecef;
}

.preview-body {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.template-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.detail-item label {
  font-weight: 500;
  width: 80px;
  color: #495057;
}

.detail-item code {
  background: #f8f9fa;
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
  font-size: 0.75rem;
}

.template-data {
  margin-bottom: 1rem;
}

.template-data label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.data-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.75rem;
  font-size: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.template-usage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.template-usage label {
  font-weight: 500;
  color: #495057;
}

.usage-example {
  flex: 1;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.75rem;
}

.copy-btn {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 0.75rem;
}

.copy-btn:hover {
  background: #f8f9fa;
}

.preview-footer {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  text-align: right;
}

.preview-footer .apply-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
}

.preview-footer .apply-btn:hover {
  background: #0056b3;
}
</style>
