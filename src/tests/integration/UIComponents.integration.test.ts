/**
 * UIComponents.integration.test.ts
 * UI组件集成测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import { nextTick } from 'vue';
import EnhancedTemplatePanel from '../../components/EnhancedTemplatePanel.vue';
import TemplateCard from '../../components/TemplateCard.vue';
import NotificationContainer from '../../components/NotificationContainer.vue';
import { TemplateManager } from '../../services/TemplateManager';
import { notificationService } from '../../services/NotificationService';
import { TestUtils, createTestTemplate } from '../setup';

// Mock dependencies
vi.mock('../../services/TemplateManager');
vi.mock('../../services/NotificationService');

describe('UI组件集成测试', () => {
  let templateManager: any;

  beforeEach(() => {
    // 重置模拟
    vi.clearAllMocks();
    
    // 创建模拟的TemplateManager
    templateManager = {
      getAllTemplates: vi.fn(() => []),
      getUsageStats: vi.fn(() => ({ totalTemplates: 0, totalInstances: 0 })),
      getInstanceStats: vi.fn(() => ({ total: 0 })),
      getParameterConflicts: vi.fn(() => []),
      createTemplate: vi.fn(() => ({ success: true, data: createTestTemplate() })),
      deleteTemplate: vi.fn(() => ({ success: true })),
      duplicateTemplate: vi.fn(() => ({ success: true, data: createTestTemplate() })),
      syncTemplateParameters: vi.fn(() => Promise.resolve({ success: true })),
      instanceTracker: {
        getTemplateInstances: vi.fn(() => [])
      },
      configExporter: {
        exportTemplate: vi.fn(() => ({}))
      }
    };

    (TemplateManager.getInstance as any).mockReturnValue(templateManager);
    
    TestUtils.cleanup();
  });

  afterEach(() => {
    TestUtils.cleanup();
  });

  describe('EnhancedTemplatePanel', () => {
    let wrapper: VueWrapper<any>;

    beforeEach(() => {
      wrapper = mount(EnhancedTemplatePanel, {
        global: {
          stubs: {
            TemplateCard: true,
            NodeToTemplateDialog: true,
            SimpleParameterSyncPanel: true,
            TemplateDetailSidebar: true
          }
        }
      });
    });

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount();
      }
    });

    it('应该正确渲染面板结构', () => {
      expect(wrapper.find('.enhanced-template-panel').exists()).toBe(true);
      expect(wrapper.find('.panel-header').exists()).toBe(true);
      expect(wrapper.find('.filter-bar').exists()).toBe(true);
      expect(wrapper.find('.template-content').exists()).toBe(true);
      expect(wrapper.find('.status-bar').exists()).toBe(true);
    });

    it('应该显示正确的标题和统计信息', async () => {
      const testTemplates = TestUtils.createTestTemplates(5);
      templateManager.getAllTemplates.mockReturnValue(testTemplates);
      
      await wrapper.vm.refreshTemplates();
      await nextTick();

      expect(wrapper.find('.panel-title').text()).toContain('模板库');
      expect(wrapper.find('.template-count').text()).toContain('5 个模板');
    });

    it('应该支持搜索功能', async () => {
      const testTemplates = TestUtils.createTestTemplates(10);
      testTemplates[0].name = '特殊搜索模板';
      templateManager.getAllTemplates.mockReturnValue(testTemplates);
      
      await wrapper.vm.refreshTemplates();
      await nextTick();

      // 输入搜索关键词
      const searchInput = wrapper.find('.search-input');
      await searchInput.setValue('特殊搜索');
      await nextTick();

      // 验证过滤结果
      expect(wrapper.vm.filteredTemplates).toHaveLength(1);
      expect(wrapper.vm.filteredTemplates[0].name).toBe('特殊搜索模板');
    });

    it('应该支持类型过滤', async () => {
      const testTemplates = TestUtils.createTestTemplates(6);
      templateManager.getAllTemplates.mockReturnValue(testTemplates);
      
      await wrapper.vm.refreshTemplates();
      await nextTick();

      // 选择类型过滤
      const typeSelect = wrapper.find('select[v-model="selectedType"]');
      await typeSelect.setValue('MESH');
      await nextTick();

      // 验证过滤结果
      const filteredTemplates = wrapper.vm.filteredTemplates;
      filteredTemplates.forEach((template: any) => {
        expect(template.type).toBe('MESH');
      });
    });

    it('应该支持视图模式切换', async () => {
      // 默认应该是网格视图
      expect(wrapper.vm.viewMode).toBe('grid');
      expect(wrapper.find('.view-grid').exists()).toBe(true);

      // 切换到列表视图
      const listViewBtn = wrapper.find('.view-btn[title="列表视图"]');
      await listViewBtn.trigger('click');
      await nextTick();

      expect(wrapper.vm.viewMode).toBe('list');
      expect(wrapper.find('.view-list').exists()).toBe(true);
    });

    it('应该正确处理创建模板对话框', async () => {
      expect(wrapper.vm.showCreateDialog).toBe(false);

      // 点击创建按钮
      const createBtn = wrapper.find('.create-btn');
      await createBtn.trigger('click');
      await nextTick();

      expect(wrapper.vm.showCreateDialog).toBe(true);
    });

    it('应该正确处理同步面板', async () => {
      expect(wrapper.vm.showSyncPanel).toBe(false);

      // 点击同步状态按钮
      const syncBtn = wrapper.find('.sync-status-btn');
      await syncBtn.trigger('click');
      await nextTick();

      expect(wrapper.vm.showSyncPanel).toBe(true);
    });

    it('应该正确处理分页', async () => {
      const testTemplates = TestUtils.createTestTemplates(25); // 超过默认页面大小
      templateManager.getAllTemplates.mockReturnValue(testTemplates);
      
      await wrapper.vm.refreshTemplates();
      await nextTick();

      expect(wrapper.vm.totalPages).toBeGreaterThan(1);
      expect(wrapper.vm.paginatedTemplates.length).toBeLessThanOrEqual(12); // 默认页面大小

      // 测试翻页
      expect(wrapper.vm.currentPage).toBe(1);
      
      // 模拟点击下一页（需要实际的分页按钮）
      wrapper.vm.currentPage = 2;
      await nextTick();

      expect(wrapper.vm.currentPage).toBe(2);
    });
  });

  describe('TemplateCard', () => {
    let wrapper: VueWrapper<any>;
    let testTemplate: any;

    beforeEach(() => {
      testTemplate = createTestTemplate({
        name: '测试模板卡片',
        metadata: {
          ...createTestTemplate().metadata,
          usageCount: 10,
          isBuiltIn: false
        }
      });

      wrapper = mount(TemplateCard, {
        props: {
          template: testTemplate,
          viewMode: 'grid'
        }
      });
    });

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount();
      }
    });

    it('应该正确显示模板信息', () => {
      expect(wrapper.find('.template-name').text()).toBe('测试模板卡片');
      expect(wrapper.find('.template-type').text()).toContain('网格');
      expect(wrapper.find('.stat-value').text()).toContain('10');
    });

    it('应该支持不同的视图模式', async () => {
      // 网格视图
      expect(wrapper.classes()).toContain('view-grid');

      // 切换到列表视图
      await wrapper.setProps({ viewMode: 'list' });
      await nextTick();

      expect(wrapper.classes()).toContain('view-list');
    });

    it('应该正确处理选择事件', async () => {
      await wrapper.trigger('click');
      
      expect(wrapper.emitted('select')).toBeTruthy();
      expect(wrapper.emitted('select')![0]).toEqual([testTemplate]);
    });

    it('应该正确处理编辑事件', async () => {
      const editBtn = wrapper.find('.edit-btn');
      await editBtn.trigger('click');
      
      expect(wrapper.emitted('edit')).toBeTruthy();
      expect(wrapper.emitted('edit')![0]).toEqual([testTemplate]);
    });

    it('应该正确处理拖拽事件', async () => {
      const mockDragEvent = TestUtils.createMockDragEvent('dragstart', { template: testTemplate });
      
      await wrapper.trigger('dragstart', mockDragEvent);
      
      expect(wrapper.emitted('dragStart')).toBeTruthy();
      expect(wrapper.emitted('dragStart')![0][0]).toBe(testTemplate);
    });

    it('应该显示操作菜单', async () => {
      expect(wrapper.vm.showMenu).toBe(false);

      // 点击菜单按钮
      const menuBtn = wrapper.find('.menu-btn');
      await menuBtn.trigger('click');
      await nextTick();

      expect(wrapper.vm.showMenu).toBe(true);
      expect(wrapper.find('.action-menu').exists()).toBe(true);
    });

    it('应该正确处理菜单操作', async () => {
      // 显示菜单
      wrapper.vm.showMenu = true;
      await nextTick();

      // 测试复制操作
      const duplicateBtn = wrapper.find('.menu-item:contains("复制模板")');
      if (duplicateBtn.exists()) {
        await duplicateBtn.trigger('click');
        expect(wrapper.emitted('duplicate')).toBeTruthy();
      }
    });
  });

  describe('NotificationContainer', () => {
    let wrapper: VueWrapper<any>;

    beforeEach(() => {
      // 清除所有通知
      notificationService.clear();
      
      wrapper = mount(NotificationContainer);
    });

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount();
      }
      notificationService.clear();
    });

    it('应该正确渲染通知容器', () => {
      expect(wrapper.find('.notification-container').exists()).toBe(true);
      expect(wrapper.find('.notifications-list').exists()).toBe(true);
    });

    it('应该显示通知', async () => {
      // 添加通知
      notificationService.success({
        title: '测试通知',
        message: '这是一个测试通知'
      });

      await nextTick();

      const notifications = wrapper.findAll('.notification');
      expect(notifications).toHaveLength(1);
      expect(notifications[0].find('.notification-title').text()).toBe('测试通知');
      expect(notifications[0].find('.notification-message').text()).toBe('这是一个测试通知');
    });

    it('应该显示不同类型的通知', async () => {
      // 添加不同类型的通知
      notificationService.success({ title: '成功通知' });
      notificationService.error({ title: '错误通知' });
      notificationService.warning({ title: '警告通知' });
      notificationService.info({ title: '信息通知' });

      await nextTick();

      const notifications = wrapper.findAll('.notification');
      expect(notifications).toHaveLength(4);

      expect(wrapper.find('.notification-success').exists()).toBe(true);
      expect(wrapper.find('.notification-error').exists()).toBe(true);
      expect(wrapper.find('.notification-warning').exists()).toBe(true);
      expect(wrapper.find('.notification-info').exists()).toBe(true);
    });

    it('应该支持关闭通知', async () => {
      // 添加通知
      const notificationId = notificationService.success({
        title: '可关闭通知',
        persistent: true
      });

      await nextTick();

      expect(wrapper.findAll('.notification')).toHaveLength(1);

      // 点击关闭按钮
      const closeBtn = wrapper.find('.notification-close');
      await closeBtn.trigger('click');
      await nextTick();

      expect(wrapper.findAll('.notification')).toHaveLength(0);
    });

    it('应该支持通知操作', async () => {
      const actionHandler = vi.fn();
      
      // 添加带操作的通知
      notificationService.warning({
        title: '确认操作',
        message: '是否继续？',
        persistent: true,
        actions: [
          { label: '确认', action: actionHandler, style: 'primary' },
          { label: '取消', action: vi.fn(), style: 'secondary' }
        ]
      });

      await nextTick();

      const actionBtns = wrapper.findAll('.action-btn');
      expect(actionBtns).toHaveLength(2);

      // 点击确认按钮
      await actionBtns[0].trigger('click');
      
      expect(actionHandler).toHaveBeenCalled();
      
      // 通知应该被移除
      await nextTick();
      expect(wrapper.findAll('.notification')).toHaveLength(0);
    });

    it('应该正确显示进度条', async () => {
      // 添加非持久通知
      notificationService.info({
        title: '临时通知',
        duration: 3000
      });

      await nextTick();

      const notification = wrapper.find('.notification');
      expect(notification.exists()).toBe(true);
      
      const progressBar = notification.find('.notification-progress');
      expect(progressBar.exists()).toBe(true);
      
      // 验证动画样式
      const style = progressBar.attributes('style');
      expect(style).toContain('animation-duration: 3000ms');
    });
  });

  describe('组件交互集成', () => {
    it('应该正确处理模板面板和卡片的交互', async () => {
      const testTemplates = TestUtils.createTestTemplates(3);
      templateManager.getAllTemplates.mockReturnValue(testTemplates);

      const panelWrapper = mount(EnhancedTemplatePanel, {
        global: {
          stubs: {
            NodeToTemplateDialog: true,
            SimpleParameterSyncPanel: true,
            TemplateDetailSidebar: true
          }
        }
      });

      await panelWrapper.vm.refreshTemplates();
      await nextTick();

      // 模拟选择模板
      const template = testTemplates[0];
      panelWrapper.vm.selectTemplate(template);
      await nextTick();

      expect(panelWrapper.vm.selectedTemplate).toBe(template);

      panelWrapper.unmount();
    });

    it('应该正确处理通知和操作的集成', async () => {
      const testTemplate = createTestTemplate();
      templateManager.getAllTemplates.mockReturnValue([testTemplate]);

      const panelWrapper = mount(EnhancedTemplatePanel, {
        global: {
          stubs: {
            TemplateCard: true,
            NodeToTemplateDialog: true,
            SimpleParameterSyncPanel: true,
            TemplateDetailSidebar: true
          }
        }
      });

      const notificationWrapper = mount(NotificationContainer);

      // 模拟删除操作
      await panelWrapper.vm.deleteTemplate(testTemplate);
      await nextTick();

      // 验证通知显示
      const notifications = notificationWrapper.findAll('.notification');
      expect(notifications.length).toBeGreaterThan(0);

      panelWrapper.unmount();
      notificationWrapper.unmount();
    });
  });

  describe('响应式设计测试', () => {
    it('应该在不同屏幕尺寸下正确显示', async () => {
      const wrapper = mount(EnhancedTemplatePanel, {
        global: {
          stubs: {
            TemplateCard: true,
            NodeToTemplateDialog: true,
            SimpleParameterSyncPanel: true,
            TemplateDetailSidebar: true
          }
        }
      });

      // 模拟移动端屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768
      });

      window.dispatchEvent(new Event('resize'));
      await nextTick();

      // 验证响应式样式
      expect(wrapper.find('.enhanced-template-panel').exists()).toBe(true);

      wrapper.unmount();
    });
  });

  describe('无障碍访问测试', () => {
    it('应该支持键盘导航', async () => {
      const wrapper = mount(EnhancedTemplatePanel, {
        global: {
          stubs: {
            TemplateCard: true,
            NodeToTemplateDialog: true,
            SimpleParameterSyncPanel: true,
            TemplateDetailSidebar: true
          }
        }
      });

      // 测试Tab键导航
      const searchInput = wrapper.find('.search-input');
      expect(searchInput.attributes('tabindex')).not.toBe('-1');

      // 测试Enter键操作
      await searchInput.trigger('keydown.enter');

      // 验证焦点管理
      expect(document.activeElement).toBeDefined();

      wrapper.unmount();
    });

    it('应该提供正确的ARIA标签', () => {
      const wrapper = mount(TemplateCard, {
        props: {
          template: createTestTemplate(),
          viewMode: 'grid'
        }
      });

      // 验证ARIA属性
      const card = wrapper.find('.template-card');
      expect(card.attributes('role')).toBeDefined();

      const actionBtns = wrapper.findAll('.action-btn');
      actionBtns.forEach(btn => {
        expect(btn.attributes('aria-label') || btn.attributes('title')).toBeDefined();
      });

      wrapper.unmount();
    });
  });
});
