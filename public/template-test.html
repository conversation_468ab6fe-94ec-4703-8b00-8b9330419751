<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #545b62;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 模板系统测试页面</h1>
        <p>这个页面用于测试ddd-flow的模板系统功能。</p>
        
        <div class="test-section">
            <h3>📊 系统状态检查</h3>
            <button class="button" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="button secondary" onclick="clearOutput('status-output')">清除输出</button>
            <div id="status-output" class="output"></div>
        </div>
        
        <div class="test-section">
            <h3>🏗️ 模板管理器测试</h3>
            <button class="button" onclick="testTemplateManager()">测试模板管理器</button>
            <button class="button" onclick="createSampleTemplates()">创建示例模板</button>
            <button class="button" onclick="listAllTemplates()">列出所有模板</button>
            <button class="button secondary" onclick="clearOutput('manager-output')">清除输出</button>
            <div id="manager-output" class="output"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 模板搜索和过滤测试</h3>
            <input type="text" id="search-input" placeholder="输入搜索关键词..." style="padding: 8px; margin-right: 10px; width: 200px;">
            <button class="button" onclick="searchTemplates()">搜索模板</button>
            <button class="button" onclick="filterByType()">按类型过滤</button>
            <button class="button secondary" onclick="clearOutput('search-output')">清除输出</button>
            <div id="search-output" class="output"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 模板解析测试</h3>
            <input type="text" id="resolve-input" placeholder="输入模板引用路径..." style="padding: 8px; margin-right: 10px; width: 300px;">
            <button class="button" onclick="resolveTemplate()">解析模板</button>
            <button class="button" onclick="testComplexResolve()">测试复杂解析</button>
            <button class="button secondary" onclick="clearOutput('resolve-output')">清除输出</button>
            <div id="resolve-output" class="output"></div>
        </div>
        
        <div class="test-section">
            <h3>✅ 模板验证测试</h3>
            <button class="button" onclick="testValidation()">测试模板验证</button>
            <button class="button" onclick="testInvalidTemplate()">测试无效模板</button>
            <button class="button secondary" onclick="clearOutput('validation-output')">清除输出</button>
            <div id="validation-output" class="output"></div>
        </div>
        
        <div class="test-section">
            <h3>🎨 UI组件测试</h3>
            <button class="button" onclick="testTemplatePanel()">测试模板面板</button>
            <button class="button" onclick="testTemplateSelector()">测试模板选择器</button>
            <button class="button" onclick="openMainApp()">打开主应用</button>
            <button class="button secondary" onclick="clearOutput('ui-output')">清除输出</button>
            <div id="ui-output" class="output"></div>
        </div>
    </div>

    <script type="module">
        // 导入模板系统
        let TemplateManager, TemplateType;
        
        try {
            const module = await import('/src/services/TemplateManager.js');
            TemplateManager = module.TemplateManager;
            TemplateType = module.TemplateType;
            
            // 获取模板管理器实例
            window.templateManager = TemplateManager.getInstance();
            
            showStatus('success', '✅ 模板系统加载成功');
            
            // 初始化一些示例数据
            initializeSampleData();
            
        } catch (error) {
            showStatus('error', '❌ 模板系统加载失败: ' + error.message);
            console.error('模板系统加载失败:', error);
        }
        
        // 初始化示例数据
        function initializeSampleData() {
            if (!window.templateManager) return;
            
            // 创建一些示例模板
            const sampleTemplates = [
                {
                    name: "测试网格模板",
                    type: TemplateType.MESH,
                    data: ["mesh1", "mesh2", "mesh3"],
                    metadata: {
                        category: "测试",
                        tags: ["test", "mesh"],
                        author: "system",
                        usage: 0,
                        dependencies: []
                    }
                },
                {
                    name: "测试样式模板",
                    type: TemplateType.STYLE,
                    data: {
                        backgroundColor: "#ff6b35",
                        color: "#ffffff",
                        borderRadius: "8px"
                    },
                    metadata: {
                        category: "测试",
                        tags: ["test", "style"],
                        author: "system",
                        usage: 0,
                        dependencies: []
                    }
                }
            ];
            
            sampleTemplates.forEach(template => {
                window.templateManager.createTemplate(template);
            });
            
            console.log('示例模板初始化完成');
        }
        
        // 全局函数
        window.TemplateManager = TemplateManager;
        window.TemplateType = TemplateType;
        
        // 工具函数
        window.showStatus = function(type, message) {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            document.body.insertBefore(statusDiv, document.body.firstChild);
            
            setTimeout(() => {
                statusDiv.remove();
            }, 5000);
        };
        
        window.appendOutput = function(elementId, content) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent += content + '\n';
                element.scrollTop = element.scrollHeight;
            }
        };
        
        window.clearOutput = function(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = '';
            }
        };
        
        // 测试函数
        window.checkSystemStatus = function() {
            const output = 'status-output';
            clearOutput(output);
            
            appendOutput(output, '🔍 检查系统状态...');
            appendOutput(output, `模板管理器: ${window.templateManager ? '✅ 可用' : '❌ 不可用'}`);
            
            if (window.templateManager) {
                const stats = window.templateManager.getUsageStats();
                appendOutput(output, `总模板数: ${stats.totalTemplates}`);
                appendOutput(output, `按类型统计: ${JSON.stringify(stats.byType, null, 2)}`);
                appendOutput(output, `按分类统计: ${JSON.stringify(stats.byCategory, null, 2)}`);
            }
            
            appendOutput(output, '✅ 状态检查完成');
        };
        
        window.testTemplateManager = function() {
            const output = 'manager-output';
            clearOutput(output);
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            appendOutput(output, '🧪 测试模板管理器...');
            
            // 测试获取所有模板
            const allTemplates = window.templateManager.registry.getAll();
            appendOutput(output, `当前模板数量: ${allTemplates.length}`);
            
            // 测试按类型获取
            Object.values(TemplateType).forEach(type => {
                const templates = window.templateManager.registry.getByType(type);
                appendOutput(output, `${type} 类型模板: ${templates.length} 个`);
            });
            
            appendOutput(output, '✅ 模板管理器测试完成');
        };
        
        window.createSampleTemplates = function() {
            const output = 'manager-output';
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            appendOutput(output, '➕ 创建示例模板...');
            
            const newTemplate = {
                name: `动态模板_${Date.now()}`,
                type: TemplateType.MESH,
                data: [`dynamic_${Date.now()}_1`, `dynamic_${Date.now()}_2`],
                metadata: {
                    category: "动态",
                    tags: ["dynamic", "test"],
                    author: "user",
                    usage: 0,
                    dependencies: []
                }
            };
            
            const result = window.templateManager.createTemplate(newTemplate);
            if (result.success) {
                appendOutput(output, `✅ 创建成功: ${result.template.name}`);
            } else {
                appendOutput(output, `❌ 创建失败: ${result.errors.join(', ')}`);
            }
        };
        
        window.listAllTemplates = function() {
            const output = 'manager-output';
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            const templates = window.templateManager.registry.getAll();
            appendOutput(output, `📋 所有模板列表 (${templates.length} 个):`);
            
            templates.forEach((template, index) => {
                appendOutput(output, `  ${index + 1}. ${template.name} (${template.type}) - ${template.description}`);
            });
        };
        
        window.searchTemplates = function() {
            const output = 'search-output';
            const query = document.getElementById('search-input').value;
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            if (!query) {
                appendOutput(output, '⚠️ 请输入搜索关键词');
                return;
            }
            
            const results = window.templateManager.registry.search(query);
            appendOutput(output, `🔍 搜索 "${query}" 结果: ${results.length} 个模板`);
            
            results.forEach((template, index) => {
                appendOutput(output, `  ${index + 1}. ${template.name} (${template.type})`);
            });
        };
        
        window.filterByType = function() {
            const output = 'search-output';
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            appendOutput(output, '🏷️ 按类型过滤结果:');
            
            Object.values(TemplateType).forEach(type => {
                const templates = window.templateManager.registry.getByType(type);
                appendOutput(output, `  ${type}: ${templates.length} 个模板`);
                templates.forEach(template => {
                    appendOutput(output, `    - ${template.name}`);
                });
            });
        };
        
        window.resolveTemplate = function() {
            const output = 'resolve-output';
            const refPath = document.getElementById('resolve-input').value;
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            if (!refPath) {
                appendOutput(output, '⚠️ 请输入模板引用路径');
                return;
            }
            
            try {
                const resolved = window.templateManager.resolver.resolve(refPath);
                appendOutput(output, `🔧 解析 "${refPath}":`);
                appendOutput(output, JSON.stringify(resolved, null, 2));
            } catch (error) {
                appendOutput(output, `❌ 解析失败: ${error.message}`);
            }
        };
        
        window.testComplexResolve = function() {
            const output = 'resolve-output';
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            // 测试复杂引用
            const complexRef = {
                $ref: "mesh.测试网格模板",
                $extend: ["extra1", "extra2"]
            };
            
            try {
                const resolved = window.templateManager.resolver.resolve(complexRef);
                appendOutput(output, '🔧 复杂引用解析测试:');
                appendOutput(output, `输入: ${JSON.stringify(complexRef, null, 2)}`);
                appendOutput(output, `输出: ${JSON.stringify(resolved, null, 2)}`);
            } catch (error) {
                appendOutput(output, `❌ 复杂解析失败: ${error.message}`);
            }
        };
        
        window.testValidation = function() {
            const output = 'validation-output';
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            const validTemplate = {
                name: "验证测试模板",
                type: TemplateType.MESH,
                data: ["valid1", "valid2"],
                metadata: {
                    category: "验证",
                    tags: ["validation"],
                    author: "test",
                    usage: 0,
                    dependencies: []
                }
            };
            
            const validation = window.templateManager.validator.validate(validTemplate);
            appendOutput(output, '✅ 有效模板验证:');
            appendOutput(output, `结果: ${validation.valid ? '通过' : '失败'}`);
            if (!validation.valid) {
                appendOutput(output, `错误: ${validation.errors.join(', ')}`);
            }
        };
        
        window.testInvalidTemplate = function() {
            const output = 'validation-output';
            
            if (!window.templateManager) {
                appendOutput(output, '❌ 模板管理器不可用');
                return;
            }
            
            const invalidTemplate = {
                name: "", // 无效：空名称
                type: "invalid_type", // 无效：错误类型
                data: null, // 无效：空数据
                metadata: {} // 无效：缺少必需字段
            };
            
            const validation = window.templateManager.validator.validate(invalidTemplate);
            appendOutput(output, '❌ 无效模板验证:');
            appendOutput(output, `结果: ${validation.valid ? '通过' : '失败'}`);
            appendOutput(output, `错误: ${validation.errors.join(', ')}`);
        };
        
        window.testTemplatePanel = function() {
            const output = 'ui-output';
            appendOutput(output, '🎨 测试模板面板...');
            appendOutput(output, '💡 请在主应用中查看模板面板功能');
            appendOutput(output, '建议: 点击"打开主应用"按钮');
        };
        
        window.testTemplateSelector = function() {
            const output = 'ui-output';
            appendOutput(output, '🎯 测试模板选择器...');
            appendOutput(output, '💡 请在主应用的属性面板中查看模板选择器');
            appendOutput(output, '建议: 在主应用中选择一个事件节点');
        };
        
        window.openMainApp = function() {
            window.open('/', '_blank');
        };
    </script>
</body>
</html>
