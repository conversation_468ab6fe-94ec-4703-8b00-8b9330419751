/**
 * TemplateSystem.integration.test.ts
 * 模板系统集成测试
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { TemplateManager } from '../../services/TemplateManager';
import { NodeSelector } from '../../services/NodeSelector';
import { TemplateType } from '../../types/TemplateTypes';
import { TestUtils, createTestTemplate, createTestInstance } from '../setup';

describe('模板系统集成测试', () => {
  let templateManager: TemplateManager;
  let nodeSelector: NodeSelector;

  beforeEach(() => {
    // 重置所有单例实例
    (TemplateManager as any).instance = null;
    (NodeSelector as any).instance = null;
    
    templateManager = TemplateManager.getInstance();
    nodeSelector = NodeSelector.getInstance();
    
    TestUtils.cleanup();
  });

  afterEach(() => {
    TestUtils.cleanup();
  });

  describe('端到端工作流程', () => {
    it('应该完成完整的节点转模板工作流程', async () => {
      // 1. 创建模拟节点
      const mockNode = {
        id: 'test-node-1',
        data: {
          nodeType: 'action-highlight',
          displayName: '高亮动作',
          color: [1, 0, 0],
          duration: 1000,
          intensity: 1.5
        },
        attrs: {
          body: { fill: '#ffffff', stroke: '#000000' },
          text: { text: '测试节点', fontSize: 14 }
        },
        position: { x: 100, y: 200 },
        size: { width: 120, height: 80 },
        getData: () => mockNode.data,
        getAttrs: () => mockNode.attrs,
        getPosition: () => mockNode.position,
        getSize: () => mockNode.size
      };

      // 2. 选择节点
      const selectionResult = nodeSelector.selectNode(mockNode);
      expect(selectionResult.success).toBe(true);
      expect(nodeSelector.getSelectedNode()).toBe(mockNode);

      // 3. 分析节点
      const analysis = nodeSelector.analyzeNode(mockNode);
      expect(analysis.success).toBe(true);
      expect(analysis.nodeInfo!.type).toBe('action-highlight');
      expect(analysis.parameters!.color).toEqual([1, 0, 0]);

      // 4. 生成模板建议
      const suggestions = nodeSelector.generateTemplateSuggestions(mockNode);
      expect(suggestions.length).toBeGreaterThan(0);
      
      const actionSuggestion = suggestions.find(s => s.type === 'ACTION');
      expect(actionSuggestion).toBeDefined();

      // 5. 创建模板
      const template = createTestTemplate({
        name: actionSuggestion!.name,
        type: TemplateType.ACTION,
        data: analysis.parameters,
        definition: {
          parameters: [
            { name: 'color', type: 'array', defaultValue: [1, 0, 0], required: true },
            { name: 'duration', type: 'number', defaultValue: 1000, required: true },
            { name: 'intensity', type: 'number', defaultValue: 1.5, required: false }
          ],
          dependencies: [],
          validation: { required: ['color', 'duration'], constraints: {} }
        }
      });

      const createResult = templateManager.createTemplate(template);
      expect(createResult.success).toBe(true);
      expect(createResult.data!.id).toBe(template.id);

      // 6. 实例化模板
      const instanceResult = await templateManager.instantiateTemplate(
        template.id,
        'test-scene',
        'test-node',
        { color: [0, 1, 0], duration: 2000, intensity: 2.0 }
      );

      expect(instanceResult.success).toBe(true);
      expect(instanceResult.data!.templateId).toBe(template.id);
      expect(instanceResult.data!.parameters.color).toEqual([0, 1, 0]);

      // 7. 验证实例追踪
      const instances = templateManager.instanceTracker.getTemplateInstances(template.id);
      expect(instances).toHaveLength(1);
      expect(instances[0].id).toBe(instanceResult.data!.id);
    });

    it('应该完成完整的参数同步工作流程', async () => {
      // 1. 创建原始模板
      const originalTemplate = createTestTemplate({
        definition: {
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ffffff', required: false },
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
          ],
          dependencies: [],
          validation: { required: ['size'], constraints: {} }
        }
      });

      templateManager.createTemplate(originalTemplate);

      // 2. 创建多个实例
      const instances = [];
      for (let i = 0; i < 3; i++) {
        const instanceResult = await templateManager.instantiateTemplate(
          originalTemplate.id,
          'test-scene',
          `test-node-${i}`,
          { color: `#ff000${i}`, size: 1.0 + i }
        );
        expect(instanceResult.success).toBe(true);
        instances.push(instanceResult.data!);
      }

      // 3. 更新模板定义
      const updatedTemplate = {
        ...originalTemplate,
        definition: {
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#00ff00', required: false }, // 默认值变更
            { name: 'size', type: 'number', defaultValue: 2.0, required: true }, // 默认值变更
            { name: 'opacity', type: 'number', defaultValue: 1.0, required: false } // 新增参数
          ],
          dependencies: [],
          validation: { required: ['size'], constraints: {} }
        }
      };

      const updateResult = templateManager.updateTemplate(originalTemplate.id, updatedTemplate);
      expect(updateResult.success).toBe(true);

      // 4. 检测参数变更
      const changes = templateManager.parameterSyncManager.detectParameterChanges(
        originalTemplate,
        updateResult.data!
      );
      expect(changes.length).toBeGreaterThan(0);

      const addedChanges = changes.filter(c => c.changeType === 'added');
      const modifiedChanges = changes.filter(c => c.changeType === 'modified');
      
      expect(addedChanges).toHaveLength(1);
      expect(addedChanges[0].parameterName).toBe('opacity');
      expect(modifiedChanges.length).toBeGreaterThan(0);

      // 5. 检测冲突
      const conflicts = templateManager.getParameterConflicts(originalTemplate.id);
      // 在这个场景中，应该没有严重冲突，因为只是默认值变更和参数添加
      expect(conflicts.length).toBe(0);

      // 6. 同步实例
      const syncResult = await templateManager.syncTemplateParameters(originalTemplate.id);
      expect(syncResult.success).toBe(true);

      // 7. 验证同步结果
      const updatedInstances = templateManager.instanceTracker.getTemplateInstances(originalTemplate.id);
      expect(updatedInstances).toHaveLength(3);
      
      updatedInstances.forEach(instance => {
        expect(instance.parameters.opacity).toBe(1.0); // 新参数应该有默认值
        expect(instance.syncStatus).toBe('synced');
      });
    });

    it('应该完成完整的批量操作工作流程', async () => {
      // 1. 创建多个模板
      const templates = TestUtils.createTestTemplates(5);
      const createdTemplates = [];

      for (const template of templates) {
        const result = templateManager.createTemplate(template);
        expect(result.success).toBe(true);
        createdTemplates.push(result.data!);
      }

      // 2. 为每个模板创建实例
      const allInstances = [];
      for (const template of createdTemplates) {
        const instances = TestUtils.createTestInstances(template.id, 3);
        for (const instance of instances) {
          templateManager.instanceTracker.trackInstance(instance);
          allInstances.push(instance);
        }
      }

      expect(allInstances).toHaveLength(15); // 5个模板 × 3个实例

      // 3. 批量更新模板
      const templateIds = createdTemplates.map(t => t.id);
      const batchUpdateResult = await templateManager.batchUpdateManager.batchUpdateTemplates(
        templateIds,
        { 
          metadata: { 
            tags: [...createdTemplates[0].metadata.tags, 'batch-updated'] 
          } 
        }
      );

      expect(batchUpdateResult.success).toBe(true);
      expect(batchUpdateResult.successCount).toBe(5);
      expect(batchUpdateResult.failureCount).toBe(0);

      // 4. 验证批量更新结果
      for (const templateId of templateIds) {
        const template = templateManager.getTemplate(templateId);
        expect(template).toBeDefined();
        expect(template!.metadata.tags).toContain('batch-updated');
      }

      // 5. 批量同步实例
      const batchSyncResult = await templateManager.batchSyncTemplates(templateIds, {
        strategy: 'conservative'
      });

      expect(batchSyncResult.success).toBe(true);
      expect(batchSyncResult.processedTemplates).toBe(5);

      // 6. 验证统计信息
      const stats = templateManager.getUsageStats();
      expect(stats.totalTemplates).toBe(5);
      expect(stats.totalInstances).toBe(15);

      const instanceStats = templateManager.getInstanceStats();
      expect(instanceStats.total).toBe(15);
      expect(instanceStats.byTemplate).toHaveProperty(templateIds[0]);
    });
  });

  describe('错误恢复和边界情况', () => {
    it('应该正确处理部分失败的批量操作', async () => {
      // 1. 创建混合的模板集合（有效和无效）
      const validTemplates = TestUtils.createTestTemplates(3);
      const invalidTemplate = {
        name: '无效模板',
        // 缺少必需字段
      } as any;

      const mixedTemplates = [...validTemplates, invalidTemplate];

      // 2. 尝试批量创建
      const results = [];
      for (const template of mixedTemplates) {
        const result = templateManager.createTemplate(template);
        results.push(result);
      }

      // 3. 验证部分成功
      const successResults = results.filter(r => r.success);
      const failureResults = results.filter(r => !r.success);

      expect(successResults).toHaveLength(3);
      expect(failureResults).toHaveLength(1);

      // 4. 验证系统状态一致性
      const allTemplates = templateManager.getAllTemplates();
      expect(allTemplates).toHaveLength(3); // 只有有效模板被创建
    });

    it('应该正确处理并发操作', async () => {
      const template = createTestTemplate();
      templateManager.createTemplate(template);

      // 并发创建实例
      const concurrentPromises = Array.from({ length: 10 }, (_, index) =>
        templateManager.instantiateTemplate(
          template.id,
          'test-scene',
          `concurrent-node-${index}`,
          { size: 1.0 + index * 0.1 }
        )
      );

      const results = await Promise.all(concurrentPromises);

      // 验证所有操作都成功
      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBe(10);

      // 验证实例追踪正确
      const instances = templateManager.instanceTracker.getTemplateInstances(template.id);
      expect(instances).toHaveLength(10);

      // 验证没有重复ID
      const instanceIds = instances.map(i => i.id);
      const uniqueIds = new Set(instanceIds);
      expect(uniqueIds.size).toBe(10);
    });

    it('应该正确处理存储限制', () => {
      // 模拟存储空间不足
      const originalSetItem = Storage.prototype.setItem;
      let callCount = 0;
      
      Storage.prototype.setItem = function(key: string, value: string) {
        callCount++;
        if (callCount > 3) {
          throw new Error('存储空间不足');
        }
        return originalSetItem.call(this, key, value);
      };

      try {
        // 尝试创建多个模板
        const templates = TestUtils.createTestTemplates(5);
        const results = templates.map(template => templateManager.createTemplate(template));

        // 验证前几个成功，后面的失败
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;

        expect(successCount).toBeLessThan(5);
        expect(failureCount).toBeGreaterThan(0);

        // 验证错误信息
        const failedResults = results.filter(r => !r.success);
        failedResults.forEach(result => {
          expect(result.errors).toContain('存储失败');
        });

      } finally {
        // 恢复原始方法
        Storage.prototype.setItem = originalSetItem;
      }
    });
  });

  describe('性能和扩展性', () => {
    it('应该在合理时间内处理大量数据', async () => {
      const startTime = Date.now();

      // 创建大量模板
      const templates = TestUtils.createTestTemplates(100);
      for (const template of templates) {
        templateManager.createTemplate(template);
      }

      // 为每个模板创建实例
      for (const template of templates) {
        const instances = TestUtils.createTestInstances(template.id, 5);
        for (const instance of instances) {
          templateManager.instanceTracker.trackInstance(instance);
        }
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // 验证性能（应该在合理时间内完成）
      expect(duration).toBeLessThan(5000); // 5秒内完成

      // 验证数据完整性
      const allTemplates = templateManager.getAllTemplates();
      expect(allTemplates).toHaveLength(100);

      const stats = templateManager.getInstanceStats();
      expect(stats.total).toBe(500); // 100个模板 × 5个实例
    });

    it('应该正确处理内存使用', () => {
      // 创建大量数据
      const templates = TestUtils.createTestTemplates(50);
      templates.forEach(template => {
        templateManager.createTemplate(template);
        
        const instances = TestUtils.createTestInstances(template.id, 10);
        instances.forEach(instance => {
          templateManager.instanceTracker.trackInstance(instance);
        });
      });

      // 获取性能指标
      const metrics = templateManager.getPerformanceMetrics();
      
      expect(metrics.templateCount).toBe(50);
      expect(metrics.instanceCount).toBe(500);
      expect(metrics.memoryUsage).toBeDefined();
      expect(metrics.memoryUsage.templates).toBeGreaterThan(0);
      expect(metrics.memoryUsage.instances).toBeGreaterThan(0);

      // 清理数据
      templateManager.clear();

      // 验证清理效果
      const metricsAfterClear = templateManager.getPerformanceMetrics();
      expect(metricsAfterClear.templateCount).toBe(0);
      expect(metricsAfterClear.instanceCount).toBe(0);
    });
  });

  describe('数据一致性', () => {
    it('应该维护模板和实例之间的一致性', async () => {
      const template = createTestTemplate();
      templateManager.createTemplate(template);

      // 创建实例
      const instanceResult = await templateManager.instantiateTemplate(
        template.id,
        'test-scene',
        'test-node',
        { size: 2.0 }
      );
      expect(instanceResult.success).toBe(true);

      // 更新模板
      const updatedTemplate = {
        ...template,
        name: '更新后的模板',
        version: '2.0.0'
      };

      const updateResult = templateManager.updateTemplate(template.id, updatedTemplate);
      expect(updateResult.success).toBe(true);

      // 验证实例仍然有效
      const instances = templateManager.instanceTracker.getTemplateInstances(template.id);
      expect(instances).toHaveLength(1);
      expect(instances[0].templateId).toBe(template.id);

      // 删除模板（应该失败，因为有活跃实例）
      const deleteResult = templateManager.deleteTemplate(template.id);
      expect(deleteResult.success).toBe(false);
      expect(deleteResult.errors).toContain('模板有活跃实例，无法删除');

      // 清理实例后删除模板
      templateManager.instanceTracker.untrackInstance(instances[0].id);
      
      const deleteResult2 = templateManager.deleteTemplate(template.id);
      expect(deleteResult2.success).toBe(true);
      expect(templateManager.getTemplate(template.id)).toBeUndefined();
    });

    it('应该正确处理级联删除', async () => {
      const template = createTestTemplate();
      templateManager.createTemplate(template);

      // 创建多个实例
      const instances = [];
      for (let i = 0; i < 3; i++) {
        const result = await templateManager.instantiateTemplate(
          template.id,
          'test-scene',
          `test-node-${i}`,
          { size: 1.0 + i }
        );
        instances.push(result.data!);
      }

      // 强制删除模板
      const deleteResult = templateManager.deleteTemplate(template.id, { force: true });
      expect(deleteResult.success).toBe(true);

      // 验证模板已删除
      expect(templateManager.getTemplate(template.id)).toBeUndefined();

      // 验证实例也被清理
      const remainingInstances = templateManager.instanceTracker.getTemplateInstances(template.id);
      expect(remainingInstances).toHaveLength(0);
    });
  });
});
