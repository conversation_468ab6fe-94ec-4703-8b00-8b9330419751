/**
 * NodeToTemplateFlow.test.ts
 * 节点转模板功能的集成测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { TemplateManager, TemplateType } from '../services/TemplateManager';
import { NodeSelector } from '../services/NodeSelector';
import { TemplateExtractor } from '../services/TemplateExtractor';

describe('节点转模板功能测试', () => {
  let templateManager: TemplateManager;
  let nodeSelector: NodeSelector;
  let templateExtractor: TemplateExtractor;

  beforeEach(() => {
    templateManager = TemplateManager.getInstance();
    nodeSelector = NodeSelector.getInstance();
    templateExtractor = new TemplateExtractor();
  });

  it('应该能够创建模拟节点', () => {
    const buttonNode = nodeSelector.createMockNode('button');
    
    expect(buttonNode).toBeDefined();
    expect(buttonNode.getData()).toBeDefined();
    expect(buttonNode.getData().label).toBe('示例按钮');
    expect(buttonNode.getData().backgroundColor).toBe('#3182ce');
  });

  it('应该能够从节点提取模板', () => {
    const buttonNode = nodeSelector.createMockNode('button');
    const template = templateExtractor.extractFromNode(buttonNode, '测试按钮模板');
    
    expect(template).toBeDefined();
    expect(template.name).toBe('测试按钮模板');
    expect(template.type).toBe(TemplateType.STYLE);
    expect(template.data).toBeDefined();
    expect(template.metadata?.sourceType).toBe('node-converted');
  });

  it('应该能够验证节点的模板兼容性', () => {
    const buttonNode = nodeSelector.createMockNode('button');
    const validation = nodeSelector.validateNodeForTemplate(buttonNode);
    
    expect(validation.valid).toBe(true);
    expect(validation.errors).toHaveLength(0);
  });

  it('应该能够获取节点显示信息', () => {
    const labelNode = nodeSelector.createMockNode('label');
    const displayInfo = nodeSelector.getNodeDisplayInfo(labelNode);
    
    expect(displayInfo.name).toBe('示例标签');
    expect(displayInfo.icon).toBe('📝');
    expect(displayInfo.description).toContain('文本');
  });

  it('应该能够完成完整的节点转模板流程', () => {
    // 1. 创建模拟节点
    const shapeNode = nodeSelector.createMockNode('shape');
    
    // 2. 设置为选中节点
    nodeSelector.setSelectedNode(shapeNode);
    
    // 3. 验证节点选择
    const selectedNode = nodeSelector.getSelectedNode();
    expect(selectedNode).toBe(shapeNode);
    
    // 4. 从节点创建模板
    const result = templateManager.createTemplateFromNode(
      shapeNode,
      '测试形状模板',
      '从形状节点创建的测试模板',
      '测试'
    );
    
    // 5. 验证模板创建结果
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data?.name).toBe('测试形状模板');
    expect(result.data?.metadata.category).toBe('测试');
    
    // 6. 验证模板已注册
    const registeredTemplate = templateManager.getTemplate(result.data!.id);
    expect(registeredTemplate).toBeDefined();
    expect(registeredTemplate?.name).toBe('测试形状模板');
  });

  it('应该能够处理不同类型的节点', () => {
    const nodeTypes: ('button' | 'label' | 'shape')[] = ['button', 'label', 'shape'];
    
    nodeTypes.forEach(nodeType => {
      const node = nodeSelector.createMockNode(nodeType);
      const template = templateExtractor.extractFromNode(node, `测试${nodeType}模板`);
      
      expect(template).toBeDefined();
      expect(template.name).toBe(`测试${nodeType}模板`);
      expect(template.metadata?.sourceType).toBe('node-converted');
      
      // 验证不同节点类型的特定属性
      switch (nodeType) {
        case 'button':
          expect(template.data.backgroundColor).toBeDefined();
          expect(template.data.onClick).toBeDefined();
          break;
        case 'label':
          expect(template.data.text).toBeDefined();
          expect(template.data.fontSize).toBeDefined();
          break;
        case 'shape':
          expect(template.data.fillColor).toBeDefined();
          expect(template.data.strokeColor).toBeDefined();
          break;
      }
    });
  });

  it('应该能够处理模板参数提取', () => {
    const buttonNode = nodeSelector.createMockNode('button');
    const template = templateExtractor.extractFromNode(buttonNode, '参数测试模板');
    
    expect(template.definition?.parameters).toBeDefined();
    expect(template.definition?.parameters.length).toBeGreaterThan(0);
    
    // 验证参数包含预期的属性
    const parameterNames = template.definition?.parameters.map(p => p.name) || [];
    expect(parameterNames).toContain('backgroundColor');
    expect(parameterNames).toContain('textColor');
    expect(parameterNames).toContain('fontSize');
  });

  it('应该能够检测模板依赖关系', () => {
    const nodeWithRef = {
      id: 'test-node',
      shape: 'rect',
      data: {
        style: { $ref: 'templates.1.styles.baseStyle' },
        label: '测试节点'
      },
      getData: function() { return this.data; },
      getAttrs: () => ({}),
      getSize: () => ({ width: 100, height: 50 }),
      getPosition: () => ({ x: 0, y: 0 })
    } as any;
    
    const template = templateExtractor.extractFromNode(nodeWithRef, '依赖测试模板');
    
    expect(template.metadata?.dependencies).toBeDefined();
    expect(template.metadata?.dependencies).toContain('templates.1.styles.baseStyle');
  });

  it('应该能够处理错误情况', () => {
    // 测试空节点
    const result1 = templateManager.createTemplateFromNode(
      null as any,
      '空节点测试',
      '测试空节点处理'
    );
    expect(result1.success).toBe(false);
    expect(result1.errors).toBeDefined();
    
    // 测试无效节点
    const invalidNode = {} as any;
    const result2 = templateManager.createTemplateFromNode(
      invalidNode,
      '无效节点测试',
      '测试无效节点处理'
    );
    expect(result2.success).toBe(false);
  });

  it('应该能够生成正确的配置路径', () => {
    const buttonNode = nodeSelector.createMockNode('button');
    const result = templateManager.createTemplateFromNode(
      buttonNode,
      '路径测试模板',
      '测试配置路径生成',
      '测试'
    );
    
    expect(result.success).toBe(true);
    expect(result.data?.path).toBeDefined();
    expect(result.data?.path).toMatch(/^templates\.\d+\.\w+\.\w+$/);
  });

  it('应该能够处理模板版本控制', () => {
    const buttonNode = nodeSelector.createMockNode('button');
    const result = templateManager.createTemplateFromNode(
      buttonNode,
      '版本测试模板',
      '测试版本控制',
      '测试'
    );
    
    expect(result.success).toBe(true);
    expect(result.data?.version).toBeDefined();
    expect(result.data?.createdAt).toBeDefined();
    expect(result.data?.updatedAt).toBeDefined();
  });
});

describe('节点选择器测试', () => {
  let nodeSelector: NodeSelector;

  beforeEach(() => {
    nodeSelector = NodeSelector.getInstance();
  });

  it('应该能够管理节点选择状态', () => {
    const node1 = nodeSelector.createMockNode('button');
    const node2 = nodeSelector.createMockNode('label');
    
    // 初始状态
    expect(nodeSelector.getSelectedNode()).toBeNull();
    
    // 选择第一个节点
    nodeSelector.setSelectedNode(node1);
    expect(nodeSelector.getSelectedNode()).toBe(node1);
    
    // 选择第二个节点
    nodeSelector.setSelectedNode(node2);
    expect(nodeSelector.getSelectedNode()).toBe(node2);
    
    // 清除选择
    nodeSelector.clearSelection();
    expect(nodeSelector.getSelectedNode()).toBeNull();
  });

  it('应该能够触发选择变化回调', () => {
    let callbackTriggered = false;
    let callbackNode = null;
    
    const callback = (node: any) => {
      callbackTriggered = true;
      callbackNode = node;
    };
    
    nodeSelector.onSelectionChange(callback);
    
    const testNode = nodeSelector.createMockNode('shape');
    nodeSelector.setSelectedNode(testNode);
    
    expect(callbackTriggered).toBe(true);
    expect(callbackNode).toBe(testNode);
    
    // 清理回调
    nodeSelector.removeSelectionListener(callback);
  });
});

describe('模板提取器测试', () => {
  let templateExtractor: TemplateExtractor;

  beforeEach(() => {
    templateExtractor = new TemplateExtractor();
  });

  it('应该能够正确识别模板类型', () => {
    const nodeSelector = NodeSelector.getInstance();
    
    // 测试样式类型识别
    const styleNode = nodeSelector.createMockNode('button');
    const styleTemplate = templateExtractor.extractFromNode(styleNode, '样式测试');
    expect(styleTemplate.type).toBe(TemplateType.STYLE);
    
    // 测试标签类型识别
    const labelNode = nodeSelector.createMockNode('label');
    const labelTemplate = templateExtractor.extractFromNode(labelNode, '标签测试');
    expect(labelTemplate.type).toBe(TemplateType.LABEL);
  });

  it('应该能够检查配置兼容性', () => {
    const nodeSelector = NodeSelector.getInstance();
    const buttonNode = nodeSelector.createMockNode('button');
    const template = templateExtractor.extractFromNode(buttonNode, '兼容性测试');
    
    const isCompatible = templateExtractor.checkConfigCompatibility(template);
    expect(isCompatible).toBe(true);
  });
});
