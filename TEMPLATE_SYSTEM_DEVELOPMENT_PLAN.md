# 模板系统重构开发计划 - 组件化设计

## 🎯 项目概述

将现有的静态模板系统重构为动态组件化模板系统，实现类似React组件的概念：
- **节点转模板**：任何画布上的节点都可以转换为可复用的模板
- **模板实例化**：从模板库拖拽创建节点实例
- **参数同步更新**：修改模板参数时，所有使用该模板的实例自动更新
- **版本控制**：支持模板版本管理和回滚
- **config.js兼容**：完全支持现有的 `public/config.js` 文件格式和 `$ref` 引用机制

## 📋 开发阶段规划

### 🏗️ 阶段1：核心架构设计 (预计2-3天)

#### 1.1 设计模板数据结构
```typescript
interface Template {
  id: string;
  name: string;
  version: string;
  type: TemplateType;
  description: string;

  // 模板定义 - 兼容config.js格式
  definition: {
    nodeType: string;
    properties: Record<string, any>;
    dependencies: string[];
    parameters: TemplateParameter[];
    configPath?: string; // 在config.js中的路径，如 "templates.1.styles.orangeGradient"
  };

  // 元数据
  metadata: {
    author: string;
    category: string;
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
    usage: number;
    sourceType: 'user-created' | 'config-imported'; // 区分用户创建和从config.js导入
  };

  // 版本信息
  versionHistory: TemplateVersion[];
}

interface TemplateInstance {
  id: string;
  templateId: string;
  templateVersion: string;
  nodeId: string;
  sceneId: string;

  // 实例特定参数（覆盖模板默认值）
  overrides: Record<string, any>;

  // 同步状态
  syncStatus: 'synced' | 'outdated' | 'conflict';
  lastSyncAt: Date;

  // config.js引用信息
  configRef?: {
    path: string; // 如 "templates.1.styles.orangeGradient"
    isReference: boolean; // 是否使用$ref引用
  };
}

// config.js引用解析器
interface ConfigReference {
  path: string; // 如 "templates.1.styles.orangeGradient"
  resolvedValue: any; // 解析后的实际值
  dependencies: string[]; // 依赖的其他引用路径
}
```

#### 1.2 设计模板注册表
```typescript
class TemplateRegistry {
  // 模板存储
  private templates: Map<string, Template>;
  private templatesByType: Map<TemplateType, Template[]>;
  private templatesByCategory: Map<string, Template[]>;

  // 实例追踪
  private instances: Map<string, TemplateInstance[]>;

  // config.js集成
  private configReferences: Map<string, ConfigReference>;

  // 方法
  importFromConfig(config: any): void;
  exportToConfig(): any;
  resolveReference(path: string): any;

  // 版本管理
  getTemplateVersion(templateId: string, version?: string): Template;
  createTemplateVersion(templateId: string): string;

  // 实例追踪
  getTemplateInstances(templateId: string): TemplateInstance[];
  updateInstances(templateId: string, changes: any): void;
}
```

- 支持模板版本管理
- 实例追踪和关联关系维护
- 依赖解析和循环依赖检测
- 批量操作和事务支持
- **config.js导入导出**：支持从config.js导入模板和导出到config.js
- **引用解析**：支持$ref引用机制

#### 1.3 设计模板生命周期管理
- 创建：从节点提取模板定义
- 更新：修改模板参数和结构
- 删除：安全删除（检查实例引用）
- 版本升级：向前兼容性处理

#### 1.4 设计实例同步机制
- 变更检测：属性、结构、依赖变化识别
- 冲突处理：实例自定义vs模板更新
- 回滚策略：支持撤销同步操作

### 🔄 阶段2：节点转模板功能 (预计3-4天)

#### 2.1 实现节点选择与分析
- 节点属性提取和分类
- 可模板化属性识别
- 依赖关系分析
- 参数化候选项检测

#### 2.2 实现模板提取器
```typescript
class TemplateExtractor {
  extractFromNode(node: Cell.Properties): TemplateDefinition {
    // 提取节点类型和基础属性
    // 识别可参数化的属性
    // 分析依赖关系
    // 生成模板定义
  }

  generateParameters(properties: any): TemplateParameter[] {
    // 自动识别可参数化的属性
    // 生成参数定义和默认值
  }

  generateConfigPath(template: Template): string {
    // 生成在config.js中的路径
    // 例如: templates.1.styles.customStyle1
  }

  checkConfigCompatibility(template: Template): boolean {
    // 检查模板是否兼容config.js格式
    // 验证所有属性是否可序列化
  }
}
```

#### 2.3 实现模板创建界面
- 模板基本信息设置（名称、分类、描述）
- 参数配置界面
- 预览功能
- 验证和保存

#### 2.4 实现模板保存机制
- 版本控制集成
- 冲突检测（同名模板）
- 自动生成模板ID和路径

### ⚙️ 阶段3：模板实例化系统 (预计4-5天)

#### 3.1 实现模板实例化引擎
```typescript
class TemplateInstantiator {
  instantiate(template: Template, parameters: Record<string, any>): Cell.Properties {
    // 参数替换和验证
    // 依赖解析
    // 节点属性生成
    // 实例注册
  }

  resolveParameters(template: Template, overrides: Record<string, any>): any {
    // 合并模板默认值和实例覆盖值
    // 参数验证和类型转换
  }

  generateConfigReference(instance: TemplateInstance): any {
    // 生成config.js兼容的引用格式
    // 支持$ref和$extend语法
  }

  applyConfigExtensions(baseConfig: any, extensions: any): any {
    // 应用config.js中的$extend扩展
  }
}
```

#### 3.2 实现实例追踪系统
- 实例与模板的关联关系维护
- 实例状态管理（同步状态、冲突状态）
- 实例生命周期事件

#### 3.3 实现拖拽创建功能
- 从模板库拖拽到画布
- 实时参数配置
- 位置和布局处理

#### 3.4 实现实例参数管理
- 参数覆盖机制
- 参数继承规则
- 参数验证和约束

### 🔄 阶段4：参数同步更新 (预计4-5天)

#### 4.1 实现模板变更检测
```typescript
class TemplateChangeDetector {
  detectChanges(oldTemplate: Template, newTemplate: Template): TemplateChanges {
    // 属性变更检测
    // 结构变更检测
    // 参数变更检测
    // 依赖变更检测
  }
  
  analyzeImpact(changes: TemplateChanges, instances: TemplateInstance[]): ImpactAnalysis {
    // 影响范围分析
    // 冲突预测
    // 更新策略建议
  }
}
```

#### 4.2 实现实例更新机制
- 增量更新：只更新变更的部分
- 完全更新：重新生成整个实例
- 批量更新：同时更新多个实例

#### 4.3 实现冲突解决策略
- 自动解决：简单冲突自动处理
- 用户选择：复杂冲突提供选项
- 保留自定义：保护用户的个性化设置

#### 4.4 实现更新通知机制
- 更新日志生成
- 变更预览
- 用户确认流程

### 🎨 阶段5：UI界面重构 (预计3-4天)

#### 5.1 重构模板面板界面
- 组件化模板展示
- 模板分类和搜索
- 版本历史查看
- 使用统计显示

#### 5.2 实现模板编辑器
- 可视化参数编辑
- 实时预览
- 参数验证
- 版本比较

#### 5.3 实现实例管理界面
- 实例列表和状态
- 关联关系可视化
- 批量操作
- 同步状态管理

#### 5.4 实现右键菜单功能
- 节点右键菜单扩展
- "转为模板"操作
- "更新模板"操作
- "断开模板连接"操作

### 🧪 阶段6：测试与优化 (预计2-3天)

#### 6.1 单元测试开发
- 模板提取器测试
- 实例化引擎测试
- 同步机制测试
- 冲突解决测试

#### 6.2 集成测试开发
- 端到端工作流测试
- 多场景协作测试
- 性能压力测试

#### 6.3 性能优化
- 大量模板和实例的性能优化
- 内存使用优化
- 渲染性能优化

#### 6.4 用户测试与反馈
- 用户体验测试
- 功能完整性验证
- 反馈收集和改进

#### 6.5 文档编写
- 开发文档
- 用户指南
- API文档

## 🔧 技术实现要点

### 数据结构设计
- 使用TypeScript强类型定义
- 支持JSON序列化和反序列化
- 版本兼容性处理

### 性能考虑
- 懒加载模板定义
- 实例更新的批量处理
- 变更检测的增量算法

### 用户体验
- 操作的可撤销性
- 清晰的状态反馈
- 直观的可视化界面

## 📅 时间估算

| 阶段 | 预计时间 | 关键里程碑 |
|------|----------|------------|
| 阶段1 | 2-3天 | 核心架构设计完成 |
| 阶段2 | 3-4天 | 节点转模板功能可用 |
| 阶段3 | 4-5天 | 模板实例化系统完成 |
| 阶段4 | 4-5天 | 参数同步更新功能完成 |
| 阶段5 | 3-4天 | UI界面重构完成 |
| 阶段6 | 2-3天 | 测试和优化完成 |
| **总计** | **18-24天** | **完整系统交付** |

## 🎯 成功标准

### 功能完整性
- ✅ 任意节点可转换为模板
- ✅ 模板可实例化为节点
- ✅ 模板修改时实例自动同步
- ✅ 支持参数覆盖和冲突处理

### 性能要求
- ✅ 支持1000+模板和10000+实例
- ✅ 实例同步延迟<100ms
- ✅ UI响应时间<50ms

### 用户体验
- ✅ 操作流程直观易懂
- ✅ 错误处理友好
- ✅ 支持撤销和回滚

---

**下一步行动**：开始阶段1的核心架构设计，首先定义新的数据结构和接口。
