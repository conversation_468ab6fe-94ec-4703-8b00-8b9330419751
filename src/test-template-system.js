/**
 * 模板系统集成测试
 */

import { TemplateManager } from './services/TemplateManager.js';

// 创建测试配置，模拟真实的config.js结构
const testTemplateConfig = {
  templates: {
    "1": {
      meshes: {
        buildingLevels: [
          "电气楼五层",
          "NA_LSZSC_QITA04", 
          "NA_LSZSC_QITA03",
          "二层外墙",
          "NA_LSZ_YC_MOD10"
        ],
        gisDoors: [
          "DOOR_NA_LSZ_GIS_MOD105",
          "DOOR_NA_LSZ_GIS_MOD108",
          "DOOR_NA_LSZ_GIS_MOD109"
        ],
        deviceMeshes: [
          "DEVICE_001",
          "DEVICE_002", 
          "DEVICE_003"
        ]
      },
      styles: {
        orangeGradient: {
          background: "linear-gradient(45deg, #ff6b35, #f7931e)",
          color: "#ffffff",
          borderRadius: "8px",
          padding: "8px 16px"
        },
        blueGradient: {
          background: "linear-gradient(45deg, #1e3c72, #2a5298)",
          color: "#ffffff", 
          borderRadius: "8px",
          padding: "8px 16px"
        },
        transparent: {
          background: "rgba(255, 255, 255, 0.1)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
          borderRadius: "8px"
        }
      },
      cameras: {
        building: {
          position: [50, 30, 50],
          target: [0, 0, 0],
          fov: 60,
          near: 0.1,
          far: 1000
        },
        "scene-1-3": {
          position: [30, 20, 30],
          target: [0, 5, 0],
          fov: 45,
          near: 0.1,
          far: 500
        },
        "close-up": {
          position: [10, 10, 10],
          target: [0, 0, 0],
          fov: 75,
          near: 0.1,
          far: 100
        }
      },
      actions: {
        hoverHighlight: {
          type: "hover",
          highlight: {
            color: [1, 1, 0],
            duration: 300,
            intensity: 1.2
          }
        },
        clickRedHighlight: {
          type: "click",
          highlight: {
            color: [1, 0, 0],
            duration: 500,
            intensity: 1.5
          }
        },
        doubleClickFocus: {
          type: "doubleClick",
          callback: "CameraService.focusToDevice",
          parameters: {
            duration: 1000,
            easing: "easeInOutQuad"
          }
        }
      },
      labels: {
        floorLabel: {
          style: "default",
          position: "top",
          offset: [0, 20, 0],
          fontSize: 14,
          color: "#ffffff",
          background: "rgba(0, 0, 0, 0.7)"
        },
        deviceLabel: {
          style: "compact",
          position: "center",
          offset: [0, 0, 0],
          fontSize: 12,
          color: "#333333",
          background: "rgba(255, 255, 255, 0.9)"
        },
        roomLabel: {
          style: "large",
          position: "bottom",
          offset: [0, -10, 0],
          fontSize: 16,
          color: "#ffffff",
          background: "rgba(0, 100, 200, 0.8)"
        }
      },
      positions: {
        centerPoint: [0, 0, 0],
        topView: [0, 100, 0],
        frontView: [0, 0, 50],
        sideView: [50, 0, 0],
        cornerView: [30, 30, 30]
      }
    }
  }
};

/**
 * 测试模板系统的基础功能
 */
function testTemplateSystemBasics() {
  console.log("🧪 开始测试模板系统基础功能...");
  
  const templateManager = TemplateManager.getInstance();
  
  try {
    // 1. 测试模板加载
    console.log("\n📥 测试模板加载...");
    templateManager.loadFromConfig(testTemplateConfig);
    
    const allTemplates = templateManager.registry.getAll();
    console.log(`✅ 成功加载 ${allTemplates.length} 个模板`);
    
    // 2. 测试按类型获取模板
    console.log("\n📋 测试按类型获取模板...");
    Object.values(templateManager.registry.templateTypes || {}).forEach(type => {
      const templates = templateManager.registry.getByType(type);
      console.log(`  ${type}: ${templates.length} 个模板`);
    });
    
    // 3. 测试按分类获取模板
    console.log("\n📁 测试按分类获取模板...");
    const stats = templateManager.getUsageStats();
    Object.entries(stats.byCategory).forEach(([category, count]) => {
      console.log(`  ${category}: ${count} 个模板`);
    });
    
    // 4. 测试模板搜索
    console.log("\n🔍 测试模板搜索...");
    const searchResults = templateManager.registry.search("building");
    console.log(`搜索 "building" 找到 ${searchResults.length} 个模板:`);
    searchResults.forEach(template => {
      console.log(`  - ${template.name} (${template.type})`);
    });
    
    // 5. 测试模板解析
    console.log("\n🔧 测试模板解析...");
    const meshTemplate = templateManager.registry.get("meshes.buildingLevels");
    if (meshTemplate) {
      console.log(`模板 "${meshTemplate.name}" 数据:`, meshTemplate.data);
      
      // 测试引用解析
      const resolvedData = templateManager.resolver.resolve("templates.1.meshes.buildingLevels");
      console.log("解析后的数据:", resolvedData);
      
      // 测试带扩展的引用解析
      const extendedData = templateManager.resolver.resolve({
        $ref: "templates.1.meshes.buildingLevels",
        $extend: ["额外网格1", "额外网格2"]
      });
      console.log("扩展后的数据:", extendedData);
    }
    
    // 6. 测试模板验证
    console.log("\n✅ 测试模板验证...");
    const validTemplate = {
      name: "测试模板",
      type: "mesh",
      data: ["test1", "test2"],
      metadata: {
        category: "测试分类",
        tags: ["test"],
        author: "test",
        usage: 0,
        dependencies: []
      }
    };
    
    const validation = templateManager.validator.validate(validTemplate);
    console.log(`模板验证结果: ${validation.valid ? '通过' : '失败'}`);
    if (!validation.valid) {
      console.log("验证错误:", validation.errors);
    }
    
    console.log("\n🎉 模板系统基础功能测试完成！");
    return true;
    
  } catch (error) {
    console.error("❌ 模板系统基础功能测试失败:", error);
    return false;
  }
}

/**
 * 测试模板系统的高级功能
 */
function testTemplateSystemAdvanced() {
  console.log("🚀 开始测试模板系统高级功能...");
  
  const templateManager = TemplateManager.getInstance();
  
  try {
    // 1. 测试模板创建
    console.log("\n➕ 测试模板创建...");
    const newTemplate = {
      name: "自定义网格模板",
      type: "mesh",
      data: ["custom1", "custom2", "custom3"],
      metadata: {
        category: "自定义",
        tags: ["custom", "test"],
        author: "user",
        usage: 0,
        dependencies: []
      }
    };
    
    const createResult = templateManager.createTemplate(newTemplate);
    if (createResult.success) {
      console.log(`✅ 成功创建模板: ${createResult.template.name}`);
    } else {
      console.log("❌ 模板创建失败:", createResult.errors);
    }
    
    // 2. 测试模板更新
    console.log("\n📝 测试模板更新...");
    if (createResult.success) {
      const updateResult = templateManager.updateTemplate(createResult.template.id, {
        description: "更新后的描述",
        data: ["updated1", "updated2", "updated3", "updated4"]
      });
      
      if (updateResult.success) {
        console.log(`✅ 成功更新模板: ${updateResult.template.name}`);
      } else {
        console.log("❌ 模板更新失败:", updateResult.errors);
      }
    }
    
    // 3. 测试复杂引用解析
    console.log("\n🔗 测试复杂引用解析...");
    const complexRef = {
      $ref: "templates.1.styles.orangeGradient",
      $extend: {
        fontSize: "16px",
        fontWeight: "bold"
      },
      $override: {
        color: "#000000"
      }
    };
    
    const resolvedComplex = templateManager.resolver.resolve(complexRef);
    console.log("复杂引用解析结果:", resolvedComplex);
    
    // 4. 测试引用验证
    console.log("\n🔍 测试引用验证...");
    const validRef = "templates.1.meshes.buildingLevels";
    const invalidRef = "templates.1.nonexistent.template";
    
    console.log(`引用 "${validRef}" 有效性: ${templateManager.resolver.validateReference(validRef)}`);
    console.log(`引用 "${invalidRef}" 有效性: ${templateManager.resolver.validateReference(invalidRef)}`);
    
    // 5. 测试模板依赖分析
    console.log("\n📊 测试模板依赖分析...");
    const templatesWithDeps = templateManager.registry.getAll().filter(t => t.metadata.dependencies.length > 0);
    console.log(`有依赖的模板数量: ${templatesWithDeps.length}`);
    templatesWithDeps.forEach(template => {
      console.log(`  ${template.name}: 依赖 ${template.metadata.dependencies.join(', ')}`);
    });
    
    // 6. 测试模板删除
    console.log("\n🗑️ 测试模板删除...");
    if (createResult.success) {
      const deleteResult = templateManager.deleteTemplate(createResult.template.id);
      console.log(`删除模板结果: ${deleteResult ? '成功' : '失败'}`);
    }
    
    console.log("\n🎉 模板系统高级功能测试完成！");
    return true;
    
  } catch (error) {
    console.error("❌ 模板系统高级功能测试失败:", error);
    return false;
  }
}

/**
 * 测试模板系统与UI的集成
 */
function testTemplateUIIntegration() {
  console.log("🎨 开始测试模板系统UI集成...");
  
  try {
    // 1. 测试模板分类展示
    console.log("\n📁 测试模板分类展示...");
    const templateManager = TemplateManager.getInstance();
    const stats = templateManager.getUsageStats();
    
    console.log("模板分类统计:");
    Object.entries(stats.byCategory).forEach(([category, count]) => {
      const templates = templateManager.registry.getByCategory(category);
      console.log(`  📂 ${category} (${count}个):`);
      templates.forEach(template => {
        console.log(`    📄 ${template.name} - ${template.description}`);
      });
    });
    
    // 2. 测试模板选择器数据
    console.log("\n🎯 测试模板选择器数据...");
    const meshTemplates = templateManager.registry.getByType("mesh");
    console.log(`网格模板选择器可用选项 (${meshTemplates.length}个):`);
    meshTemplates.forEach(template => {
      console.log(`  - ${template.name}: ${template.path}`);
    });
    
    // 3. 测试模板预览数据
    console.log("\n👁️ 测试模板预览数据...");
    const sampleTemplate = templateManager.registry.get("meshes.buildingLevels");
    if (sampleTemplate) {
      console.log("模板预览信息:");
      console.log(`  名称: ${sampleTemplate.name}`);
      console.log(`  类型: ${sampleTemplate.type}`);
      console.log(`  分类: ${sampleTemplate.metadata.category}`);
      console.log(`  路径: ${sampleTemplate.path}`);
      console.log(`  数据大小: ${JSON.stringify(sampleTemplate.data).length} 字符`);
      console.log(`  标签: ${sampleTemplate.metadata.tags.join(', ')}`);
      console.log(`  引用示例: { "$ref": "${sampleTemplate.path}" }`);
    }
    
    // 4. 测试搜索和过滤
    console.log("\n🔍 测试搜索和过滤功能...");
    const searchQueries = ["building", "gradient", "door", "label"];
    searchQueries.forEach(query => {
      const results = templateManager.registry.search(query);
      console.log(`搜索 "${query}": ${results.length} 个结果`);
    });
    
    console.log("\n🎉 模板系统UI集成测试完成！");
    return true;
    
  } catch (error) {
    console.error("❌ 模板系统UI集成测试失败:", error);
    return false;
  }
}

/**
 * 运行所有模板系统测试
 */
function runAllTemplateTests() {
  console.log("🧪 开始运行所有模板系统测试...");
  
  const results = {
    basics: false,
    advanced: false,
    uiIntegration: false
  };
  
  try {
    results.basics = testTemplateSystemBasics();
    results.advanced = testTemplateSystemAdvanced();
    results.uiIntegration = testTemplateUIIntegration();
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n📊 测试结果总结:`);
    console.log(`  ✅ 通过: ${passedTests}/${totalTests}`);
    console.log(`  📋 详细结果:`);
    console.log(`    基础功能: ${results.basics ? '✅' : '❌'}`);
    console.log(`    高级功能: ${results.advanced ? '✅' : '❌'}`);
    console.log(`    UI集成: ${results.uiIntegration ? '✅' : '❌'}`);
    
    if (passedTests === totalTests) {
      console.log(`\n🎉 所有测试通过！模板系统运行正常。`);
    } else {
      console.log(`\n⚠️ 有 ${totalTests - passedTests} 个测试失败，请检查相关功能。`);
    }
    
    return {
      success: passedTests === totalTests,
      results,
      summary: {
        passed: passedTests,
        total: totalTests,
        percentage: Math.round((passedTests / totalTests) * 100)
      }
    };
    
  } catch (error) {
    console.error("❌ 测试运行过程中发生错误:", error);
    return {
      success: false,
      error: error.message,
      results
    };
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testTemplateSystemBasics = testTemplateSystemBasics;
  window.testTemplateSystemAdvanced = testTemplateSystemAdvanced;
  window.testTemplateUIIntegration = testTemplateUIIntegration;
  window.runAllTemplateTests = runAllTemplateTests;
  
  console.log("模板系统测试函数已添加到window对象:");
  console.log("- testTemplateSystemBasics() - 基础功能测试");
  console.log("- testTemplateSystemAdvanced() - 高级功能测试");
  console.log("- testTemplateUIIntegration() - UI集成测试");
  console.log("- runAllTemplateTests() - 运行所有测试");
}

export { 
  testTemplateSystemBasics, 
  testTemplateSystemAdvanced, 
  testTemplateUIIntegration, 
  runAllTemplateTests 
};
