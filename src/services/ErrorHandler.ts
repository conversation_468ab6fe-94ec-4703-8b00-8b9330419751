/**
 * ErrorHandler.ts
 * 统一错误处理和恢复机制
 */

import { notificationService } from './NotificationService';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION = 'validation',
  NETWORK = 'network',
  STORAGE = 'storage',
  TEMPLATE = 'template',
  INSTANCE = 'instance',
  SYNC = 'sync',
  PERFORMANCE = 'performance',
  UNKNOWN = 'unknown'
}

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  timestamp: Date;
  context?: {
    operation?: string;
    templateId?: string;
    instanceId?: string;
    userId?: string;
    sessionId?: string;
  };
  stack?: string;
  recoverable: boolean;
  retryCount?: number;
  maxRetries?: number;
}

/**
 * 恢复策略接口
 */
export interface RecoveryStrategy {
  name: string;
  description: string;
  execute: (error: ErrorInfo) => Promise<boolean>;
  canRecover: (error: ErrorInfo) => boolean;
  priority: number;
}

/**
 * 错误处理器
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  
  private errors: Map<string, ErrorInfo> = new Map();
  private recoveryStrategies: Map<ErrorType, RecoveryStrategy[]> = new Map();
  private errorListeners: ((error: ErrorInfo) => void)[] = [];
  private maxErrorHistory = 1000;
  private retryDelays = [1000, 2000, 5000, 10000]; // 重试延迟（毫秒）

  private constructor() {
    this.initializeRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * 处理错误
   */
  async handleError(
    error: Error | string,
    type: ErrorType = ErrorType.UNKNOWN,
    context?: ErrorInfo['context']
  ): Promise<ErrorInfo> {
    const errorInfo: ErrorInfo = {
      id: this.generateErrorId(),
      type,
      severity: this.determineSeverity(error, type),
      message: typeof error === 'string' ? error : error.message,
      details: typeof error === 'object' ? error : undefined,
      timestamp: new Date(),
      context,
      stack: typeof error === 'object' && error.stack ? error.stack : undefined,
      recoverable: this.isRecoverable(type),
      retryCount: 0,
      maxRetries: this.getMaxRetries(type)
    };

    // 存储错误信息
    this.errors.set(errorInfo.id, errorInfo);
    this.limitErrorHistory();

    // 通知监听器
    this.notifyListeners(errorInfo);

    // 显示用户通知
    this.showUserNotification(errorInfo);

    // 尝试恢复
    if (errorInfo.recoverable) {
      await this.attemptRecovery(errorInfo);
    }

    // 记录错误日志
    this.logError(errorInfo);

    return errorInfo;
  }

  /**
   * 重试操作
   */
  async retryOperation<T>(
    operation: () => Promise<T>,
    type: ErrorType,
    context?: ErrorInfo['context'],
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error | undefined;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          // 最后一次尝试失败，处理错误
          await this.handleError(lastError, type, {
            ...context,
            operation: 'retry_operation'
          });
          throw lastError;
        }
        
        // 等待重试延迟
        const delay = this.retryDelays[Math.min(attempt, this.retryDelays.length - 1)];
        await new Promise(resolve => setTimeout(resolve, delay));
        
        console.warn(`操作重试 ${attempt + 1}/${maxRetries}:`, lastError.message);
      }
    }
    
    throw lastError;
  }

  /**
   * 添加错误监听器
   */
  onError(listener: (error: ErrorInfo) => void): () => void {
    this.errorListeners.push(listener);
    
    // 返回取消监听的函数
    return () => {
      const index = this.errorListeners.indexOf(listener);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(
    type?: ErrorType,
    severity?: ErrorSeverity,
    limit: number = 100
  ): ErrorInfo[] {
    let errors = Array.from(this.errors.values());
    
    if (type) {
      errors = errors.filter(e => e.type === type);
    }
    
    if (severity) {
      errors = errors.filter(e => e.severity === severity);
    }
    
    return errors
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory(olderThan?: Date): void {
    if (olderThan) {
      for (const [id, error] of this.errors) {
        if (error.timestamp < olderThan) {
          this.errors.delete(id);
        }
      }
    } else {
      this.errors.clear();
    }
    
    console.log('🧹 错误历史已清除');
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recentErrors: number;
    recoveredErrors: number;
  } {
    const errors = Array.from(this.errors.values());
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const byType = {} as Record<ErrorType, number>;
    const bySeverity = {} as Record<ErrorSeverity, number>;
    let recoveredErrors = 0;
    
    for (const error of errors) {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
      
      if (error.recoverable && error.retryCount && error.retryCount > 0) {
        recoveredErrors++;
      }
    }
    
    return {
      total: errors.length,
      byType,
      bySeverity,
      recentErrors: errors.filter(e => e.timestamp > oneHourAgo).length,
      recoveredErrors
    };
  }

  /**
   * 初始化恢复策略
   */
  private initializeRecoveryStrategies(): void {
    // 网络错误恢复策略
    this.addRecoveryStrategy(ErrorType.NETWORK, {
      name: 'network_retry',
      description: '网络重试',
      priority: 1,
      canRecover: (error) => error.type === ErrorType.NETWORK,
      execute: async (error) => {
        // 简单的网络重试逻辑
        await new Promise(resolve => setTimeout(resolve, 2000));
        return true;
      }
    });

    // 存储错误恢复策略
    this.addRecoveryStrategy(ErrorType.STORAGE, {
      name: 'storage_fallback',
      description: '存储降级',
      priority: 1,
      canRecover: (error) => error.type === ErrorType.STORAGE,
      execute: async (error) => {
        // 尝试使用内存存储作为降级方案
        console.warn('存储失败，使用内存存储作为降级方案');
        return true;
      }
    });

    // 模板错误恢复策略
    this.addRecoveryStrategy(ErrorType.TEMPLATE, {
      name: 'template_validation_fix',
      description: '模板验证修复',
      priority: 1,
      canRecover: (error) => error.type === ErrorType.TEMPLATE && error.severity !== ErrorSeverity.CRITICAL,
      execute: async (error) => {
        // 尝试修复常见的模板验证错误
        if (error.details && error.details.validationErrors) {
          console.warn('尝试修复模板验证错误:', error.details.validationErrors);
          return true;
        }
        return false;
      }
    });

    // 同步错误恢复策略
    this.addRecoveryStrategy(ErrorType.SYNC, {
      name: 'sync_retry',
      description: '同步重试',
      priority: 1,
      canRecover: (error) => error.type === ErrorType.SYNC,
      execute: async (error) => {
        // 延迟后重试同步
        await new Promise(resolve => setTimeout(resolve, 5000));
        console.log('重试同步操作');
        return true;
      }
    });
  }

  /**
   * 添加恢复策略
   */
  private addRecoveryStrategy(type: ErrorType, strategy: RecoveryStrategy): void {
    if (!this.recoveryStrategies.has(type)) {
      this.recoveryStrategies.set(type, []);
    }
    
    const strategies = this.recoveryStrategies.get(type)!;
    strategies.push(strategy);
    strategies.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 尝试恢复
   */
  private async attemptRecovery(error: ErrorInfo): Promise<boolean> {
    const strategies = this.recoveryStrategies.get(error.type) || [];
    
    for (const strategy of strategies) {
      if (strategy.canRecover(error)) {
        try {
          console.log(`尝试恢复策略: ${strategy.name}`);
          const recovered = await strategy.execute(error);
          
          if (recovered) {
            error.retryCount = (error.retryCount || 0) + 1;
            console.log(`✅ 错误恢复成功: ${strategy.name}`);
            
            // 显示恢复成功通知
            notificationService.success({
              title: '错误已恢复',
              message: `使用 ${strategy.description} 策略成功恢复`,
              duration: 3000
            });
            
            return true;
          }
        } catch (recoveryError) {
          console.error(`恢复策略失败: ${strategy.name}`, recoveryError);
        }
      }
    }
    
    return false;
  }

  /**
   * 确定错误严重程度
   */
  private determineSeverity(error: Error | string, type: ErrorType): ErrorSeverity {
    const message = typeof error === 'string' ? error : error.message;
    
    // 关键词匹配
    if (message.includes('critical') || message.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }
    
    if (message.includes('network') || message.includes('timeout')) {
      return ErrorSeverity.MEDIUM;
    }
    
    // 根据类型判断
    switch (type) {
      case ErrorType.STORAGE:
      case ErrorType.NETWORK:
        return ErrorSeverity.HIGH;
      case ErrorType.VALIDATION:
        return ErrorSeverity.MEDIUM;
      case ErrorType.PERFORMANCE:
        return ErrorSeverity.LOW;
      default:
        return ErrorSeverity.MEDIUM;
    }
  }

  /**
   * 判断是否可恢复
   */
  private isRecoverable(type: ErrorType): boolean {
    const recoverableTypes = [
      ErrorType.NETWORK,
      ErrorType.STORAGE,
      ErrorType.SYNC,
      ErrorType.TEMPLATE
    ];
    return recoverableTypes.includes(type);
  }

  /**
   * 获取最大重试次数
   */
  private getMaxRetries(type: ErrorType): number {
    switch (type) {
      case ErrorType.NETWORK:
        return 3;
      case ErrorType.STORAGE:
        return 2;
      case ErrorType.SYNC:
        return 5;
      default:
        return 1;
    }
  }

  /**
   * 显示用户通知
   */
  private showUserNotification(error: ErrorInfo): void {
    const shouldNotifyUser = error.severity === ErrorSeverity.HIGH || 
                           error.severity === ErrorSeverity.CRITICAL;
    
    if (shouldNotifyUser) {
      const notificationType = error.severity === ErrorSeverity.CRITICAL ? 'error' : 'warning';
      
      notificationService[notificationType]({
        title: this.getErrorTitle(error.type),
        message: error.message,
        persistent: error.severity === ErrorSeverity.CRITICAL,
        actions: error.recoverable ? [
          {
            label: '重试',
            action: () => this.attemptRecovery(error),
            style: 'primary'
          }
        ] : undefined
      });
    }
  }

  /**
   * 获取错误标题
   */
  private getErrorTitle(type: ErrorType): string {
    const titles: Record<ErrorType, string> = {
      [ErrorType.VALIDATION]: '验证错误',
      [ErrorType.NETWORK]: '网络错误',
      [ErrorType.STORAGE]: '存储错误',
      [ErrorType.TEMPLATE]: '模板错误',
      [ErrorType.INSTANCE]: '实例错误',
      [ErrorType.SYNC]: '同步错误',
      [ErrorType.PERFORMANCE]: '性能问题',
      [ErrorType.UNKNOWN]: '未知错误'
    };
    return titles[type];
  }

  /**
   * 通知监听器
   */
  private notifyListeners(error: ErrorInfo): void {
    for (const listener of this.errorListeners) {
      try {
        listener(error);
      } catch (listenerError) {
        console.error('错误监听器执行失败:', listenerError);
      }
    }
  }

  /**
   * 记录错误日志
   */
  private logError(error: ErrorInfo): void {
    const logLevel = error.severity === ErrorSeverity.CRITICAL ? 'error' : 
                    error.severity === ErrorSeverity.HIGH ? 'warn' : 'info';
    
    console[logLevel](`[${error.type.toUpperCase()}] ${error.message}`, {
      id: error.id,
      severity: error.severity,
      context: error.context,
      timestamp: error.timestamp,
      stack: error.stack
    });
  }

  /**
   * 限制错误历史数量
   */
  private limitErrorHistory(): void {
    if (this.errors.size > this.maxErrorHistory) {
      const sortedErrors = Array.from(this.errors.entries())
        .sort(([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime());
      
      const toDelete = sortedErrors.slice(0, this.errors.size - this.maxErrorHistory);
      for (const [id] of toDelete) {
        this.errors.delete(id);
      }
    }
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 处理未捕获的Promise拒绝
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(
          event.reason || 'Unhandled Promise Rejection',
          ErrorType.UNKNOWN,
          { operation: 'unhandled_promise_rejection' }
        );
      });

      // 处理未捕获的错误
      window.addEventListener('error', (event) => {
        this.handleError(
          event.error || event.message,
          ErrorType.UNKNOWN,
          { operation: 'unhandled_error' }
        );
      });
    }
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance();

// 便捷的错误处理函数
export const handleError = (
  error: Error | string,
  type: ErrorType = ErrorType.UNKNOWN,
  context?: ErrorInfo['context']
) => errorHandler.handleError(error, type, context);

// 如果在浏览器环境中，将错误处理器添加到全局对象
if (typeof window !== 'undefined') {
  (window as any).errorHandler = errorHandler;
  (window as any).handleError = handleError;
  console.log('🛡️ 错误处理器已添加到全局对象');
}
