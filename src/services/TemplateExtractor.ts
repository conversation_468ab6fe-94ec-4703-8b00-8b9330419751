/**
 * TemplateExtractor.ts
 * 负责从画布节点提取模板定义
 */

import { Cell } from '@antv/x6';
import { Template, TemplateType, TemplateParameter, TemplateDefinition } from './TemplateManager';

/**
 * 节点类型到模板类型的映射
 */
const NODE_TYPE_MAPPING: Record<string, TemplateType> = {
  'MeshNode': TemplateType.MESH,
  'StyleNode': TemplateType.STYLE,
  'CameraNode': TemplateType.CAMERA,
  'ActionNode': TemplateType.ACTION,
  'LabelNode': TemplateType.LABEL,
  'PositionNode': TemplateType.POSITION,
  'InteractionNode': TemplateType.INTERACTION,
  'EnvironmentNode': TemplateType.ENVIRONMENT,
  'CallbackNode': TemplateType.CALLBACK
};

/**
 * 模板提取器类
 */
export class TemplateExtractor {
  /**
   * 从节点提取模板
   * @param node X6节点
   * @param templateName 模板名称
   * @param description 模板描述
   * @param category 模板分类
   * @returns 模板定义
   */
  public extractFromNode(
    node: Cell, 
    templateName: string, 
    description: string = '', 
    category: string = '自定义'
  ): Partial<Template> {
    // 获取节点属性
    const nodeData = node.getData();
    const nodeAttrs = node.getAttrs();
    const nodeSize = node.getSize();
    const nodePosition = node.getPosition();
    
    // 合并所有属性
    const properties = {
      ...nodeData,
      attrs: nodeAttrs,
      size: nodeSize,
      position: nodePosition,
      shape: node.shape
    };
    
    // 确定模板类型
    const templateType = this.determineTemplateType(node);
    
    // 提取参数
    const parameters = this.extractParameters(properties);
    
    // 分析依赖
    const dependencies = this.extractDependencies(properties);
    
    // 创建模板定义
    const definition: TemplateDefinition = {
      nodeType: node.shape || 'GenericNode',
      properties,
      parameters,
      dependencies
    };
    
    // 创建模板对象
    const template: Partial<Template> = {
      name: templateName,
      type: templateType,
      description,
      data: properties,
      definition,
      metadata: {
        tags: this.generateTags(templateType, templateName, properties),
        author: 'user',
        usage: 0,
        dependencies,
        category,
        sourceType: 'node-converted'
      }
    };
    
    return template;
  }
  
  /**
   * 确定模板类型
   */
  private determineTemplateType(node: Cell): TemplateType {
    // 根据节点形状确定类型
    const shape = node.shape;
    
    if (shape && shape in NODE_TYPE_MAPPING) {
      return NODE_TYPE_MAPPING[shape];
    }
    
    // 根据节点数据确定类型
    const data = node.getData();
    
    if (data) {
      // 检查是否包含样式相关属性
      if (data.backgroundColor || data.borderColor || data.textColor) {
        return TemplateType.STYLE;
      }
      
      // 检查是否包含网格相关属性
      if (data.meshNames || data.meshes) {
        return TemplateType.MESH;
      }
      
      // 检查是否包含相机相关属性
      if (data.camera || data.cameraPosition) {
        return TemplateType.CAMERA;
      }
      
      // 检查是否包含动作相关属性
      if (data.action || data.callback || data.onClick) {
        return TemplateType.ACTION;
      }
      
      // 检查是否包含标签相关属性
      if (data.label || data.text || data.fontSize) {
        return TemplateType.LABEL;
      }
      
      // 检查是否包含位置相关属性
      if (data.position || data.coordinates) {
        return TemplateType.POSITION;
      }
      
      // 检查是否包含交互相关属性
      if (data.hover || data.click || data.interaction) {
        return TemplateType.INTERACTION;
      }
    }
    
    // 默认返回样式类型
    return TemplateType.STYLE;
  }
  
  /**
   * 提取参数
   */
  private extractParameters(properties: any): TemplateParameter[] {
    const parameters: TemplateParameter[] = [];
    
    // 递归提取参数
    this.extractParametersRecursive(properties, '', parameters);
    
    return parameters;
  }
  
  /**
   * 递归提取参数
   */
  private extractParametersRecursive(
    obj: any, 
    prefix: string, 
    parameters: TemplateParameter[]
  ): void {
    if (obj === null || typeof obj !== 'object') {
      return;
    }
    
    // 跳过数组的递归处理（数组作为整体参数）
    if (Array.isArray(obj)) {
      return;
    }
    
    for (const key in obj) {
      const value = obj[key];
      const paramName = prefix ? `${prefix}.${key}` : key;
      
      // 跳过某些系统属性
      if (this.shouldSkipProperty(key)) {
        continue;
      }
      
      // 如果是基本类型或数组，添加为参数
      if (this.isParameterizable(value)) {
        parameters.push({
          name: paramName,
          type: this.getParameterType(value),
          description: this.generateParameterDescription(key, value),
          defaultValue: value,
          required: this.isRequiredParameter(key),
          options: this.getParameterOptions(key, value)
        });
      } else if (typeof value === 'object' && !Array.isArray(value)) {
        // 递归处理嵌套对象
        this.extractParametersRecursive(value, paramName, parameters);
      }
    }
  }
  
  /**
   * 判断属性是否应该跳过
   */
  private shouldSkipProperty(key: string): boolean {
    const skipKeys = ['id', 'zIndex', 'parent', 'children'];
    return skipKeys.includes(key);
  }
  
  /**
   * 判断值是否可以参数化
   */
  private isParameterizable(value: any): boolean {
    // 基本类型可以参数化
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return true;
    }
    
    // 数组可以参数化
    if (Array.isArray(value)) {
      return true;
    }
    
    return false;
  }
  
  /**
   * 获取参数类型
   */
  private getParameterType(value: any): 'string' | 'number' | 'boolean' | 'array' | 'object' {
    if (Array.isArray(value)) {
      return 'array';
    }
    
    if (typeof value === 'object') {
      return 'object';
    }
    
    return typeof value as 'string' | 'number' | 'boolean';
  }
  
  /**
   * 生成参数描述
   */
  private generateParameterDescription(key: string, value: any): string {
    const descriptions: Record<string, string> = {
      'backgroundColor': '背景颜色',
      'borderColor': '边框颜色',
      'textColor': '文字颜色',
      'fontSize': '字体大小',
      'width': '宽度',
      'height': '高度',
      'x': 'X坐标',
      'y': 'Y坐标',
      'opacity': '透明度',
      'rotation': '旋转角度'
    };
    
    return descriptions[key] || `${key}参数`;
  }
  
  /**
   * 判断是否为必需参数
   */
  private isRequiredParameter(key: string): boolean {
    const requiredKeys = ['name', 'type', 'id'];
    return requiredKeys.includes(key);
  }
  
  /**
   * 获取参数选项
   */
  private getParameterOptions(key: string, value: any): any[] | undefined {
    // 为某些特定参数提供选项
    if (key === 'textAlign') {
      return ['left', 'center', 'right'];
    }
    
    if (key === 'verticalAlign') {
      return ['top', 'middle', 'bottom'];
    }
    
    if (key === 'fontWeight') {
      return ['normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900'];
    }
    
    return undefined;
  }
  
  /**
   * 提取依赖关系
   */
  private extractDependencies(properties: any): string[] {
    const dependencies: string[] = [];
    
    // 递归查找引用
    this.extractDependenciesRecursive(properties, dependencies);
    
    // 去重
    return [...new Set(dependencies)];
  }
  
  /**
   * 递归提取依赖
   */
  private extractDependenciesRecursive(obj: any, dependencies: string[]): void {
    if (obj === null || typeof obj !== 'object') {
      return;
    }
    
    if (Array.isArray(obj)) {
      for (const item of obj) {
        this.extractDependenciesRecursive(item, dependencies);
      }
      return;
    }
    
    // 检查$ref引用
    if (obj.$ref) {
      dependencies.push(obj.$ref);
    }
    
    // 递归处理对象属性
    for (const key in obj) {
      if (key !== '$ref') {
        this.extractDependenciesRecursive(obj[key], dependencies);
      }
    }
  }
  
  /**
   * 生成模板标签
   */
  private generateTags(type: TemplateType, name: string, properties: any): string[] {
    const tags = [type.toString()];
    
    // 根据名称添加标签
    const nameLower = name.toLowerCase();
    if (nameLower.includes('building')) tags.push('建筑');
    if (nameLower.includes('floor')) tags.push('楼层');
    if (nameLower.includes('door')) tags.push('门');
    if (nameLower.includes('device')) tags.push('设备');
    if (nameLower.includes('gis')) tags.push('GIS');
    if (nameLower.includes('button')) tags.push('按钮');
    if (nameLower.includes('label')) tags.push('标签');
    
    // 根据属性添加标签
    if (properties.backgroundColor) tags.push('有背景');
    if (properties.borderColor) tags.push('有边框');
    if (properties.onClick || properties.callback) tags.push('可点击');
    if (properties.hover) tags.push('可悬停');
    
    return tags;
  }
  
  /**
   * 检查模板兼容性
   */
  public checkConfigCompatibility(template: Partial<Template>): boolean {
    try {
      // 检查是否可以序列化
      JSON.stringify(template.data);
      
      // 检查是否包含不支持的属性
      if (template.data && typeof template.data === 'object') {
        return this.checkObjectCompatibility(template.data);
      }
      
      return true;
    } catch (error) {
      console.warn('模板不兼容config.js格式:', error);
      return false;
    }
  }
  
  /**
   * 检查对象兼容性
   */
  private checkObjectCompatibility(obj: any): boolean {
    if (obj === null || typeof obj !== 'object') {
      return true;
    }
    
    if (Array.isArray(obj)) {
      return obj.every(item => this.checkObjectCompatibility(item));
    }
    
    for (const key in obj) {
      const value = obj[key];
      
      // 检查函数类型（不支持）
      if (typeof value === 'function') {
        return false;
      }
      
      // 递归检查嵌套对象
      if (!this.checkObjectCompatibility(value)) {
        return false;
      }
    }
    
    return true;
  }
}
