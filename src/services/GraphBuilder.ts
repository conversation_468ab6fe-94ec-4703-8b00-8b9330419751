/**
 * 节点图构建器 - 将解析后的配置数据转换为X6节点图
 */

import { Graph, Node, Edge } from "@antv/x6";
import type { 
  ParsedSceneData, 
  BaseNodeData, 
  ConnectionData,
  SceneConfigNodeData,
  EventNodeData,
  ActionNodeData,
  LifecycleNodeData,
  StaticLabelNodeData,
  ReferenceNodeData,
  DataNodeData
} from "./ConfigParser";

/**
 * 节点图构建器类
 */
export class GraphBuilder {
  private graph: Graph | null = null;
  private nodeIdMap: Map<string, Node> = new Map();

  /**
   * 设置图实例
   */
  setGraph(graph: Graph): void {
    this.graph = graph;
    this.nodeIdMap.clear();
  }

  /**
   * 构建完整的节点图
   */
  buildGraph(parsedData: ParsedSceneData): void {
    if (!this.graph) {
      throw new Error("Graph instance not set. Call setGraph() first.");
    }

    // 清空现有图
    this.graph.clearCells();
    this.nodeIdMap.clear();

    // 创建各种类型的节点
    this.createSceneConfigNode(parsedData.sceneConfig);
    this.createEventNodes(parsedData.eventNodes);
    this.createActionNodes(parsedData.actionNodes);
    this.createLifecycleNodes(parsedData.lifecycleNodes);
    this.createStaticLabelNodes(parsedData.staticLabelNodes);
    this.createReferenceNodes(parsedData.referenceNodes);
    this.createDataNodes(parsedData.dataNodes);

    // 创建连接
    this.createConnections(parsedData.connections);

    // 优化布局
    this.optimizeLayout();
  }

  /**
   * 创建场景配置节点
   */
  private createSceneConfigNode(nodeData: SceneConfigNodeData): void {
    if (!this.graph) return;

    const node = this.graph.addNode({
      id: nodeData.id,
      x: nodeData.x || 50,
      y: nodeData.y || 50,
      shape: "scene-config-node",
      label: nodeData.displayName,
      data: nodeData,
      ports: {
        groups: {
          out: {
            position: "right",
            attrs: {
              circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
            },
          },
        },
        items: [{ id: "out-exec", group: "out" }],
      },
    });

    this.nodeIdMap.set(nodeData.id, node);
  }

  /**
   * 创建事件节点
   */
  private createEventNodes(eventNodes: EventNodeData[]): void {
    if (!this.graph) return;

    eventNodes.forEach((nodeData) => {
      const node = this.graph!.addNode({
        id: nodeData.id,
        x: nodeData.x || 200,
        y: nodeData.y || 150,
        shape: "event-node",
        label: nodeData.displayName,
        data: nodeData,
        ports: {
          groups: {
            in: {
              position: "left",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
            out: {
              position: "right",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
          },
          items: [
            { id: "in-trigger", group: "in" },
            { id: "out-exec", group: "out" },
          ],
        },
      });

      this.nodeIdMap.set(nodeData.id, node);
    });
  }

  /**
   * 创建动作节点
   */
  private createActionNodes(actionNodes: ActionNodeData[]): void {
    if (!this.graph) return;

    actionNodes.forEach((nodeData) => {
      let shape = "action-node";
      let color = "#52c41a";

      if (nodeData.nodeType === "action-highlight") {
        shape = "highlight-node";
        color = "#faad14";
      } else if (nodeData.nodeType === "action-callback") {
        shape = "callback-node";
        color = "#1890ff";
      }

      const node = this.graph!.addNode({
        id: nodeData.id,
        x: nodeData.x || 400,
        y: nodeData.y || 150,
        shape: shape,
        label: nodeData.displayName,
        data: nodeData,
        attrs: {
          body: {
            fill: color,
            stroke: "#5F95FF",
            strokeWidth: 2,
          },
          label: {
            fill: "#ffffff",
            fontSize: 14,
          },
        },
        ports: {
          groups: {
            in: {
              position: "left",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
            out: {
              position: "right",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
          },
          items: [
            { id: "in-exec", group: "in" },
            { id: "out-exec", group: "out" },
          ],
        },
      });

      this.nodeIdMap.set(nodeData.id, node);
    });
  }

  /**
   * 创建生命周期节点
   */
  private createLifecycleNodes(lifecycleNodes: LifecycleNodeData[]): void {
    if (!this.graph) return;

    lifecycleNodes.forEach((nodeData) => {
      const node = this.graph!.addNode({
        id: nodeData.id,
        x: nodeData.x || 50,
        y: nodeData.y || 300,
        shape: "lifecycle-node",
        label: nodeData.displayName,
        data: nodeData,
        attrs: {
          body: {
            fill: "#722ed1",
            stroke: "#5F95FF",
            strokeWidth: 2,
          },
          label: {
            fill: "#ffffff",
            fontSize: 14,
          },
        },
        ports: {
          groups: {
            out: {
              position: "right",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
          },
          items: [{ id: "out-exec", group: "out" }],
        },
      });

      this.nodeIdMap.set(nodeData.id, node);
    });
  }

  /**
   * 创建静态标签节点
   */
  private createStaticLabelNodes(staticLabelNodes: StaticLabelNodeData[]): void {
    if (!this.graph) return;

    staticLabelNodes.forEach((nodeData) => {
      const node = this.graph!.addNode({
        id: nodeData.id,
        x: nodeData.x || 50,
        y: nodeData.y || 500,
        shape: "static-label-node",
        label: nodeData.displayName,
        data: nodeData,
        attrs: {
          body: {
            fill: "#13c2c2",
            stroke: "#5F95FF",
            strokeWidth: 2,
          },
          label: {
            fill: "#ffffff",
            fontSize: 14,
          },
        },
        ports: {
          groups: {
            out: {
              position: "right",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
          },
          items: [{ id: "out-click", group: "out" }],
        },
      });

      this.nodeIdMap.set(nodeData.id, node);
    });
  }

  /**
   * 创建引用节点
   */
  private createReferenceNodes(referenceNodes: ReferenceNodeData[]): void {
    if (!this.graph) return;

    referenceNodes.forEach((nodeData) => {
      const node = this.graph!.addNode({
        id: nodeData.id,
        x: nodeData.x || 600,
        y: nodeData.y || 100,
        shape: "reference-node",
        label: nodeData.displayName,
        data: nodeData,
        attrs: {
          body: {
            fill: "#eb2f96",
            stroke: "#5F95FF",
            strokeWidth: 2,
          },
          label: {
            fill: "#ffffff",
            fontSize: 12,
          },
        },
        ports: {
          groups: {
            in: {
              position: "left",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
            out: {
              position: "right",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
          },
          items: [
            { id: "in-ref", group: "in" },
            { id: "out-ref", group: "out" },
          ],
        },
      });

      this.nodeIdMap.set(nodeData.id, node);
    });
  }

  /**
   * 创建数据节点
   */
  private createDataNodes(dataNodes: DataNodeData[]): void {
    if (!this.graph) return;

    dataNodes.forEach((nodeData) => {
      let color = "#0066cc";
      let shape = "data-node";

      if (nodeData.nodeType === "data-source") {
        color = "#52c41a";
        shape = "data-source-node";
      } else if (nodeData.nodeType === "data-transform") {
        color = "#faad14";
        shape = "transform-node";
      } else if (nodeData.nodeType === "data-consumer") {
        color = "#fa8c16";
        shape = "consumer-node";
      }

      const node = this.graph!.addNode({
        id: nodeData.id,
        x: nodeData.x || 300,
        y: nodeData.y || 400,
        shape: shape,
        label: nodeData.displayName,
        data: nodeData,
        attrs: {
          body: {
            fill: color,
            stroke: "#5F95FF",
            strokeWidth: 2,
          },
          label: {
            fill: "#ffffff",
            fontSize: 14,
          },
        },
        ports: {
          groups: {
            in: {
              position: "left",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
            out: {
              position: "right",
              attrs: {
                circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
              },
            },
          },
          items: [
            { id: "in-exec", group: "in" },
            { id: "in-data", group: "in" },
            { id: "out-exec", group: "out" },
            { id: "out-data", group: "out" },
          ],
        },
      });

      this.nodeIdMap.set(nodeData.id, node);
    });
  }

  /**
   * 创建连接
   */
  private createConnections(connections: ConnectionData[]): void {
    if (!this.graph) return;

    connections.forEach((connData) => {
      const sourceNode = this.nodeIdMap.get(connData.source);
      const targetNode = this.nodeIdMap.get(connData.target);

      if (sourceNode && targetNode) {
        this.graph!.addEdge({
          id: connData.id,
          source: {
            cell: sourceNode.id,
            port: connData.sourcePort || "out-exec",
          },
          target: {
            cell: targetNode.id,
            port: connData.targetPort || "in-exec",
          },
          attrs: {
            line: {
              stroke: "#5F95FF",
              strokeWidth: 2,
              targetMarker: {
                name: "classic",
                size: 8,
              },
            },
          },
        });
      }
    });
  }

  /**
   * 优化布局
   */
  private optimizeLayout(): void {
    if (!this.graph) return;

    // 这里可以实现自动布局算法
    // 目前使用解析时设置的坐标
    console.log("Layout optimization completed");
  }
}
