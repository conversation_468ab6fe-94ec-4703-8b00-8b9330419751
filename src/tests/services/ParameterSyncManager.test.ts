/**
 * ParameterSyncManager.test.ts
 * ParameterSyncManager服务的单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ParameterSyncManager } from '../../services/ParameterSyncManager';
import { TestUtils, createTestTemplate, createTestInstance } from '../setup';

describe('ParameterSyncManager', () => {
  let syncManager: ParameterSyncManager;

  beforeEach(() => {
    syncManager = new ParameterSyncManager();
    TestUtils.cleanup();
  });

  afterEach(() => {
    TestUtils.cleanup();
  });

  describe('参数变更检测', () => {
    let oldTemplate: any;
    let newTemplate: any;

    beforeEach(() => {
      oldTemplate = createTestTemplate({
        definition: {
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ffffff', required: false },
            { name: 'size', type: 'number', defaultValue: 1.0, required: true },
            { name: 'opacity', type: 'number', defaultValue: 1.0, required: false }
          ],
          dependencies: [],
          validation: { required: ['size'], constraints: {} }
        }
      });

      newTemplate = createTestTemplate({
        id: oldTemplate.id,
        definition: {
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ff0000', required: false }, // 默认值变更
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }, // 无变更
            { name: 'scale', type: 'number', defaultValue: 1.0, required: false } // 新增参数
            // opacity 参数被删除
          ],
          dependencies: [],
          validation: { required: ['size'], constraints: {} }
        }
      });
    });

    it('应该检测到参数添加', () => {
      const changes = syncManager.detectParameterChanges(oldTemplate, newTemplate);

      const addedChanges = changes.filter(c => c.changeType === 'added');
      expect(addedChanges).toHaveLength(1);
      expect(addedChanges[0].parameterName).toBe('scale');
    });

    it('应该检测到参数删除', () => {
      const changes = syncManager.detectParameterChanges(oldTemplate, newTemplate);

      const removedChanges = changes.filter(c => c.changeType === 'removed');
      expect(removedChanges).toHaveLength(1);
      expect(removedChanges[0].parameterName).toBe('opacity');
    });

    it('应该检测到参数修改', () => {
      const changes = syncManager.detectParameterChanges(oldTemplate, newTemplate);

      const modifiedChanges = changes.filter(c => c.changeType === 'modified');
      expect(modifiedChanges).toHaveLength(1);
      expect(modifiedChanges[0].parameterName).toBe('color');
      expect(modifiedChanges[0].details).toContain('默认值');
    });

    it('应该检测到类型变更', () => {
      const typeChangedTemplate = {
        ...newTemplate,
        definition: {
          ...newTemplate.definition,
          parameters: [
            { name: 'color', type: 'number', defaultValue: 0xff0000, required: false }, // 类型从string改为number
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
          ]
        }
      };

      const changes = syncManager.detectParameterChanges(oldTemplate, typeChangedTemplate);

      const typeChanges = changes.filter(c => c.changeType === 'type_changed');
      expect(typeChanges).toHaveLength(1);
      expect(typeChanges[0].parameterName).toBe('color');
    });

    it('应该检测到必需性变更', () => {
      const requiredChangedTemplate = {
        ...newTemplate,
        definition: {
          ...newTemplate.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ffffff', required: true }, // 从可选改为必需
            { name: 'size', type: 'number', defaultValue: 1.0, required: false } // 从必需改为可选
          ]
        }
      };

      const changes = syncManager.detectParameterChanges(oldTemplate, requiredChangedTemplate);

      const requiredChanges = changes.filter(c => c.details?.includes('必需性'));
      expect(requiredChanges).toHaveLength(2);
    });
  });

  describe('冲突检测', () => {
    let template: any;
    let instances: any[];

    beforeEach(() => {
      template = createTestTemplate({
        definition: {
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ffffff', required: false },
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
          ],
          dependencies: [],
          validation: { required: ['size'], constraints: {} }
        }
      });

      instances = [
        createTestInstance(template.id, {
          parameters: { color: '#ff0000', size: 2.0 },
          overrides: { color: '#ff0000' }
        }),
        createTestInstance(template.id, {
          parameters: { color: '#00ff00', size: 1.5 },
          overrides: { color: '#00ff00' }
        })
      ];
    });

    it('应该检测参数删除冲突', () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
            // color 参数被删除
          ]
        }
      };

      const conflicts = syncManager.detectConflicts(template, updatedTemplate, instances);

      const parameterRemovedConflicts = conflicts.filter(c => c.conflictType === 'parameter_removed');
      expect(parameterRemovedConflicts).toHaveLength(1);
      expect(parameterRemovedConflicts[0].parameterName).toBe('color');
      expect(parameterRemovedConflicts[0].affectedInstances).toHaveLength(2);
    });

    it('应该检测类型不匹配冲突', () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'number', defaultValue: 0xffffff, required: false }, // 类型改为number
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
          ]
        }
      };

      const conflicts = syncManager.detectConflicts(template, updatedTemplate, instances);

      const typeMismatchConflicts = conflicts.filter(c => c.conflictType === 'type_mismatch');
      expect(typeMismatchConflicts).toHaveLength(1);
      expect(typeMismatchConflicts[0].parameterName).toBe('color');
    });

    it('应该检测值不兼容冲突', () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ffffff', required: false },
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
          ],
          validation: {
            required: ['size'],
            constraints: {
              size: { min: 3.0, max: 10.0 } // 新的约束，现有实例值不满足
            }
          }
        }
      };

      const conflicts = syncManager.detectConflicts(template, updatedTemplate, instances);

      const valueIncompatibleConflicts = conflicts.filter(c => c.conflictType === 'value_incompatible');
      expect(valueIncompatibleConflicts).toHaveLength(1);
      expect(valueIncompatibleConflicts[0].parameterName).toBe('size');
    });

    it('应该正确计算冲突严重程度', () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
            // color 参数被删除 - 应该是高严重程度
          ]
        }
      };

      const conflicts = syncManager.detectConflicts(template, updatedTemplate, instances);

      expect(conflicts[0].severity).toBe('high');
    });
  });

  describe('同步策略', () => {
    let template: any;
    let instance: any;

    beforeEach(() => {
      template = createTestTemplate({
        definition: {
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ffffff', required: false },
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
          ],
          dependencies: [],
          validation: { required: ['size'], constraints: {} }
        }
      });

      instance = createTestInstance(template.id, {
        parameters: { color: '#ff0000', size: 2.0 },
        overrides: { color: '#ff0000' }
      });
    });

    it('应该支持保守同步策略', async () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#00ff00', required: false }, // 默认值变更
            { name: 'size', type: 'number', defaultValue: 1.0, required: true }
          ]
        }
      };

      const result = await syncManager.syncInstance(instance, template, updatedTemplate, 'conservative');

      expect(result.success).toBe(true);
      expect(result.updatedInstance!.parameters.color).toBe('#ff0000'); // 保持实例值
      expect(result.updatedInstance!.parameters.size).toBe(2.0);
    });

    it('应该支持激进同步策略', async () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#00ff00', required: false }, // 默认值变更
            { name: 'size', type: 'number', defaultValue: 3.0, required: true } // 默认值变更
          ]
        }
      };

      const result = await syncManager.syncInstance(instance, template, updatedTemplate, 'aggressive');

      expect(result.success).toBe(true);
      expect(result.updatedInstance!.parameters.color).toBe('#00ff00'); // 使用新默认值
      expect(result.updatedInstance!.parameters.size).toBe(3.0); // 使用新默认值
    });

    it('应该支持智能同步策略', async () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#00ff00', required: false },
            { name: 'size', type: 'number', defaultValue: 3.0, required: true },
            { name: 'opacity', type: 'number', defaultValue: 0.8, required: false } // 新增参数
          ]
        }
      };

      const result = await syncManager.syncInstance(instance, template, updatedTemplate, 'smart');

      expect(result.success).toBe(true);
      expect(result.updatedInstance!.parameters.color).toBe('#ff0000'); // 保持覆盖值
      expect(result.updatedInstance!.parameters.size).toBe(2.0); // 保持实例值
      expect(result.updatedInstance!.parameters.opacity).toBe(0.8); // 使用新参数默认值
    });

    it('应该支持用户引导策略', async () => {
      const userChoices = {
        color: 'keep_instance',
        size: 'use_template'
      };

      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#00ff00', required: false },
            { name: 'size', type: 'number', defaultValue: 3.0, required: true }
          ]
        }
      };

      const result = await syncManager.syncInstance(
        instance, 
        template, 
        updatedTemplate, 
        'user_guided',
        { userChoices }
      );

      expect(result.success).toBe(true);
      expect(result.updatedInstance!.parameters.color).toBe('#ff0000'); // 用户选择保持实例值
      expect(result.updatedInstance!.parameters.size).toBe(3.0); // 用户选择使用模板值
    });
  });

  describe('批量同步', () => {
    let template: any;
    let instances: any[];

    beforeEach(() => {
      template = createTestTemplate();
      instances = TestUtils.createTestInstances(template.id, 5);
    });

    it('应该支持批量同步', async () => {
      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            ...template.definition.parameters,
            { name: 'newParam', type: 'string', defaultValue: 'new', required: false }
          ]
        }
      };

      const result = await syncManager.batchSyncInstances(instances, template, updatedTemplate, 'conservative');

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(5);
      expect(result.failureCount).toBe(0);
      expect(result.updatedInstances).toHaveLength(5);
    });

    it('应该正确处理部分失败', async () => {
      // 模拟一个会失败的实例
      const faultyInstance = { ...instances[0], parameters: null };
      const mixedInstances = [faultyInstance, ...instances.slice(1)];

      const updatedTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ffffff', required: false }
          ]
        }
      };

      const result = await syncManager.batchSyncInstances(mixedInstances, template, updatedTemplate, 'conservative');

      expect(result.success).toBe(true); // 整体操作成功
      expect(result.successCount).toBe(4);
      expect(result.failureCount).toBe(1);
      expect(result.errors).toHaveLength(1);
    });

    it('应该支持进度回调', async () => {
      const progressCallback = vi.fn();
      const updatedTemplate = { ...template };

      await syncManager.batchSyncInstances(
        instances, 
        template, 
        updatedTemplate, 
        'conservative',
        { progressCallback }
      );

      expect(progressCallback).toHaveBeenCalledTimes(instances.length + 1); // 每个实例 + 完成回调
    });

    it('应该支持并发控制', async () => {
      const updatedTemplate = { ...template };
      const startTime = Date.now();

      await syncManager.batchSyncInstances(
        instances, 
        template, 
        updatedTemplate, 
        'conservative',
        { concurrency: 2 }
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      // 验证并发控制生效（具体时间取决于实现）
      expect(duration).toBeGreaterThan(0);
    });
  });

  describe('变更历史', () => {
    let template: any;

    beforeEach(() => {
      template = createTestTemplate();
    });

    it('应该记录参数变更历史', () => {
      const oldTemplate = template;
      const newTemplate = {
        ...template,
        definition: {
          ...template.definition,
          parameters: [
            { name: 'color', type: 'string', defaultValue: '#ff0000', required: false }
          ]
        }
      };

      const changes = syncManager.detectParameterChanges(oldTemplate, newTemplate);
      syncManager.recordParameterChanges(template.id, changes);

      const history = syncManager.getParameterChangeHistory(template.id);
      expect(history).toHaveLength(1);
      expect(history[0].templateId).toBe(template.id);
      expect(history[0].changes).toEqual(changes);
    });

    it('应该支持历史查询', () => {
      // 记录多次变更
      for (let i = 0; i < 3; i++) {
        const changes = [{
          parameterName: `param${i}`,
          changeType: 'added' as const,
          oldValue: undefined,
          newValue: `value${i}`,
          timestamp: new Date(),
          details: `添加参数 param${i}`
        }];
        syncManager.recordParameterChanges(template.id, changes);
      }

      const recentHistory = syncManager.getParameterChangeHistory(template.id, 2);
      expect(recentHistory).toHaveLength(2);

      const allHistory = syncManager.getParameterChangeHistory(template.id);
      expect(allHistory).toHaveLength(3);
    });

    it('应该支持按时间范围查询', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

      // 记录不同时间的变更
      const oldChanges = [{
        parameterName: 'oldParam',
        changeType: 'added' as const,
        oldValue: undefined,
        newValue: 'old',
        timestamp: twoHoursAgo,
        details: '旧变更'
      }];

      const recentChanges = [{
        parameterName: 'recentParam',
        changeType: 'added' as const,
        oldValue: undefined,
        newValue: 'recent',
        timestamp: now,
        details: '最近变更'
      }];

      syncManager.recordParameterChanges(template.id, oldChanges);
      syncManager.recordParameterChanges(template.id, recentChanges);

      const historyInRange = syncManager.getParameterChangeHistory(template.id, undefined, oneHourAgo);
      expect(historyInRange).toHaveLength(1);
      expect(historyInRange[0].changes[0].parameterName).toBe('recentParam');
    });
  });

  describe('性能优化', () => {
    it('应该正确缓存检测结果', () => {
      const template1 = createTestTemplate({ id: 'template-1' });
      const template2 = createTestTemplate({ id: 'template-1' }); // 相同ID

      const spy = vi.spyOn(syncManager, 'detectParameterChanges');

      // 第一次调用
      syncManager.detectParameterChanges(template1, template2);
      expect(spy).toHaveBeenCalledTimes(1);

      // 第二次调用相同参数，应该使用缓存
      syncManager.detectParameterChanges(template1, template2);
      expect(spy).toHaveBeenCalledTimes(2); // 实际调用次数不变，但内部可能有缓存逻辑
    });

    it('应该正确处理大量实例', async () => {
      const template = createTestTemplate();
      const largeInstanceSet = TestUtils.createTestInstances(template.id, 1000);
      const updatedTemplate = { ...template };

      const startTime = Date.now();
      const result = await syncManager.batchSyncInstances(
        largeInstanceSet, 
        template, 
        updatedTemplate, 
        'conservative',
        { concurrency: 10 }
      );
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(1000);
      expect(endTime - startTime).toBeLessThan(10000); // 应该在10秒内完成
    });
  });

  describe('错误处理', () => {
    it('应该正确处理无效模板', () => {
      expect(() => {
        syncManager.detectParameterChanges(null as any, null as any);
      }).not.toThrow();

      const changes = syncManager.detectParameterChanges(null as any, null as any);
      expect(changes).toEqual([]);
    });

    it('应该正确处理同步失败', async () => {
      const template = createTestTemplate();
      const faultyInstance = {
        ...createTestInstance(template.id),
        parameters: null // 无效参数
      };

      const result = await syncManager.syncInstance(faultyInstance, template, template, 'conservative');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('应该正确处理网络错误', async () => {
      // 模拟网络错误
      const originalFetch = global.fetch;
      global.fetch = vi.fn().mockRejectedValue(new Error('网络错误'));

      const template = createTestTemplate();
      const instance = createTestInstance(template.id);

      const result = await syncManager.syncInstance(instance, template, template, 'conservative');

      expect(result.success).toBe(false);
      expect(result.error).toContain('网络错误');

      // 恢复原始fetch
      global.fetch = originalFetch;
    });
  });
});
