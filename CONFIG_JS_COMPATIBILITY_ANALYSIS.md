# config.js 兼容性分析与集成方案

## 📋 现有config.js结构分析

### 🏗️ 核心结构
```javascript
window.$config = {
  authServer: "...",
  authPage: "...",
  
  // 模板库 - 按场景分组
  templates: {
    1: {
      meshes: { ... },      // 网格模板
      styles: { ... },      // 样式模板  
      interactions: { ... }, // 交互模板
      positions: { ... },   // 位置模板
      cameras: { ... },     // 相机模板
      actions: { ... },     // 动作模板
      labels: { ... },      // 标签模板
      environments: { ... }, // 环境模板
      resetCallbacks: { ... } // 回调模板
    }
  },
  
  // 场景配置
  scenes: {
    1: { ... },
    "1-1": { ... },
    "1-3": { ... }
  }
}
```

### 🔗 引用机制分析

#### 1. $ref 引用
```javascript
// 基础引用
style: {
  $ref: "templates.1.styles.orangeGradient"
}

// 嵌套引用
camera: {
  $ref: "templates.1.cameras.building"
}
```

#### 2. $extend 扩展
```javascript
{
  $ref: "templates.1.labels.floorLabel",
  $extend: {
    meshNames: ["电气楼五层"],
    customNames: {
      电气楼五层: "电气楼五层"
    },
    click: { ... }
  }
}
```

### 📊 模板类型统计

| 模板类型 | 数量 | 示例 |
|----------|------|------|
| meshes | 4个集合 | buildingLevels, gisDoors |
| styles | 3个样式 | orangeGradient, blueGradient, transparent |
| interactions | 3个交互 | basicHover, warningHover, sceneSwitch |
| positions | 8个位置 | buildingPosition, GISPosition |
| cameras | 4个相机 | building, 1-3, 3-1, 3-2 |
| actions | 4个动作 | hoverHighlight, clickRedHighlight |
| labels | 3个标签 | floorLabel, roomLabel, deviceLabel |
| environments | 3个环境 | techStyle, basic, water |
| resetCallbacks | 4个回调 | building, 1-3, 3-1, 3-2 |

## 🎯 集成策略

### 1. 双向兼容性
- **导入**：从config.js导入现有模板到新系统
- **导出**：将新创建的模板导出到config.js格式
- **同步**：保持两个系统的数据同步

### 2. 引用解析
```typescript
class ConfigReferenceResolver {
  // 解析$ref引用
  resolveReference(path: string, config: any): any {
    const parts = path.split('.');
    let current = config;
    for (const part of parts) {
      current = current[part];
      if (!current) throw new Error(`Reference not found: ${path}`);
    }
    return current;
  }
  
  // 应用$extend扩展
  applyExtension(base: any, extension: any): any {
    return { ...base, ...extension };
  }
  
  // 检测循环引用
  detectCircularReferences(config: any): string[] {
    // 实现循环引用检测算法
  }
}
```

### 3. 模板映射
```typescript
interface ConfigTemplateMapping {
  configPath: string;        // "templates.1.styles.orangeGradient"
  templateId: string;        // 内部模板ID
  templateType: TemplateType; // STYLE, MESH, CAMERA等
  lastSyncAt: Date;          // 最后同步时间
  isDirty: boolean;          // 是否有未同步的更改
}
```

## 🔄 导入导出流程

### 导入流程
1. **解析config.js**：读取并解析配置文件
2. **提取模板**：从templates节点提取各类模板
3. **解析引用**：处理$ref引用关系
4. **创建模板**：在新系统中创建对应模板
5. **建立映射**：记录config路径与模板ID的映射关系

### 导出流程
1. **收集模板**：获取所有需要导出的模板
2. **生成路径**：为新模板生成config.js路径
3. **转换格式**：将模板转换为config.js格式
4. **处理引用**：生成$ref引用
5. **合并配置**：与现有config.js合并

## 🛠️ 实现细节

### 1. 模板类型映射
```typescript
const CONFIG_TYPE_MAPPING = {
  'styles': TemplateType.STYLE,
  'meshes': TemplateType.MESH,
  'cameras': TemplateType.CAMERA,
  'actions': TemplateType.ACTION,
  'labels': TemplateType.LABEL,
  'positions': TemplateType.POSITION,
  'interactions': TemplateType.INTERACTION,
  'environments': TemplateType.ENVIRONMENT
};
```

### 2. 路径生成规则
```typescript
function generateConfigPath(template: Template): string {
  const sceneId = getCurrentSceneId(); // 获取当前场景ID
  const typeKey = getConfigTypeKey(template.type);
  const templateKey = sanitizeKey(template.name);
  return `templates.${sceneId}.${typeKey}.${templateKey}`;
}
```

### 3. 引用更新策略
```typescript
class ReferenceUpdater {
  updateReferences(oldPath: string, newPath: string, config: any): void {
    // 递归更新所有引用该路径的地方
  }
  
  validateReferences(config: any): ValidationResult {
    // 验证所有引用的有效性
  }
}
```

## 🔍 兼容性检查

### 必须支持的特性
- ✅ $ref 基础引用
- ✅ $extend 扩展语法
- ✅ 嵌套对象引用
- ✅ 数组引用
- ✅ 跨场景引用

### 扩展特性
- 🔄 循环引用检测
- 🔄 引用链追踪
- 🔄 批量引用更新
- 🔄 引用有效性验证

## 📝 迁移计划

### 阶段1：基础兼容
1. 实现config.js解析器
2. 实现$ref引用解析
3. 实现基础模板导入

### 阶段2：双向同步
1. 实现模板导出到config.js
2. 实现引用关系维护
3. 实现增量同步

### 阶段3：高级特性
1. 实现$extend扩展支持
2. 实现循环引用检测
3. 实现批量操作

## 🧪 测试用例

### 基础功能测试
```javascript
// 测试$ref引用解析
const testRef = {
  style: { $ref: "templates.1.styles.orangeGradient" }
};

// 测试$extend扩展
const testExtend = {
  $ref: "templates.1.labels.floorLabel",
  $extend: {
    meshNames: ["test"],
    fontSize: 16
  }
};
```

### 边界情况测试
- 不存在的引用路径
- 循环引用检测
- 深层嵌套引用
- 跨场景引用

## 🎯 成功标准

### 功能完整性
- ✅ 100%兼容现有config.js格式
- ✅ 支持所有现有的$ref引用
- ✅ 支持所有现有的$extend扩展
- ✅ 新模板可导出为config.js格式

### 性能要求
- ✅ 导入1000+模板 <5秒
- ✅ 引用解析 <100ms
- ✅ 导出配置文件 <2秒

### 数据完整性
- ✅ 导入导出无数据丢失
- ✅ 引用关系保持完整
- ✅ 支持版本回滚

---

**关键要点**：新的组件化模板系统必须与现有的config.js完全兼容，用户可以无缝地在两个系统之间切换和同步数据。
