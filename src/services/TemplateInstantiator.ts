/**
 * TemplateInstantiator.ts
 * 模板实例化引擎 - 负责从模板创建节点实例
 */

import { Cell } from '@antv/x6';
import { Template, TemplateInstance, TemplateParameter } from './TemplateManager';

/**
 * 实例化选项
 */
export interface InstantiationOptions {
  // 参数覆盖
  parameterOverrides?: Record<string, any>;
  
  // 位置信息
  position?: { x: number; y: number };
  
  // 大小信息
  size?: { width: number; height: number };
  
  // 场景ID
  sceneId?: string;
  
  // 节点ID（如果不提供则自动生成）
  nodeId?: string;
  
  // 是否应用默认样式
  applyDefaultStyles?: boolean;
  
  // 依赖解析选项
  resolveDependencies?: boolean;
}

/**
 * 实例化结果
 */
export interface InstantiationResult {
  success: boolean;
  node?: Cell;
  instance?: TemplateInstance;
  errors?: string[];
  warnings?: string[];
}

/**
 * 模板实例化引擎
 */
export class TemplateInstantiator {
  private static instance: TemplateInstantiator;
  
  // 参数解析器映射
  private parameterResolvers: Map<string, (value: any, context: any) => any> = new Map();
  
  // 依赖解析器
  private dependencyResolver?: (path: string) => any;

  private constructor() {
    this.initializeParameterResolvers();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): TemplateInstantiator {
    if (!TemplateInstantiator.instance) {
      TemplateInstantiator.instance = new TemplateInstantiator();
    }
    return TemplateInstantiator.instance;
  }

  /**
   * 从模板创建节点实例
   */
  instantiateTemplate(
    template: Template, 
    options: InstantiationOptions = {}
  ): InstantiationResult {
    try {
      console.log(`🏗️ 开始实例化模板: ${template.name}`);
      
      // 验证模板
      const validation = this.validateTemplate(template);
      if (!validation.valid) {
        return {
          success: false,
          errors: validation.errors
        };
      }

      // 生成节点ID
      const nodeId = options.nodeId || this.generateNodeId(template);
      
      // 解析参数
      const resolvedData = this.resolveParameters(
        template.data,
        template.definition?.parameters || [],
        options.parameterOverrides || {}
      );

      // 解析依赖
      if (options.resolveDependencies && template.metadata.dependencies.length > 0) {
        this.resolveDependencies(resolvedData, template.metadata.dependencies);
      }

      // 创建节点
      const node = this.createNode(template, resolvedData, options);
      
      // 创建实例记录
      const instance = this.createInstanceRecord(template, nodeId, options, resolvedData);

      console.log(`✅ 模板实例化成功: ${template.name} -> ${nodeId}`);
      
      return {
        success: true,
        node,
        instance,
        warnings: validation.warnings
      };

    } catch (error) {
      console.error(`❌ 模板实例化失败: ${template.name}`, error);
      return {
        success: false,
        errors: [`实例化失败: ${error}`]
      };
    }
  }

  /**
   * 批量实例化模板
   */
  instantiateMultiple(
    templates: Template[],
    options: InstantiationOptions[] = []
  ): InstantiationResult[] {
    console.log(`🏗️ 批量实例化 ${templates.length} 个模板`);
    
    const results: InstantiationResult[] = [];
    
    templates.forEach((template, index) => {
      const templateOptions = options[index] || {};
      const result = this.instantiateTemplate(template, templateOptions);
      results.push(result);
    });

    const successCount = results.filter(r => r.success).length;
    console.log(`✅ 批量实例化完成: ${successCount}/${templates.length} 成功`);
    
    return results;
  }

  /**
   * 验证模板
   */
  private validateTemplate(template: Template): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查基本属性
    if (!template.id) errors.push('模板ID不能为空');
    if (!template.name) errors.push('模板名称不能为空');
    if (!template.type) errors.push('模板类型不能为空');
    if (!template.data) errors.push('模板数据不能为空');

    // 检查数据完整性
    if (template.data && typeof template.data === 'object') {
      try {
        JSON.stringify(template.data);
      } catch (error) {
        errors.push('模板数据不能序列化');
      }
    }

    // 检查参数定义
    if (template.definition?.parameters) {
      template.definition.parameters.forEach((param, index) => {
        if (!param.name) errors.push(`参数 ${index} 缺少名称`);
        if (!param.type) errors.push(`参数 ${param.name} 缺少类型`);
      });
    }

    // 检查依赖
    if (template.metadata.dependencies.length > 0 && !this.dependencyResolver) {
      warnings.push('模板包含依赖但未设置依赖解析器');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 解析参数
   */
  private resolveParameters(
    templateData: any,
    parameters: TemplateParameter[],
    overrides: Record<string, any>
  ): any {
    console.log('🔧 解析模板参数...');
    
    // 深拷贝模板数据
    let resolvedData = JSON.parse(JSON.stringify(templateData));
    
    // 应用参数覆盖
    parameters.forEach(param => {
      const overrideValue = overrides[param.name];
      
      if (overrideValue !== undefined) {
        // 使用覆盖值
        this.setNestedProperty(resolvedData, param.name, overrideValue);
        console.log(`  📝 应用参数覆盖: ${param.name} = ${overrideValue}`);
      } else if (param.required && param.defaultValue === undefined) {
        console.warn(`⚠️ 必需参数 ${param.name} 未提供值`);
      }
    });

    // 应用参数解析器
    resolvedData = this.applyParameterResolvers(resolvedData, { parameters, overrides });

    return resolvedData;
  }

  /**
   * 解析依赖
   */
  private resolveDependencies(data: any, dependencies: string[]): void {
    if (!this.dependencyResolver) {
      console.warn('⚠️ 未设置依赖解析器，跳过依赖解析');
      return;
    }

    console.log(`🔗 解析 ${dependencies.length} 个依赖...`);
    
    dependencies.forEach(dep => {
      try {
        const resolvedValue = this.dependencyResolver!(dep);
        console.log(`  ✅ 解析依赖: ${dep}`);
        
        // 在数据中查找并替换引用
        this.replaceReferences(data, dep, resolvedValue);
      } catch (error) {
        console.error(`  ❌ 依赖解析失败: ${dep}`, error);
      }
    });
  }

  /**
   * 创建节点
   */
  private createNode(
    template: Template,
    resolvedData: any,
    options: InstantiationOptions
  ): Cell {
    console.log('🎨 创建节点实例...');
    
    // 确定节点形状
    const shape = this.determineNodeShape(template, resolvedData);
    
    // 构建节点配置
    const nodeConfig: any = {
      id: options.nodeId || this.generateNodeId(template),
      shape,
      data: resolvedData,
      position: options.position || { x: 0, y: 0 },
      size: options.size || this.getDefaultSize(template),
    };

    // 应用样式
    if (options.applyDefaultStyles !== false) {
      nodeConfig.attrs = this.generateNodeAttrs(template, resolvedData);
    }

    // 创建模拟节点（在实际应用中这里会创建真正的X6节点）
    const mockNode = this.createMockNode(nodeConfig);
    
    console.log(`  ✅ 节点创建完成: ${nodeConfig.id}`);
    return mockNode;
  }

  /**
   * 创建实例记录
   */
  private createInstanceRecord(
    template: Template,
    nodeId: string,
    options: InstantiationOptions,
    resolvedData: any
  ): TemplateInstance {
    return {
      id: `${template.id}_${nodeId}_${Date.now()}`,
      templateId: template.id,
      templateVersion: template.version,
      nodeId,
      sceneId: options.sceneId || 'default',
      overrides: options.parameterOverrides || {},
      syncStatus: 'synced',
      lastSyncAt: new Date(),
      configRef: template.definition?.configPath ? {
        path: template.definition.configPath,
        isReference: true
      } : undefined
    };
  }

  /**
   * 初始化参数解析器
   */
  private initializeParameterResolvers(): void {
    // 颜色解析器
    this.parameterResolvers.set('color', (value: any) => {
      if (typeof value === 'string' && value.startsWith('#')) {
        return value;
      }
      if (Array.isArray(value) && value.length >= 3) {
        return `rgb(${value[0]}, ${value[1]}, ${value[2]})`;
      }
      return value;
    });

    // 位置解析器
    this.parameterResolvers.set('position', (value: any) => {
      if (Array.isArray(value) && value.length >= 2) {
        return { x: value[0], y: value[1] };
      }
      return value;
    });

    // 大小解析器
    this.parameterResolvers.set('size', (value: any) => {
      if (Array.isArray(value) && value.length >= 2) {
        return { width: value[0], height: value[1] };
      }
      return value;
    });
  }

  /**
   * 应用参数解析器
   */
  private applyParameterResolvers(data: any, context: any): any {
    // 递归处理对象
    if (data && typeof data === 'object' && !Array.isArray(data)) {
      const result: any = {};
      for (const key in data) {
        const resolver = this.parameterResolvers.get(key);
        if (resolver) {
          result[key] = resolver(data[key], context);
        } else {
          result[key] = this.applyParameterResolvers(data[key], context);
        }
      }
      return result;
    }
    
    return data;
  }

  /**
   * 设置嵌套属性
   */
  private setNestedProperty(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * 替换引用
   */
  private replaceReferences(obj: any, refPath: string, resolvedValue: any): void {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (obj[key] && typeof obj[key] === 'object' && obj[key].$ref === refPath) {
          obj[key] = resolvedValue;
        } else if (typeof obj[key] === 'object') {
          this.replaceReferences(obj[key], refPath, resolvedValue);
        }
      }
    }
  }

  /**
   * 确定节点形状
   */
  private determineNodeShape(template: Template, data: any): string {
    // 根据模板类型和数据确定形状
    if (data.shape) return data.shape;
    
    switch (template.type) {
      case 'mesh': return 'rect';
      case 'style': return 'rect';
      case 'label': return 'text';
      case 'action': return 'circle';
      default: return 'rect';
    }
  }

  /**
   * 获取默认大小
   */
  private getDefaultSize(template: Template): { width: number; height: number } {
    switch (template.type) {
      case 'label': return { width: 100, height: 30 };
      case 'action': return { width: 60, height: 60 };
      default: return { width: 120, height: 40 };
    }
  }

  /**
   * 生成节点属性
   */
  private generateNodeAttrs(template: Template, data: any): any {
    const attrs: any = {};
    
    // 基础样式
    if (data.backgroundColor) {
      attrs.body = { ...attrs.body, fill: data.backgroundColor };
    }
    
    if (data.borderColor) {
      attrs.body = { ...attrs.body, stroke: data.borderColor };
    }
    
    if (data.textColor) {
      attrs.text = { ...attrs.text, fill: data.textColor };
    }
    
    if (data.fontSize) {
      attrs.text = { ...attrs.text, fontSize: data.fontSize };
    }
    
    return attrs;
  }

  /**
   * 生成节点ID
   */
  private generateNodeId(template: Template): string {
    return `${template.type}_${template.name.replace(/\s+/g, '_')}_${Date.now()}`;
  }

  /**
   * 创建模拟节点
   */
  private createMockNode(config: any): Cell {
    return {
      ...config,
      getData: () => config.data,
      getAttrs: () => config.attrs || {},
      getSize: () => config.size,
      getPosition: () => config.position,
      setData: (data: any) => { config.data = { ...config.data, ...data }; },
      setAttrs: (attrs: any) => { config.attrs = { ...config.attrs, ...attrs }; },
      setSize: (size: any) => { config.size = size; },
      setPosition: (position: any) => { config.position = position; }
    } as Cell;
  }

  /**
   * 设置依赖解析器
   */
  setDependencyResolver(resolver: (path: string) => any): void {
    this.dependencyResolver = resolver;
    console.log('✅ 依赖解析器已设置');
  }

  /**
   * 添加参数解析器
   */
  addParameterResolver(name: string, resolver: (value: any, context: any) => any): void {
    this.parameterResolvers.set(name, resolver);
    console.log(`✅ 参数解析器已添加: ${name}`);
  }
}

// 导出单例实例
export const templateInstantiator = TemplateInstantiator.getInstance();
