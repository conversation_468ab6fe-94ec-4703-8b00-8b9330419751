/**
 * ConfigExporter.ts
 * 负责将模板导出为config.js格式
 */

import { Template, TemplateType, TemplateInstance } from './TemplateManager';

/**
 * 模板类型到配置类型的映射
 */
const TYPE_CONFIG_MAPPING: Record<TemplateType, string> = {
  [TemplateType.STYLE]: 'styles',
  [TemplateType.MESH]: 'meshes',
  [TemplateType.CAMERA]: 'cameras',
  [TemplateType.ACTION]: 'actions',
  [TemplateType.LABEL]: 'labels',
  [TemplateType.POSITION]: 'positions',
  [TemplateType.INTERACTION]: 'interactions',
  [TemplateType.ENVIRONMENT]: 'environments',
  [TemplateType.CALLBACK]: 'resetCallbacks'
};

/**
 * 配置导出器类
 */
export class ConfigExporter {
  /**
   * 导出模板到config.js格式
   * @param templates 模板数组
   * @param existingConfig 现有配置（可选）
   * @returns 导出的配置对象
   */
  public exportTemplates(templates: Template[], existingConfig: any = {}): any {
    // 创建配置对象
    const config = this.cloneConfig(existingConfig);
    
    // 确保templates节点存在
    if (!config.templates) {
      config.templates = {};
    }
    
    // 按场景和类型组织模板
    const templatesByScene = this.organizeTemplatesByScene(templates);
    
    // 将模板添加到配置中
    for (const sceneId in templatesByScene) {
      if (!config.templates[sceneId]) {
        config.templates[sceneId] = {};
      }
      
      const sceneTemplates = templatesByScene[sceneId];
      
      for (const typeKey in sceneTemplates) {
        if (!config.templates[sceneId][typeKey]) {
          config.templates[sceneId][typeKey] = {};
        }
        
        const typeTemplates = sceneTemplates[typeKey];
        
        for (const templateName in typeTemplates) {
          config.templates[sceneId][typeKey][templateName] = typeTemplates[templateName];
        }
      }
    }
    
    return config;
  }
  
  /**
   * 导出模板实例到config.js格式
   * @param instances 模板实例数组
   * @param existingConfig 现有配置（可选）
   * @returns 导出的配置对象
   */
  public exportInstances(instances: TemplateInstance[], existingConfig: any = {}): any {
    // 创建配置对象
    const config = this.cloneConfig(existingConfig);
    
    // 确保scenes节点存在
    if (!config.scenes) {
      config.scenes = {};
    }
    
    // 按场景组织实例
    const instancesByScene = this.organizeInstancesByScene(instances);
    
    // 将实例添加到配置中
    for (const sceneId in instancesByScene) {
      if (!config.scenes[sceneId]) {
        config.scenes[sceneId] = {};
      }
      
      const sceneInstances = instancesByScene[sceneId];
      
      for (const nodeId in sceneInstances) {
        config.scenes[sceneId][nodeId] = sceneInstances[nodeId];
      }
    }
    
    return config;
  }
  
  /**
   * 生成config.js文件内容
   * @param config 配置对象
   * @returns config.js文件内容
   */
  public generateConfigFile(config: any): string {
    return `window.$config = ${JSON.stringify(config, null, 2)};`;
  }
  
  /**
   * 按场景和类型组织模板
   */
  private organizeTemplatesByScene(templates: Template[]): Record<string, Record<string, Record<string, any>>> {
    const result: Record<string, Record<string, Record<string, any>>> = {};
    
    for (const template of templates) {
      // 解析路径获取场景ID和类型
      const pathInfo = this.parseConfigPath(template.path);
      
      if (!pathInfo) {
        console.warn(`无法解析模板路径: ${template.path}`);
        continue;
      }
      
      const { sceneId, typeKey, templateName } = pathInfo;
      
      // 确保节点存在
      if (!result[sceneId]) {
        result[sceneId] = {};
      }
      
      if (!result[sceneId][typeKey]) {
        result[sceneId][typeKey] = {};
      }
      
      // 添加模板数据
      result[sceneId][typeKey][templateName] = template.data;
    }
    
    return result;
  }
  
  /**
   * 按场景组织实例
   */
  private organizeInstancesByScene(instances: TemplateInstance[]): Record<string, Record<string, any>> {
    const result: Record<string, Record<string, any>> = {};
    
    for (const instance of instances) {
      const sceneId = instance.sceneId;
      const nodeId = instance.nodeId;
      
      // 确保场景节点存在
      if (!result[sceneId]) {
        result[sceneId] = {};
      }
      
      // 添加实例数据
      if (instance.configRef && instance.configRef.isReference) {
        // 使用$ref引用
        result[sceneId][nodeId] = {
          $ref: instance.configRef.path
        };
        
        // 如果有覆盖参数，添加$extend
        if (instance.overrides && Object.keys(instance.overrides).length > 0) {
          result[sceneId][nodeId].$extend = instance.overrides;
        }
      } else {
        // 直接使用数据
        result[sceneId][nodeId] = instance.overrides;
      }
    }
    
    return result;
  }
  
  /**
   * 解析配置路径
   * @param path 配置路径，如 "templates.1.styles.orangeGradient"
   * @returns 解析结果
   */
  private parseConfigPath(path: string): { sceneId: string; typeKey: string; templateName: string } | null {
    // 检查路径格式
    if (!path || !path.startsWith('templates.')) {
      return null;
    }
    
    // 分割路径
    const parts = path.split('.');
    
    // 检查路径长度
    if (parts.length !== 4) {
      return null;
    }
    
    return {
      sceneId: parts[1],
      typeKey: parts[2],
      templateName: parts[3]
    };
  }
  
  /**
   * 生成配置路径
   * @param template 模板
   * @param sceneId 场景ID
   * @returns 配置路径
   */
  public generateConfigPath(template: Template, sceneId: string): string {
    const typeKey = TYPE_CONFIG_MAPPING[template.type];
    const templateName = this.sanitizeKey(template.name);
    return `templates.${sceneId}.${typeKey}.${templateName}`;
  }
  
  /**
   * 净化键名
   * @param key 原始键名
   * @returns 净化后的键名
   */
  private sanitizeKey(key: string): string {
    // 移除特殊字符，替换空格为下划线
    return key
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, '_')
      .toLowerCase();
  }
  
  /**
   * 克隆配置对象
   */
  private cloneConfig(config: any): any {
    return JSON.parse(JSON.stringify(config));
  }
}
